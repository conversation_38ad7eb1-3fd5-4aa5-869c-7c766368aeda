from common.utils.utils import return_code
class ExceptionChange:

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_template_response(self, request, response):
        if hasattr(response, 'data'):
            data = response.data
            # print(type(data), data)
            # print(type(request), response)
            # if ["detail"] == data.keys():
                # del response.data["detail"]
                # response.data["status"] = response.status_code
                # response.data["msg"] = "请先登录系统"
                # response.data = return_code(401)
            # response.data['code'] = int(response.data['code'])
        return response
