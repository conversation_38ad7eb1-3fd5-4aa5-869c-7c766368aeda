from rest_framework import permissions
from rest_framework_jwt.utils import jwt_decode_handler

from .models import MyUserInfo
from common.utils.log import Logger
from common.utils.utils import return_code
set_log = Logger.get_logger(__name__, 0)

class MyUserPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        set_log.info(f"+"*100)
        set_log.info(f"[MyUserPermission] 开始权限验证")
        # token = request.META.get('X-Auth-Token')
        # set_log.debug(f"[token] {token}")
        token = request.headers.get("Authorization")
        if token[6] == ' ':
            token = token[7:]
        set_log.debug(f"[token] {token}")
        # if request.user.is_authenticated:
        try:
            username = jwt_decode_handler(token)
            set_log.debug(f"[username] {username}")

            user = MyUserInfo.objects.filter(email=username['email'], not_send="False")[0]
            # user = User.objects.get(pk=jwt_decode_handler(token)['user_id'])
            set_log.debug(f"[user] {user}")
            if user.is_superuser:
                set_log.info(f"用户{user.user_name} 超级管理员权限")
                return True
            else:
                set_log.info(f"用户{user.user_name} 无权限")
                return False
        except Exception as e:
            set_log.debug(f"[ERROR] {e}")
            set_log.info(f"权限判断模块异常 {e}")
            return False


