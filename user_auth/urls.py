"""fault_sys URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path, include
from django.shortcuts import redirect
from . import views
from rest_framework.routers import DefaultRouter
router = DefaultRouter()
router.register(r'auth', views.MyUserInfoViewset)



urlpatterns = [
    # path('', views.hello),
    # path('', views.MyUserInfoViewset),
    path('', include(router.urls)),
    path('login', views.login_api),
    path('logout', views.logout_api),
    path('getidentity', views.Get_login_identity_api),
    path('info', views.userinfo_api),
    path('test', views.test_api)
]

