import datetime
import json
import time
import jwt

import requests
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import redirect
from common.utils.utils import NewViewBase, return_code
from common.utils.log import Logger
from common.utils.config import path_config
from rest_framework_jwt.utils import jwt_payload_handler, jwt_encode_handler, jwt_decode_handler

from .models import *
from .serializers import *
from .my_permissions import *
from .my_authentication import *
from framework.models import PersonsInitInfo

from rest_framework.decorators import api_view, authentication_classes, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAdminUser
from django.contrib.auth import authenticate, login, logout
# from .authentication import MyTokenAuthentication
# from .permissions import MyUserPermission


User = get_user_model()
conf = path_config()
set_log = Logger.get_logger(__name__, 0)
# Create your views here.



# @login_required
# def dashboard(request):
#     user_data = User.objects.get(pk=request.user.pk)
#     context = {'user': user_data}
#     return render(request, 'dashboard.html', context)

def test_view(request):
    users = User.objects.all()
    return Response(return_code(0, data=users))



@api_view(['POST', 'GET'])
@authentication_classes([])  # 未认证请求也可以访问此 API
@permission_classes([])  # 只有权限满足条件的用户才能访问此 API
def login_api(request):
    try:
        print("*" * 100)
        set_log.info("[LOGIN ING ...]")
        username = request.data.get('username')
        password = request.data.get('password')
        set_log.debug(f"[FRONTEND DATA] username: {username}, password: {password}")
        user = authenticate(request, username=username, password=password)
        set_log.debug(f"{type(user)} {user}")
        if user:
            set_log.info(f"验证成功")
            # 验证成功，登录用户
            login(request, user)
            # 返回用户信息
            serializer = MyUserInfoSerializers(user)

            payload = jwt_payload_handler(user)
            token = jwt_encode_handler(payload)
            set_log.info(f'[token] {token}')
            data = serializer.data
            data['token'] = token
            group = [i.name for i in user.groups.all()]
            set_log.info(f"[USER_GROUP] {group}")
            if len(group) > 0:
                data['role'] = group[0]
            else:
                data['role'] = 'user'
            return Response(return_code(20000, data=data, msg='请求成功', status='ok'))
            # return JsonResponse({"token": token})
        else:
            # 验证失败，返回错误信息
            return Response(return_code(2001))
    except Exception as e:
        set_log.error(f"[登录模块异常] {e}")
        return Response(return_code(-2, data=e))


@api_view(['POST', 'GET'])
@authentication_classes([])  # 未认证请求也可以访问此 API
@permission_classes([])  # 只有权限满足条件的用户才能访问此 API
def logout_api(request):
    try:
        set_log.info("*"*100)
        set_log.info("[LOGOUT ING ...]")
        logout(request)
        set_log.info("[LOGOUT SUCCESS]")
        return Response(return_code(20000, data={}, msg='请求成功', status='ok'))
        # return Response(return_code(0, data={"已成功退出登录"}))
    except Exception as e:
        set_log.error(f"[登出模块异常] {e}")
        return Response(return_code(-2, data=e))


@api_view(['POST', 'GET'])
@authentication_classes([])  # 未认证请求也可以访问此 API
@permission_classes([])  # 只有权限满足条件的用户才能访问此 API
def userinfo_api(request, *args, **kwargs):
    try:
        print("*" * 100)

        # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] Here is userlogin")
        set_log.debug("Here is userinfo")

        data = request.data
        set_log.debug(f"[frontend data] {data}")
        cookie = request.META.get("HTTP_COOKIE")
        set_log.debug(f"[frontend cookie] {cookie}")
        token = request.headers.get("Authorization")
        if token[6] == ' ':
            token = token[7:]
        set_log.debug(f"[frontend token] {token}")

        try:
            # username = data.get('username')
            username = jwt_decode_handler(token)
            set_log.debug(f"[username] {username}")

        except jwt.ExpiredSignatureError as e:
            # ExpiredSignatureError token过期
            set_log.info(f"{e}")
            return Response(return_code(401, data="token过期", msg='请求成功', status='ok'))
        user = MyUserInfo.objects.filter(email=username['username'], not_send="False")[0]
        set_log.debug(f"[user] {user}")
        ret_data = MyUserInfoSerializers(user).data
        groups = [i.name for i in user.groups.all()]
        set_log.info(f"[USER_GROUP] {groups}")
        if "Super" in groups:
            ret_data['roles'] = [{"roleName": '超级管理员', "value": 'Super'}]
        elif "Admin" in groups:
            ret_data['roles'] = [{"roleName": '管理员', "value": 'Admin'}]
        elif "InternalUser" in groups:
            ret_data['roles'] = [{"roleName": '普通用户（内部）', "value": 'InternalUser'}]
        elif "ExternalUser" in groups:
            ret_data['roles'] = [{"roleName": '普通用户（外部）', "value": 'ExternalUser'}]
        else:
            ret_data['roles'] = [{"roleName": '普通用户', "value": 'user'}]
        # payload = jwt_payload_handler(user)
        # token = jwt_encode_handler(payload)
        # set_log.info(f'[token] {token}')
        return Response(return_code(20000, data=ret_data, msg='请求成功', status='ok'))
        # return Response(return_code(0, data=ret_data))
    except Exception as e:
        return Response(return_code(2002, data='未登录', status='error'))

# sso登录验证
@api_view(['POST', 'GET'])
@authentication_classes([])  # 未认证请求也可以访问此 API
@permission_classes([])  # 只有权限满足条件的用户才能访问此 API
def Get_login_identity_api(request, *args, **kwargs):
    # data = {}
    print("*" * 100)
    # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] Here is Get_login_identity")
    set_log.debug("Here is Get_login_identity")
    try:
        frontend_data = request.data
        set_log.debug(f"[frontend data] {frontend_data}")
        code = frontend_data['code']
        # set_log.debug(f"[data] {data}")
        # code = request.GET.get("code")
        # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] code:{code}")
        set_log.debug(f"获取到的code: {code}")
        set_log.debug(f"[client_id] {client_id} [client_secret] {client_secret}")

        r = requests.get(address + "/oauth/access_token?grant_type=authorization_code&client_id="
                         + client_id + "&client_secret=" + client_secret + "&code=" + code)
        oauth = r.json()
        # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] oauth:{oauth}")
        set_log.debug(f"获取到的oauth: {oauth}")

        user_info = requests.get(address + "/oauth/user_info?access_token=" + oauth["access_token"])

        user_info_json = user_info.json()
        # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] user_info_json:{user_info_json}")
        set_log.debug(f"获取到的 user_info: {user_info_json}")


        # user = MyUserInfoSerializers(MyUserInfo.objects.filter(email=user_info_json['email'])[0])
        # ret_data = user.data
        # payload = jwt_payload_handler(user)
        # token = jwt_encode_handler(payload)
        # set_log.info(f'[token] {token}')
        # ret_data['token'] = token

        data = MyUserInfo.objects.filter(email=user_info_json['email'], not_send="False")
        set_log.debug(f"[userinfo] {data}")
        now_time_count = datetime.datetime.now()
        now_time = now_time_count.strftime("%Y-%m-%d %H:%M:%S")
        # 获取新用户飞书id
        # url = "http://yw-jarvis.ttyuyin.com/api_V1/query/lark_id/"
        # headers = {
        #     'Authorization': 'EB3xwAfrZwz.262VbIioi755+oYP#4t39RF_0+NR3BWzeGM0Cc7HvWDeT_50f5i+tzvu9ZU$qXaj8LoV0gV2JhHQ9WmRTK9LnKuu#Q4ZYbHm7krnyCmrV&fIv2ScElTz',
        #     'Content-Type': 'application/json'
        # }
        # data = {"mails": [user_info_json['email']], "id_type": "open_id"}
        # set_log.debug(f"[post data] {data}")
        # res = requests.post(url, data=json.dumps(data), headers=headers)
        # set_log.debug(f"[res] {res.json()}")
        # user_id = res.json()['lark_id_list'][0][user_info_json['email']]
        # set_log.debug(f"[user_id] {user_id}")
        user_id = get_lark_id([user_info_json['email']])
        # 用户存在刷新登录时间
        if data:
            set_log.debug(f"用户已存在")
            # 1
            # user_login = authenticate(request, username=user_info_json['email'], password="123456")
            # set_log.info(f"验证成功")
            # # 验证成功，登录用户
            # login(request, user_login)

            # 2
            userinfo = data[0]
            # 返回体
            # user = MyUserInfoSerializers(MyUserInfo.objects.filter(email=user_info_json['email'])[0])
            # ret_data = MyUserInfoSerializers(userinfo).data
            payload = jwt_payload_handler(userinfo)
            token = jwt_encode_handler(payload)
            # set_log.info(f'[token] {token}')
            # ret_data['token'] = token

            # 刷新登录时间
            userinfo.last_login = now_time
            # userinfo.statu = '已登录'
            if user_id != userinfo.user_id:
                set_log.debug(f"[change user_id] [old id: {userinfo.user_id} [new id: {user_id}]]")
                userinfo.user_id = user_id
            userinfo.save()
        else:
            # 新建用户
            set_log.debug(f"用户不存在，正在新建用户")



            MyUserInfo.objects.create_user(email=user_info_json['email'], password="123456",
                                           user_name=user_info_json['fullName'], TT_id=user_info_json['account'],
                                           user_id=user_id, deptname=user_info_json['fullDeptName'], last_login=now_time)
            user = MyUserInfo.objects.get(email=user_info_json['email'])
            # ret_data = MyUserInfoSerializers(user).data
            # set_log.debug(f"[user] {user}")
            # user = user.data
            # user['username'] = user['user_name']
            # user.username = user.user_name
            payload = jwt_payload_handler(user)
            token = jwt_encode_handler(payload)
            # set_log.info(f'[token] {token}')
            # ret_data['token'] = token
            # 新用户所属组
            # if "值班运维组" in user_info_json['fullDeptName']:
            #     #
            #     set_log.info(f"{user.user_name} 角色为 Admin 管理员")
            #     group = Group.objects.get(name='Admin')
            #     user.groups.add(group)
            # el
            set_log.debug(f"[account] {user_info_json['account']} {type(user_info_json['account'])}")
            if user_info_json['account'][0] == "T":
                set_log.info(f"{user.user_name} 角色为 InternalUser 普通用户（内部）")
                group = Group.objects.get(name='InternalUser')
                user.groups.add(group)
            elif user_info_json['account'][0] != "T":
                set_log.info(f"{user.user_name} 角色为 ExternalUser 普通用户（外部）")
                group = Group.objects.get(name='ExternalUser')
                user.groups.add(group)
        set_log.info(f"登录成功")
    except Exception as e:
        # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] error:{f}")
        set_log.info(f"登录失败")
        set_log.error(f"time:{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())} {e}")
        return Response(return_code(-2, data=e))
    return Response(return_code(20000, data={'token': token}, status='ok'))


@api_view(['POST', 'GET'])
@authentication_classes([MyTokenAuthentication])
@permission_classes([MyUserPermission])  # 只有权限满足条件的用户才能访问此 API
def test_api(request, *args, **kwargs):
    print("*" * 100)
    # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] Here is userlogin")
    set_log.debug("Here is test")

    data = request.data
    set_log.debug(f"[frontend data] {data}")
    cookie = request.META.get("HTTP_COOKIE")
    set_log.debug(f"[frontend cookie] {cookie}")
    token = request.headers.get("Authorization")
    if token[6] == ' ':
        token = token[7:]
    set_log.debug(f"[frontend token] {token}")

    try:
        # username = data.get('username')
        username = jwt_decode_handler(token)
        set_log.debug(f"[username] {username}")

    except jwt.ExpiredSignatureError as e:
        # ExpiredSignatureError token过期
        set_log.info(f"{e}")
        return Response(return_code(401, data="token过期", msg='请求成功', status='ok'))
    user = MyUserInfo.objects.filter(email=username['email'], not_send="False")[0]
    set_log.debug(f"[user] {user}")
    ret_data = MyUserInfoSerializers(user).data
    groups = [i.name for i in user.groups.all()]
    set_log.info(f"[USER_GROUP] {groups}")
    group = user.groups.all()[0]
    # set_log.debug(f"group: {group}")
    # set_log.debug(f"json group: {json.loads(str(group))}")
    # set_log.debug(f"group.name: {group.name}")
    # set_log.debug(f"group.pk: {group.pk}")
    # temp = MyUserInfo.objects.filter(is_active=1).values("user_name")
    # set_log.debug(f"{temp} {type(temp)}")
    # set_log.debug(f"{[i for i in temp]}")
    # set_log.debug(f"{[i['user_name'] for i in temp]}")
    return Response(return_code(20000, data=groups, msg='请求成功', status='ok'))
    # return Response(return_code(0, data=ret_data))

def get_lark_id(mails_list):
    #   调用
    # 通过邮箱获取用户id
    from common.robot.get_tenant_access_token import get_access_token

    # id_type = request.data.get("id_type", "user_id")
    id_type = "open_id"
    # mails_list = request.data.get("mails")

    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + get_access_token()
    }
    url = "https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id?user_id_type=" + id_type
    payload = json.dumps({
        "emails": mails_list
    })
    response = requests.post(url, headers=headers, data=payload).json()
    set_log.debug(f"[user_list response] {response}")
    code = response["code"]
    if code != 0:
        set_log.error(f"[GET_LARK_ID] ERROR: code = {code}, msg = {response.get('msg', '')}")
        return Response({
            "header": {
                "code": str(code),
                "msg": str(response.get('msg', ''))
            },
            "lark_id_list": ""
        }, status=200)
    user_list = response['data']["user_list"]
    set_log.info(f"[get id success] {user_list}")
    return user_list[0].get('user_id', "not found")


client_id = conf['client_id']
client_secret = conf['client_secret']
redirect_uri = conf['redirect_uri']
address = conf['address']

class MyUserInfoViewset(NewViewBase):
    """信息增删改查"""
    queryset = MyUserInfo.objects.all()
    serializer_class = MyUserInfoSerializers
    permission_classes = [AllowAny]
    authentication_classes = []

    @action(methods=['get', 'post'], detail=False, url_path='userlist')
    def userlist(self, request, *args, **kwargs):
        print("*" * 100)
        # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] Here is userlogin")
        set_log.debug("Here is userlist")

        frontend_data = request.data
        set_log.debug(f"[frontend data] {frontend_data}")
        # ret_data = MyUserInfoSerializers(MyUserInfo.objects.all(), many=True).data
        ret_data = MyUserInfoSerializers(MyUserInfo.objects.filter(user_id__icontains="ou_", not_send="False"), many=True).data

        return Response(return_code(20000, data=ret_data, msg="请求成功"))

    # @action(methods=['get', 'post'], detail=False, url_path='userlist')
    # def userlist(self, request, *args, **kwargs):
    #     print("*" * 100)
    #     # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] Here is userlogin")
    #     set_log.debug("Here is userlist")

    #     frontend_data = request.data
    #     set_log.debug(f"[frontend data] {frontend_data}")
    #     # ret_data = MyUserInfoSerializers(MyUserInfo.objects.all(), many=True).data
    #     ret_data = MyUserInfoSerializers(MyUserInfo.objects.filter(user_id__icontains="ou_"), many=True).data

        return Response(return_code(20000, data=ret_data, msg="请求成功"))

    @action(methods=['get', 'post'], detail=False, url_path='createuser')
    def create_user(self, request, *args, **kwargs):
        print("*" * 100)
        # print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}] Here is userlogin")
        set_log.debug("Here is create_user")

        frontend_data = request.data
        set_log.debug(f"[frontend data] {frontend_data}")
        username = frontend_data.get('username', False)
        email = frontend_data.get('email', False)
        if not username or not email:
            return Response(return_code(1009, "用户名或邮箱为空"))
        password = frontend_data.get('password', '123456')
        TT_id = ''
        user_id = ''
        try:
            MyUserInfo.objects.create_user(email=email, user_name=username, password=password)
        except Exception as e:
            set_log.info(f"账号创建失败 - 账号已存在")
            return Response(return_code(-1, data='账号已存在'))
        user = MyUserInfo.objects.filter(user_name=username, not_send="False")[0]
        set_log.info(f"[USER] {user}")

        ret_data = MyUserInfoSerializers(user).data
        group = user.groups.all()
        set_log.info(f"[USER_GROUP] {group}")
        ret_data['role'] = group[0]

        return Response(return_code(0, data=ret_data))

    @action(methods=['get', 'post'], detail=False, url_path='test')
    def test(self, request, *args, **kwargs):
        print("*" * 100)
        user_info = MyUserInfo.objects.all()
        for i in user_info:
            try:
                temp = PersonsInitInfo.objects.get(email=i.email)
                i.user_id = temp.user_id
                i.save()
                set_log.info(f"{i.user_name} id 更新成功")
            except Exception as e:
                set_log.error(f"{i.user_name} id 更新失败 {e}")
        set_log.info(f"all id 更新成功")
        return Response(return_code(0, data="success"))
        

        

    @action(methods=['get', 'post'], detail=False, url_path='createuserbyhand')
    def create_user_by_hand(self, request, *args, **kwargs):
        print("*" * 100)
        set_log.debug("Here is creat_user_by_hand")
        # import hashlib
        # app_secret = 'mwyxmjfizgytnmzlnc00odq5ltk4nzetotbkmwmwnze1nzmx'

        # def get_md5_value(value):
        #     my_md5 = hashlib.md5()  # 获取一个MD5的加密算法对象
        #     my_md5.update(value.encode("utf8"))  # 得到MD5消息摘要
        #     my_md5_Digest = my_md5.hexdigest()  # 以16进制返回消息摘要，32位
        #     return my_md5_Digest

        # def sign(pass_id, timestamp, nonce):
        #     params = {"x-tt-paasid": pass_id,
        #                 "x-tt-timestamp": str(timestamp),
        #                 "x-tt-nonce": nonce}
        #     temp_string = ""
        #     for k in sorted(params):
        #         temp_string = temp_string + k + params[k]
        #     temp_string = temp_string + app_secret
        #     md5_value = get_md5_value(temp_string)
        #     return md5_value

        # def test():
        #     test_result = sign("fip-backend", now, "Abcd2222")
        #     return test_result

        # now = str(time.time()).split('.')[0]
        # print(str(now), type(now))
        # headers = {
        #     'x-tt-paasid': 'fip-backend',
        #     'x-tt-signature': test(),
        #     'x-tt-timestamp': now,
        #     'x-tt-nonce': 'Abcd2222'
        # }
        # page = request.data.get("page", False)
        # if not page:
        #     return Response(return_code(-1, data="无分页"))

        # url = f"https://test-union-b-gateway.52tt.com/ebc-api/api-user/restApi/user/query?status=ENABLED&pageSize=200&pageNo={page}"
        # s = requests.get(url=url, headers=headers)
        # print("s的值" + s.text)
        # print(s.json())
        # data = s.json()['result']['data']
        # # 批量创建用户

        # for i in data:
        #     username = i.get('fullName', False)
        #     TTid = i.get('account', False)
        #     email = i.get('email', False)
        #     deptname = i.get('fullDeptName', False)
        #     now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        #     user_id = get_lark_id([email])
        #     set_log.debug(f"[user_id] {user_id}")
        #     # 创建信息
        #     try:

        #         personsinit = PersonsInitInfo(user_name=username, user_id=user_id, email=email)
        #         personsinit.save()
        #     except Exception as e:
        #         set_log.error(f"{e}")
        #         set_log.info(f"信息录入失败 - {username} {TTid} {user_id}已存在")
        #         continue


        set_log.info(f"createuserbyhand success")
        return Response(return_code(0, data="success"))







# from django.contrib.auth.decorators import login_required, permission_required
# from django.contrib.auth.mixins import PermissionRequiredMixin
# from django.core.exceptions import PermissionDenied
#
# @login_required
# def profile(request):
#     user = request.user
#     context = {'user': user}
#
# @permission_required('app_name.add_post', raise_exception=True)
# def create_post(request):
#     # check if user can create blog post
#     if not request.user.has_perm('app_name.add_post'):
#         raise PermissionDenied
#
#     # create post
#     # ...
#
#     return render(request, 'create_post.html')
#
# class CreatePostView(PermissionRequiredMixin, View):
#     permission_required = 'app_name.add_post'

    # def get(self, request):
        # create post
        # ...

