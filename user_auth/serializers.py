from rest_framework import serializers
from .models import *


class MyUserInfoSerializers(serializers.ModelSerializer):
    """用户信息反馈序列化"""

    class Meta:
        model = MyUserInfo
        # fileds = '__all__'
        fields = ('user_name', 'email', 'user_id', 'TT_id', 'deptname')

class MyGroupInfoSerializers(serializers.ModelSerializer):
    """用户信息反馈序列化"""

    class Meta:
        model = MyGroupInfo
        fileds = '__all__'
        # fields = ('user_name', 'email', 'user_id', 'TT_id', 'deptname')
