import json
from .models import MyUserInfo
from django.contrib.auth.models import User
from rest_framework import authentication
from rest_framework import exceptions
from common.utils.log import Logger
from common.utils.utils import return_code
from rest_framework_jwt.utils import jwt_decode_handler


set_log = Logger.get_logger(__name__, 0)

class MyTokenAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        set_log.info(f"+"*100)
        set_log.info(f"[MyTokenAuthentication] 开始Token验证")
        # token = request.META.get('X-Auth-Token')
        # set_log.debug(f"[token] {token}")
        token = request.headers.get("Authorization")
        if token[6] == ' ':
            token = token[7:]
        set_log.debug(f"[token] {token}")
        if not token:
            set_log.info(f"EMPTY TOKEN")
            # return None
            raise exceptions.AuthenticationFailed(return_code(2003))
        try:
            username = jwt_decode_handler(token)
            set_log.debug(f"[username] {username}")

            user = MyUserInfo.objects.filter(email=username['email'], not_send="False")[0]
            # user = User.objects.get(pk=jwt_decode_handler(token)['user_id'])
            set_log.debug(f"[user] {user}")
            if user.is_active:
                return (user, None)
            else:
                raise exceptions.AuthenticationFailed(return_code(2004))
        except Exception as e:
            set_log.debug(f"[ERROR] {e}")
            set_log.info(f"无效的token")

            raise exceptions.AuthenticationFailed(return_code(2004))

        # return (user, None)
