from django.db import models
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin, Group


# Create your models here.

# 自定义用户管理器
class MyUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        """
        创建并保存一个user
        """
        if not email:
            raise ValueError("users must have an email address")
        # email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """
        创建并保存一个超级管理员
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)


# 自定义用户模型
class MyUserInfo(AbstractBaseUser, PermissionsMixin):
    """
    自定义用户模型
    """
    email = models.EmailField(max_length=255, unique=True)
    user_name = models.CharField(max_length=100, blank=True, null=True)
    # last_name = models.CharField(max_length=100, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    # date_joined = models.DateTimeField(auto_now_add=True)
    # 飞书id
    user_id = models.CharField(max_length=100, blank=True, null=True)
    # 工号
    TT_id = models.CharField(max_length=100, blank=True, null=True)
    # 部门
    deptname = models.CharField(max_length=256, blank=True, null=True)
    # 是否发送
    not_send = models.BooleanField(default=False)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = MyUserManager()

    def __str__(self):
        return self.user_name

# 自定义用户组模型
class MyGroupInfo(Group):
    # name = models.CharField(max_length=256, unique=True)
    # 中文name
    describe = models.CharField(max_length=256, blank=True, null=True, default="")

    def str(self):
        return self.name


