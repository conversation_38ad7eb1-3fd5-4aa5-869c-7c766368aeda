import json

data = {
    "treeData": [
        {
            "title": "所有故障",
            "key": "all",
            "query": {
                "reportType": ["故障"]
            },
            "icon": "ant-design:home-outlined"
        },
        {
            "title": "故障报表",
            "selectable": False,
            "key": "report",
            "icon": "majesticons:file-report-line",
            "children": [
                {
                    "title": "2024年所有故障",
                    "key": "node_18579726711735024663109",
                    "query": {
                        "rangeTime": ["2024-01", "2024-12"],
                        "isEffectProduce": ["TT语音", "欢游", "谜境", "麦可", "声洞", "TTChat", "提提电竞", ""],
                        "reportType": ["故障"],
                        "stage": ["已发布"]
                    }
                }
            ]
        }
    ]
}

json_str = json.dumps(data)

print(json_str)