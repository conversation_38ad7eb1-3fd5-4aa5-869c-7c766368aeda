from sqlite3 import IntegrityError
import mysql.connector
import hashlib
import os
import django

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'itmp.settings')
django.setup()

from fault.models import FaultInfo
from fault.models import PlaybackInfo
from fault.models import RectifyInfo

#OLD api_v2_faultinfo  api_v2_playbackinfo
#NEW fault_info playback_info


# 连接到数据库 fmp
db_a = mysql.connector.connect(
  host="**************",
  user="fmp",
  password="fmp123456#",
  database="faults_sys"
)


# 查询数据FaultInfo
FaultInfo_query = "SELECT * FROM api_v2_faultinfo  "
FaultInfo_cursor_a = db_a.cursor()
FaultInfo_cursor_a.execute(FaultInfo_query)
FaultInfo_result_set = FaultInfo_cursor_a.fetchall()
print(FaultInfo_result_set)

# 
FaultInfo_old = ['id', 'fid', 'fault_describe', 'meeting_minutes', 'current_progress', 'fault_year', 'fault_month', 'fault_quarter', 'start_time', 'discovery_time', 'discovery_consuming', 'response_time', 'response_consuming', 'loacte_time', 'loacte_consuming', 'recover_time', 'recover_consuming', 'duration', 'playback', 'cause_classify', 'is_change', 'infrastructure', 'other_reason', 'cause_describe', 'service_classification', 'is_effect_produce', 'direct_effect_revenue', 'is_grade', 'level', 'effect_content', 'is_effect_outside', 'has_watch', 'has_alert', 'is_firstlaunch', 'firstlaunch_source', 'feedback_source', 'cause_locator', 'causelocate_tool', 'improve_action', 'handler', 'driver', 'recorder', 'simple_report_time', 'simple_report_delay', 'preparation_time', 'replay_time', 'replay_delay', 'finish_file_time', 'finish_file_overtime', 'recovery_plan', 'creator', 'architecture_diagram', 'stage', 'troubleshooting', 'creator_id', 'isoperation', 'problem', 'budgetSLA', 'declineSLA', 'groupLink', 'hasSLA', 'isEffectSLA', 'participants', 'effectSLO', 'isDevLocate', 'isOpsLocate', 'reportType', 'isReplay', 'slaDetail']
FaultInfo_new = ['id', 'fid', 'faultDescribe', 'meetingMinutes', 'currentProgress', 'faultYear', 'faultMonth', 'faultQuarter', 'startTime', 'discoveryTime', 'discoveryConsuming', 'responseTime', 'responseConsuming', 'loacteTime', 'loacteConsuming', 'recoverTime', 'recoverConsuming', 'duration', 'playback', 'causeClassify', 'isChange', 'infrastructure', 'otherReason', 'causeDescribe', 'serviceClassification', 'isEffectProduce', 'directEffectRevenue', 'isGrade', 'level', 'effectContent', 'isEffectOutside', 'hasWatch', 'hasAlert', 'isFirstLaunch', 'firstLaunchSource', 'feedbackSource', 'causeLocator', 'isDevLocate', 'isOpsLocate', 'causeLocateTool', 'improveAction', 'handler', 'driver', 'recorder', 'simpleReportTime', 'simpleReportDelay', 'preparationTime', 'replayTime', 'replayDelay', 'finishFileTime', 'finishFileDelay', 'recoveryPlan', 'creator', 'creatorID', 'stage', 'reportType', 'architectureDiagram', 'troubleshooting', 'isOperation', 'problem', 'groupLink', 'hasSLA', 'isEffectSLA', 'declineSLA', 'slaDetail', 'effectSLO', 'budgetSLA', 'isReplay', 'participants', 'improvementFinishTime']

FaultInfo_item={
  'id': 'id', 
  'fid': 'fid', 
  'fault_describe': 'faultDescribe', 
  'meeting_minutes': 'meetingMinutes', 
  'current_progress': 'currentProgress', 
  'fault_year': 'faultYear', 
  'fault_month': 'faultMonth', 
  'fault_quarter': 'faultQuarter', 
  'start_time': 'startTime', 
  'discovery_time': 'discoveryTime', 
  'discovery_consuming': 'discoveryConsuming', 
  'response_time': 'responseTime', 
  'response_consuming': 'responseConsuming', 
  'loacte_time': 'loacteTime', 
  'loacte_consuming': 'loacteConsuming', 
  'recover_time': 'recoverTime', 
  'recover_consuming': 'recoverConsuming', 
  'duration': 'duration', 
  'playback': 'playback', 
  'cause_classify': 'causeClassify', 
  'is_change': 'isChange', 
  'infrastructure': 'infrastructure', 
  'other_reason': 'otherReason', 
  'cause_describe': 'causeDescribe', 
  'service_classification': 'serviceClassification', 
  'is_effect_produce': 'isEffectProduce', 
  'direct_effect_revenue': 'directEffectRevenue', 
  'is_grade': 'isGrade', 
  'level': 'level', 
  'effect_content': 'effectContent', 
  'is_effect_outside': 'isEffectOutside', 
  'has_watch': 'hasWatch', 
  'has_alert': 'hasAlert', 
  'is_firstlaunch': 'isFirstLaunch', 
  'firstlaunch_source': 'firstLaunchSource', 
  'feedback_source': 'feedbackSource', 
  'cause_locator': 'causeLocator', 
  'causelocate_tool': 'causeLocateTool',
  'improve_action': 'improveAction',
  'handler': 'handler', 
  'driver': 'driver', 
  'recorder': 'recorder', 
  'simple_report_time': 'simpleReportTime', 
  'simple_report_delay': 'simpleReportDelay', 
  'preparation_time': 'preparationTime', 
  'replay_time': 'replayTime', 
  'replay_delay': 'replayDelay', 
  'finish_file_time': 'finishFileTime', 
  'finish_file_overtime': 'finishFileDelay', 
  'recovery_plan': 'recoveryPlan', 
  'creator': 'creator',
  'architecture_diagram': 'architectureDiagram', 
  'stage': 'stage', 
  'troubleshooting': 'troubleshooting',
  'creator_id': 'creatorID', 
  'isoperation': 'isOperation',
  'problem': 'problem', 
  'budgetSLA': 'budgetSLA', 
  'declineSLA': 'declineSLA',
  'groupLink': 'groupLink', 
  'hasSLA': 'hasSLA', 
  'isEffectSLA': 'isEffectSLA', 
  'participants': 'participants', 
  'effectSLO': 'effectSLO', 
  'isDevLocate': 'isDevLocate', 
  'isOpsLocate': 'isOpsLocate', 
  'reportType': 'reportType', 
  'isReplay': 'isReplay', 
  'slaDetail': 'slaDetail',
  '0': 'improvementFinishTime'}

new_list = [FaultInfo_item.get(key, key) for key in FaultInfo_old]


# 转化成成list
FaultInfo_data = []
for row in FaultInfo_result_set:
    FaultInfo_dict = dict(zip(new_list, row))
    FaultInfo_dict.pop('id')
    FaultInfo_dict['reportType'] = '故障'
    FaultInfo_data.append(FaultInfo_dict)
print(FaultInfo_data)    
for i in FaultInfo_data:
    i['participants'] = eval(i['participants'])
    i['isEffectProduce'] = eval(i['isEffectProduce'])
    try:
        if i['effectSLO']:
            i['effectSLO'] = eval(i['effectSLO'])
        else:
            i['effectSLO'] = []
    except KeyError:
        i['effectSLO'] = []    

    i['slaDetail'] = eval(i['slaDetail'])
    i['troubleshooting'] = eval(i['troubleshooting'])
    try:
        if i['architectureDiagram']:  # 检查是否为空
            i['architectureDiagram'] = eval(i['architectureDiagram'])
        else:
            i['architectureDiagram'] = []
    except KeyError:
        i['architectureDiagram'] = []
    i['handler'] = eval(i['handler'])
    i['causeLocateTool'] = eval(i['causeLocateTool'])
    i['firstLaunchSource'] = eval(i['firstLaunchSource'])
    i['otherReason'] = eval(i['otherReason'])
    item_to_update = FaultInfo.objects.filter(fid=i['fid']).first()
    # 修改因fid冲突非’故障‘的fid，避免重复
    # print(item_to_update.reportType)
    if item_to_update and item_to_update.reportType == '故障':
        print("退出5")
        exit(5)
    if item_to_update and item_to_update.reportType != '故障':
        # 执行模糊查询
        last_fid = FaultInfo.objects.filter(fid__contains=i['fid'][:8]).order_by('-fid').first().fid
        print(last_fid)
        item_to_update.fid  = str(int(last_fid) + 1)
        print(item_to_update.fid)
        item_to_update.save()
        print("UPdate fid")
        # 修改重fid
        if PlaybackInfo.objects.filter(fid=i['fid']).first():
            print(i['fid'])
            PlaybackInfo.objects.filter(fid=i['fid']).update(fid=item_to_update.fid)
            print(item_to_update.fid)
            #PlaybackInfo.objects.filter(fid=i['fid']).update(fid=item_to_update.fid)
        # if RectifyInfo.objects.filter(fid=i['fid']).first():
        #     print(i['fid'])
        #     RectifyInfo.objects.filter(fid=i['fid']).update(fid=item_to_update.fid) 
    # 插入RectifyInfo（故障整个措施）            
    # RectifyInfo_query = "SELECT * FROM api_v2_rectifyinfo WHERE fid_id = '{}' ".format(i['fid'])
    # RectifyInfo_cursor_a = db_a.cursor()
    # RectifyInfo_cursor_a.execute(RectifyInfo_query)
    # RectifyInfo_result_set = RectifyInfo_cursor_a.fetchall()
    # print(RectifyInfo_result_set)
    # RectifyInfo_old = ['id', 'faultDescribe', 'rectifyDescribe', 'type', 'fault_time', 'start_time', 'estimate_time', 'consuming', 'rate', 'complete_time', 'isDelay', 'driver', 'department', 'fid_id', 'stage', 'delayDate', 'fileUrl']
    # RectifyInfo_new = ['id', 'faultDescribe', 'rectifyDescribe', 'type', 'faultTime', 'startTime', 'estimateTime', 'consuming', 'rate', 'completeTime', 'isDelay', 'driver', 'department', 'fid', 'stage', 'delayDate', 'fileUrl']
    # # 转化成成list
    # RectifyInfo_data = []
    # for row in RectifyInfo_result_set:
    #     RectifyInfo_dict = dict(zip(RectifyInfo_new, row))
    #     RectifyInfo_dict.pop('id')
    #     RectifyInfo_dict['reportType'] = '故障'
    #     RectifyInfo_data.append(RectifyInfo_dict)
    # print(RectifyInfo_data)
    # 查询数据
    PlaybackInfo_query = "SELECT * FROM api_v2_playbackinfo where fid_id = '{}' ".format(i['fid'])
    PlaybackInfo_cursor_a = db_a.cursor()
    PlaybackInfo_cursor_a.execute(PlaybackInfo_query)
    PlaybackInfo_result_set = PlaybackInfo_cursor_a.fetchall()
    print(PlaybackInfo_result_set)
    # 获取列名
    columns = [column[0] for column in PlaybackInfo_cursor_a.description]
    # 
    old=['id', 'stage', 'time', 'content', 'fid', 'image']
    # 转化成成list
    PlaybackInfo_data = []
    for row in PlaybackInfo_result_set:
        item1 = dict(zip(old, row))
        item1.pop('id')
        if item1['time'] != None:
            item1['time'] = item1['time'].strftime('%Y-%m-%d %H:%M')
        item1['stage'] = str(item1['stage'])
        item1['image'] = eval(item1['image'])
        PlaybackInfo_data.append(item1)
    # 转化成playbackInfo对象
    try:
        for item in PlaybackInfo_data:
            record = PlaybackInfo(**item)
            record.save()
    except IntegrityError:
        print(IntegrityError)     
    print(PlaybackInfo_data)
    # FaultInfo数据导入
    try:
        print(i)
        record = FaultInfo(**i)
        record.save()
    except IntegrityError:
        print(IntegrityError)     
    FaultInfo.objects.filter(fid=i['fid']).update(playback=PlaybackInfo_data)   



    # RectifyInfo数据导入
    # try:
    #     for item in RectifyInfo_data:
    #         record = RectifyInfo(**item)
    #         record.save()
    #         record.iid = hashlib.md5(str(record.pk).encode("utf8")).hexdigest()  
    #         record.save()
    # except IntegrityError:
    #     print(IntegrityError) 

