#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试故障报告分析流式 API
"""

import requests
import json
import time


def test_streaming_api():
    """
    测试流式 API
    """
    url = "http://localhost:8000/api/dify/workflows/run/fault_report_analysis/"
    
    # 测试数据
    test_data = {
        "inputs": {
            "fault_description": "数据库连接超时导致服务不可用",
            "fault_time": "2024-01-15 14:30:00",
            "fault_level": "P2",
            "affected_services": ["用户服务", "订单服务"]
        },
        "response_mode": "streaming",  # 启用流式响应
        "user": "test_user"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-test-token'  # 需要替换为实际的测试token
    }
    
    print("开始测试流式 API...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(
            url, 
            json=test_data, 
            headers=headers, 
            stream=True,
            timeout=30
        )
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            print("流式响应内容:")
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"Received: {line}")
                    
                    # 解析 SSE 数据
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])  # 去掉 'data: ' 前缀
                            print(f"Parsed data: {json.dumps(data, ensure_ascii=False, indent=2)}")
                        except json.JSONDecodeError:
                            print(f"Non-JSON data: {line[6:]}")
                    elif line.startswith('event: '):
                        print(f"Event: {line[7:]}")
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
    except KeyboardInterrupt:
        print("\n测试被用户中断")


def test_blocking_api():
    """
    测试阻塞式 API
    """
    url = "http://localhost:8000/api/dify/workflows/run/fault_report_analysis/"
    
    # 测试数据
    test_data = {
        "inputs": {
            "fault_description": "数据库连接超时导致服务不可用",
            "fault_time": "2024-01-15 14:30:00", 
            "fault_level": "P2",
            "affected_services": ["用户服务", "订单服务"]
        },
        "response_mode": "blocking",  # 阻塞式响应
        "user": "test_user"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-test-token'  # 需要替换为实际的测试token
    }
    
    print("开始测试阻塞式 API...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    print("-" * 50)
    
    try:
        start_time = time.time()
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        end_time = time.time()
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Time: {end_time - start_time:.2f}s")
        print(f"Response: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")


if __name__ == "__main__":
    print("故障报告分析 API 测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择测试模式:")
        print("1. 流式响应测试")
        print("2. 阻塞式响应测试")
        print("3. 退出")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            test_streaming_api()
        elif choice == "2":
            test_blocking_api()
        elif choice == "3":
            print("退出测试")
            break
        else:
            print("无效选择，请重新输入")
