import mysql.connector
import hashlib
import os
import django

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'itmp.settings')
django.setup()

from fault.models import PlaybackInfo

#OLD api_v2_faultinfo  api_v2_playbackinfo
#NEW fault_info playback_info


# 连接到数据库 A
db_a = mysql.connector.connect(
  host="**************",
  user="fmp",
  password="fmp123456#",
  database="faults_sys"
)



# 连接到数据库 B
db_b = mysql.connector.connect(
  host="**************",
  user="itmp_rw",
  password="itmp123456#",
  database="itmp_sys"
)

# 查询数据
query = "SELECT * FROM api_v2_playbackinfo where fid_id IN ('202312050001') "
cursor_a = db_a.cursor()
cursor_a.execute(query)
result_set = cursor_a.fetchall()
print(result_set)
# 获取列名
columns = [column[0] for column in cursor_a.description]
# 
old=['id', 'stage', 'time', 'content', 'fid', 'image']
# new=['id', 'fid', 'stage', 'content', 'image', 'time']
# item = {
#   'id': 'id',
#   'stage': 'stage',
#   'ptime': 'time',
#   'content': 'content',
#   'fid_id': 'fid',
#   'image': 'image'
# }

# new_list = [item.get(key, key) for key in old]


# 转化成成list
data = []
for row in result_set:
    item1 = dict(zip(old, row))
    item1.pop('id')
    data.append(item1)



# 写入到数据库 B

print(data)
for item in data:
    record = PlaybackInfo(**item)
    record.save()

