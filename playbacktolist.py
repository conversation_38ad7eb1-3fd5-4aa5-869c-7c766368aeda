import mysql.connector
import hashlib
import os
import django
import datetime

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'itmp.settings')
django.setup()

from fault.models import PlaybackInfo,FaultInfo

#OLD api_v2_faultinfo  api_v2_playbackinfo
#NEW fault_info playback_info


# 连接到数据库 A
db_a = mysql.connector.connect(
  host="**************",
  user="fmp",
  password="fmp123456#",
  database="faults_sys"
)



# 连接到数据库 B
db_b = mysql.connector.connect(
  host="**************",
  user="itmp_rw",
  password="itmp123456#",
  database="itmp_sys"
)

# 查询数据
PlaybackInfo_query = "SELECT * FROM api_v2_playbackinfo where fid_id IN ( '202312050001' ) "
PlaybackInfo_cursor_a = db_a.cursor()
PlaybackInfo_cursor_a.execute(PlaybackInfo_query)
PlaybackInfo_result_set = PlaybackInfo_cursor_a.fetchall()
print(PlaybackInfo_result_set)
# new=['id', 'fid', 'stage', 'content', 'image', 'time']
# item = {
#   'id': 'id',
#   'stage': 'stage',
#   'ptime': 'time',
#   'content': 'content',
#   'fid_id': 'fid',
#   'image': 'image'
# }

# new_list = [item.get(key, key) for key in old]


# 获取列名
# columns = [column[0] for column in PlaybackInfo_cursor_a.description]
# 
old=['id', 'stage', 'time', 'content', 'fid', 'image']
# 转化成成list
PlaybackInfo_data = []
for row in PlaybackInfo_result_set:
    item1 = dict(zip(old, row))
    item1['time'] = item1['time'].strftime('%Y-%m-%d %H:%M')
    PlaybackInfo_data.append(item1)
    # 转化成playbackInfo对象
    print(item1)

#PlaybackInfo_data = [PlaybackInfo(**item) for item in PlaybackInfo_data]
print(PlaybackInfo_data)    
FaultInfo.objects.filter(fid=item1['fid']).update(playback=PlaybackInfo_data)   

# stage str
# image list