import yaml
import os

Codes = {
    0: u'成功',
    20000: u'请求成功',
    # 200-599 预留给 http 状态码

    1001: u'查询失败, 未查询到',
    1002: u'查询失败，参数错误',
    1003: u'插入失败',
    1004: u'更新失败',
    1005: u'删除失败',
    1006: u'DB 连接失败',
    1007: u'分页参数错误',
    1008: u'定时任务创建失败',
    1009: u'参数错误',
    1010: u'图片上传失败',

    2001: u'用户名或密码错误,内部用户请用SSO登录',
    2002: u'用户未登录',
    2003: u'token空白',
    2004: u'token无效',
    2005: u'无权限编辑',
    2006: u'无权限删除',
    2007: u'无权限创建',
    2008: u'权限操作失败',

    -1: u'未知错误，请联系管理员',
    -2: u'登录功能异常，请联系管理员',
    -3: u'数据异常，请联系管理员'

}
def path_config():
    env = os.environ.get('ITMP_ENV')
    if env == "prod":
        # path = "/data/itmp/itmp/common/configs/setting_config.yml"
        path = "common/configs/setting_config.yml"
    elif env == "dev":
        # path = "/data/itmp/itmp/common/configs/setting_test_config.yml"
        path = "common/configs/setting_test_config.yml"
    else:
        raise Exception('环境变量不存在，请先配置环境变量')
    if not os.path.exists(path):
        raise Exception(f'配置文件{path}不存在，请根据配置模板创建config.json文件')
    with open(path, mode='r', encoding='utf-8') as f:
        config_str = f.read()
    config = yaml.load(config_str, Loader=yaml.FullLoader)
    return config



def load_config(path):
    if not os.path.exists(path):
        raise Exception('配置文件不存在，请根据配置模板创建config.json文件')
    with open(path, mode='r', encoding='utf-8') as f:
        config_str = f.read()
    config = yaml.load(config_str, Loader=yaml.FullLoader)
    return config


itmp_conf = path_config()

