#coding: utf-8

import logging
import json

apiLogger = logging.getLogger('api')

class RequestLoggingMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.method == 'GET':
            body = dict(request.GET)
        else:
            try:
                body = json.loads(request.body)
            except ValueError:
                body = {}
            except Exception as e:
                apiLogger.exception(e)
                body = {}
            body.update(dict(request.POST))

        response = self.get_response(request)

        content = response.content

        # 日志不记录登陆账号密码和返回Token
        if request.path == '/api/v1/token-auth':
            body['password'] = '******'
            content = json.loads(response.content)
            if content.get("token"):
                content["token"] = "******"
            content = json.dumps(content)

        if response.status_code != 200:
            apiLogger.error("{} {} {} {} {} {}".format(
                request.user, request.method, response.status_code,
                request.path, body, content
            ))
        else:
            if request.method == 'GET':
                apiLogger.info("{} {} {} {} {}".format(
                    request.user, request.method, response.status_code,
                    request.path, body
                ))
            else:
                apiLogger.info("{} {} {} {} {} {}".format(
                    request.user, request.method, response.status_code,
                    request.path, body, content
                ))

        return response
