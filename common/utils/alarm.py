import json
import requests
# import sys
# sys.path.append("/data/fmp-robot/Charon/robot")
# from libs.Main.send_message import send_message
from common.robot.get_tenant_access_token import get_access_token
from urllib import request
# import sys
# sys.path.append("/data")
from .config import itmp_conf
# 故障管理机器人配置读取
APP_ID = itmp_conf['APP_ID']
APP_SECRET = itmp_conf['APP_SECRET']
from .alarm_mould import get_msg
from .log import Logger
logger = Logger.get_logger(__name__, 2)
dev_chat = itmp_conf["dev_chat_id"]


def send_message(token: str, open_id: str, msg_content):
    # 发送消息模块
    url = "https://open.feishu.cn/open-apis/im/v1/messages"
    params = {"receive_id_type": "chat_id"}
    content = ''
    msg_type = ''
    # Categorize msg_content based on its type
    msg_type_choice = {
        "text": "text",
        "config": "interactive",
        "chat_id": "share_chat",
        "user_id": "share_user",
        "file_key": "file",
        "image_key": "image",
        "zh_cn": "post"
    }
    msg_content_type = list(msg_content.keys())[0]
    msg_type = msg_type_choice.get(msg_content_type, "")
    content = json.dumps(msg_content)
    req_body = {
        "receive_id": open_id,
        "content": content,
        "msg_type": msg_type
    }
    payload = json.dumps(req_body)
    headers = {
        'Authorization': "Bearer " + token,
        'Content-Type': 'application/json'
    }
    try:
        response = requests.post(url, params=params, headers=headers, data=payload)
        response.raise_for_status()
    except requests.HTTPError as http_err:
        logger.error(f"HTTP error occurred: {http_err}")
        return None
    except requests.RequestException as err:
        logger.error(f"Other error occurred: {err}")
        return None

    rsp_body = response.json()
    # Get message_id
    message_id = rsp_body['data']['message_id']
    logger.info(f'[MESSAGE_ID] {message_id}')
    # Check if the message was sent successfully
    code = rsp_body.get("code", -1)
    if code != 0:
        logger.error(f"Send message error, code = {code}, msg = {rsp_body.get('msg', '')}")
    return message_id

def send_alarm(msg, *args, **kwargs):
    access_token = get_access_token()
    msg = get_msg(msg)
    # charon 给 @wenlei 发送告警信息
    # send_message(access_token, "oc_a8d482f18e2e6d3c5076123e1d5b74af", msg)
    # charon 给 群@故障平台沟通 发送告警信息
    # send_message(access_token, "oc_f7d895075971bef9081aaf5b661ae470", msg)
    # charon 给 群@ITMP开发测试群 发送告警信息
    send_message(access_token, dev_chat, msg)

