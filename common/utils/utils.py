# coding: utf-8

import os
import datetime
import time
from .alarm import send_alarm
from rest_framework.response import Response
from rest_framework import viewsets
from rest_framework import status
from rest_framework_jwt.utils import jwt_decode_handler
from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers
from common.utils.log import Logger
set_log = Logger.get_logger(__name__, 4)

Codes = {
    0: u'成功',
    401: u'token 过期',
    20000: u'请求成功',
    # 200-599 预留给 http 状态码

    1001: u'查询失败, 未查询到',
    1002: u'查询失败，参数错误',
    1003: u'插入失败',
    10031: u'创建失败，获取功能异常，请联系管理员',
    1004: u'更新失败',
    1005: u'删除失败',
    1006: u'DB 连接失败',
    1007: u'分页参数错误',
    1008: u'定时任务创建失败',
    1009: u'参数错误',
    1010: u'图片上传失败',

    2001: u'用户名或密码错误,内部用户请用SSO登录',
    2002: u'用户未登录',
    2003: u'token空白',
    2004: u'token无效',
    2005: u'无权限编辑',
    2006: u'无权限删除',
    2007: u'无权限创建',
    2008: u'权限操作失败',

    -1: u'未知错误，请联系管理员',
    -2: u'登录功能异常，请联系管理员',
    -3: u'数据异常，请联系管理员'

}






def return_code(status_code=0, msg='', detail='', data='', *args, **kwargs):
    return_obj = {
        'code': int(status_code),
        'msg': msg or Codes.get(status_code, ''),
        'data': data,
    }
    if status_code != 0:
        # return_obj['detail'] = detail or Codes.get(status_code, '')
        if status_code in [-1, -2, -3, 1003, 1004, 1005, 1006, 1008, 1010, 10031]:
            import inspect
            alarm_msg = {
                "code": status_code,
                "start_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()),
                "content": Codes.get(status_code, ''),
                "data": data,
                "operator": kwargs.get('operator', '未知'),
                "interface": kwargs.get('interface', inspect.currentframe().f_back.f_code.co_name)
            }
            send_alarm(alarm_msg)
    return_obj.update(kwargs)
    if return_obj.get('operator', False):
        return_obj.pop('operator')
    if return_obj.get('interface', False):
        return_obj.pop('interface')
    return return_obj



# Base类，将增删改查方法重写
class NewViewBase(viewsets.ModelViewSet):
    """
    Create a model instance.
    """

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        # print("data", serializer.data)
        # print(serializer.data['startTime'], type(serializer.data['startTime']))
        # print("headers", headers)
        return Response(return_code(0, data=serializer.data, count=1), status=status.HTTP_201_CREATED, headers=headers)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        # print("-"*20, "查看数据")
        return Response(return_code(0, data=serializer.data))

    # 查看方法，加入时间格式化
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        # print("*"*20, "查看数据")
        # print(serializer.data, type(serializer.data))
        # for i in serializer.data:
        #     print(i, type(i))
        return Response(return_code(0, data=serializer.data))

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(return_code(0, data=serializer.data))

    """
    Destroy a model instance.
    """

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        print(instance)
        self.perform_destroy(instance)
        # return Response(status=status.HTTP_204_NO_CONTENT)
        return Response(return_code(0))


def get_user(request):
    try:
        token = request.headers.get("Authorization")
        if token[6] == ' ':
            token = token[7:]
        set_log.debug(f"[frontend token] {token}")
        username = jwt_decode_handler(token)
        set_log.debug(f"[username] {username}")

        user = MyUserInfoSerializers(MyUserInfo.objects.filter(email=username['username'] , not_send="False")[0]).data
        set_log.info(f"[user] {user}")

        return user
    except Exception as e:
        set_log.error(f'获取用户信息失败 {e}')
        return {'user_name': '未知', 'TT_id': '未知'}
