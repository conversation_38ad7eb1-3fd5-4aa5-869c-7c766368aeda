# 异常反馈模块
import json
from datetime import datetime
from common.utils.config import itmp_conf as conf
from ..robot.send_message import send_message
from ..robot.get_tenant_access_token import get_tenant_access_token


dev_chat = conf["dev_chat_id"]


def send_exception(err_code, exception):
    """异常反馈

    Args:
        err_code (int): 错误码
        exception (str): 描述

    Returns:
        bool: 是否反馈成功
    """
    card_path = conf["exception_card_path"]
    with open(card_path, "r") as json_file:
        card = json.load(json_file)
    current_time = datetime.now()  # 当前时间
    time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    labels = card["elements"][0]["fields"]
    insert_info = [err_code, time_str, exception]
    for i, label in enumerate(labels):
        content = labels[i]["text"]["content"]
        card["elements"][0]["fields"][i]["text"]["content"] = content.format(text=insert_info[i])
    token = get_tenant_access_token()
    message_id = send_message(token, dev_chat, card)
    if message_id.startswith("om_"):
        return True
    else:
        return False
