from .config import itmp_conf
from .log import Logger
logger = Logger.get_logger(__name__, 2)
# 故障管理机器人
APP_ID = itmp_conf['APP_ID']
APP_SECRET = itmp_conf['APP_SECRET']
encrypt_key = itmp_conf['encrypt_key']


def get_msg(msg):
    print(msg, type(msg))

    mould = {
        "config": {
            "wide_screen_mode": True
        },
        "elements": [
            {
                "fields": [
                    {
                        "is_short": True,
                        "text": {
                            "content": "**🚨 error code：**\n" + str(msg['code']),
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "content": "**🕐 故障时间：**\n" + msg['start_time'],
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": False,
                        "text": {
                            "content": "",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "content": "👸🤴 操作人：" + msg['operator'],
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": False,
                        "text": {
                            "content": "👨‍🦲 负责人：<at id=ou_b5e448ae73e92c317eecc71ed7989d53></at>",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": False,
                        "text": {
                            "content": "👉 报障接口：" + msg['interface'],
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "content": "**📋 告警内容：**\n" + msg['content'] + ' - ' + msg['data'],
                            "tag": "lark_md"
                        }
                    }
                ],
                "tag": "div"
            }
        ],
        "header": {
            "template": "orange",
            "title": {
                "content": "👮‍♂️👮‍♀️Error - ITMP" + itmp_conf['event'] + "环境接口错误",
                "tag": "plain_text"
            }
        }
    }

    return mould
