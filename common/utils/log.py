# coding: utf-8

import logging
import os
import sys


# 重构Format
class ParentDirFormatter(logging.Formatter):
    def format(self, record):
        filename = os.path.basename(record.pathname)
        parent_dirname = os.path.basename(os.path.dirname(record.pathname))
        record.pathname = os.path.join(parent_dirname, filename)
        return super().format(record)


class Logger:
    @staticmethod
    def get_logger(name, status):
        # 创建一个logger对象
        logger = logging.getLogger(name)

        # 设置logger的最低输出级别，默认为DEBUG
        logger.setLevel(logging.DEBUG)
        # 分发写入
        if status == 0:
            # 创建一个输出到文件的handler
            debug_log_path = "logs/framework.log"
            file_handler = logging.FileHandler(debug_log_path)
            file_handler.setLevel(logging.INFO)
        elif status == 1:
            # 创建一个输出到文件的handler
            debug_log_path = "logs/circulation.log"
            file_handler = logging.FileHandler(debug_log_path)
            file_handler.setLevel(logging.INFO)
        elif status == 2:
            # 创建一个输出到文件的handler
            debug_log_path = "logs/problem.log"
            file_handler = logging.FileHandler(debug_log_path)
            file_handler.setLevel(logging.INFO)
        elif status == 3:
            # 创建一个输出到文件的handler
            debug_log_path = "logs/fault.log"
            file_handler = logging.FileHandler(debug_log_path)
            file_handler.setLevel(logging.INFO)
        else:
            # 创建一个输出到文件的handler
            debug_log_path = "logs/info.log"
            file_handler = logging.FileHandler(debug_log_path)
            file_handler.setLevel(logging.INFO)

        # 创建一个输出到屏幕的handler
        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setLevel(logging.DEBUG)

        # 创建一个输出到error.log的handler
        error_handler = logging.FileHandler("logs/error.log")
        error_handler.setLevel(logging.ERROR)

        # 定义一个日志格式
        formatter = logging.Formatter('[%(levelname)s]-[%(asctime)s]-[%(pathname)s:%(lineno)d] - %(message)s',
                                      datefmt='%Y-%m-%d/%H:%M:%S')

        parent_formatter = ParentDirFormatter(formatter._fmt)
        parent_formatter.datefmt = formatter.datefmt

        # 给handler设置格式
        file_handler.setFormatter(parent_formatter)
        stream_handler.setFormatter(parent_formatter)

        # 给logger添加handler
        logger.addHandler(file_handler)
        logger.addHandler(stream_handler)

        return logger

# 测试
# set_log = Logger.get_logger()
# set_log.debug("test debug mesg")
