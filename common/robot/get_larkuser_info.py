import requests
from .get_tenant_access_token import get_access_token
from ..utils.log import Logger

logger = Logger.get_logger(__name__, 1)


def get_larkuser_info(user_id, id_type="user_id"):
    # 获取飞书用户信息
    logger.info(f"[GET_LARKUSER_INFO] ID: {user_id}, ID_TYPE: {id_type}")
    url = (f"https://open.feishu.cn/open-apis/contact/v3/users/{user_id}?department_id_type=open_department_id"
           f"&user_id_type={id_type}")
    payload = ''
    access_token = get_access_token()

    headers = {
        'Authorization': 'Bearer ' + access_token
    }
    try:
        response = requests.get(url, headers=headers, data=payload).json()
        code = response.get("code", -1)
        if code != 0:
            msg = response.get("msg", "Unknown error")
            logger.error(f"[GET_LARKUSER_INFO] Error: code={code}, error={msg}")
            return False
        user_info = response["data"].get("user")
        lark_user_id = user_info.get("user_id")
        lark_open_id = user_info.get("open_id")
        user_name = user_info.get("name")
        user_email = user_info.get("email")
        leader_id = user_info.get("leader_user_id")
        logger.info("[GET_LARKUSER_INFO] SUCCESS")
        return [user_name, lark_user_id, lark_open_id, user_email, leader_id]
    except Exception as e:
        logger.error(f"[GET_LARKUSER_INFO] Error: {e}")
        return False
