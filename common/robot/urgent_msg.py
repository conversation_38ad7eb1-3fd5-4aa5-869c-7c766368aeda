import requests
import json
from common.robot.get_tenant_access_token import get_access_token
from common.utils.log import Logger


logger = Logger.get_logger(__name__, 1)


def urgent_msg(message_id, user_id_list, user_type="open_id"):
    url = f"https://open.feishu.cn/open-apis/im/v1/messages/{message_id}/urgent_app?user_id_type={user_type}"
    payload = json.dumps({
        "user_id_list": user_id_list
    })

    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + get_access_token()
    }

    response = requests.patch(url, headers=headers, data=payload).json()
    logger.debug(response)
    code = response.get("code", -1)
    if code != 0:
        msg = response.get("msg", "Unknow error")
        logger.error(f"[URGENT_MSG] Urgent error: code={code}, error={msg}")
        return False
    logger.info(f"[URGENT_MSG] Urgent success")
    return True



