
class RegisterCommand:
    """注册命令"""
    def __init__(self):
        self.command_functions = {}

    def register_command(self, *actions):
        # 注册命令
        def decorator(func):
            for user_action in actions:
                self.command_functions[user_action.lower()] = func
            return func

        return decorator

    def dispatch(self, user_action, abnormal_result=None):
        # 分发执行指定命令对应的函数
        func = self.command_functions.get(user_action.lower(), abnormal_result)
        return func()


# 示例用法
# 创建一个 RegisterCommand 实例
# register = RegisterCommand()


# 定义要执行的函数
# @register.register_command("action1", "action2")
# def my_function():
#     print("执行自定义函数")


# 执行注册的命令
# user_action = "action1"
# if user_action.lower() in register.command_functions:
#     function_to_execute = register.command_functions[user_action.lower()]
#     function_to_execute()
