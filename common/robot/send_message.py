import logging
import requests
import json
from common.utils.log import Logger
import re

logger = Logger.get_logger(__name__, 1)


def send_message(token: str, input_id: str, msg_content):
    # 发送消息模块
    url = "https://open.feishu.cn/open-apis/im/v1/messages"
    regex_list = [('chat_id', r"^oc_.*$"), ('union_id', r"^on_.*$"), ('open_id', r"^ou_.*$")]

    # 遍历正则表达式列表进行匹配
    output_id = ""
    for regex_item in regex_list:
        match_result = re.match(regex_item[1], input_id)
        if match_result:
            output_id = regex_item[0]
            logger.info("[SEND_MSG_ID_TYPE] {}".format(output_id))
            break
    else:
        output_id = "user_id"
    params = {"receive_id_type": output_id}
    content = ''
    msg_type = ''
    # Categorize msg_content based on its type
    msg_type_choice = {
        "text": "text",
        "config": "interactive",
        "elements": "interactive",
        "chat_id": "share_chat",
        "user_id": "share_user",
        "file_key": "file",
        "image_key": "image",
        "zh_cn": "post"
    }
    msg_content_type = list(msg_content.keys())[0]
    msg_type = msg_type_choice.get(msg_content_type, "wrong_type")
    # logger.info("[SEND_MESSAGE] Msg_type = {}".format(msg_type))
    content = json.dumps(msg_content)
    req_body = {
        "receive_id": input_id,
        "content": content,
        "msg_type": msg_type
    }
    payload = json.dumps(req_body)
    headers = {
        'Authorization': "Bearer " + token,
        'Content-Type': 'application/json'
    }
    response = requests.post(url, params=params, headers=headers, data=payload)
    rsp_body = response.json()
    code = rsp_body.get("code", -1)
    # logger.info("[SEND_MESSAGE] CODE = {}".format(code))
    # response.raise_for_status()
    if code != 0:
        logger.error(f"[SEND_MESSAGE] ERROR: code = {code}, msg = {rsp_body.get('msg', '')}")
        return f"Message send error, code = {code}"
    # Get message_id
    message_id = rsp_body['data']['message_id']
    logger.info(f'[MESSAGE_ID] {message_id}')
    return message_id
