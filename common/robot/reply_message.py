import requests
import json
import uuid
from common.utils.log import Logger

logger = Logger.get_logger(__name__,1)


def uuid_create():
    """随机生成uuid"""
    _uuid = uuid.uuid1()
    return _uuid


def reply_message(token: str, sender_message_id, msg_content):
    # 发送消息模块
    url = f"https://open.feishu.cn/open-apis/im/v1/messages/{sender_message_id}/reply"
    # Categorize msg_content based on its type
    msg_type_choice = {
        "text": "text",
        "config": "interactive",
        "elements": "interactive",
        "chat_id": "share_chat",
        "user_id": "share_user",
        "file_key": "file",
        "image_key": "image",
        "zh_cn": "post"
    }
    msg_content_type = list(msg_content.keys())[0]
    msg_type = msg_type_choice.get(msg_content_type, "")
    content = json.dumps(msg_content)
    uuid = uuid_create()
    req_body = {
        "content": content,
        "msg_type": msg_type,
        "uuid": str(uuid)
    }
    payload = json.dumps(req_body)
    headers = {
        'Authorization': "Bearer " + token,
        'Content-Type': 'application/json'
    }
    response = requests.post(url, headers=headers, data=payload)
    rsp_body = response.json()
    # Check if the message was sent successfully
    code = rsp_body.get("code", -1)
    if code != 0:
        logger.error(f"[REPLY_MESSAGE] ERROR: code = {code}, msg = {rsp_body.get('msg', '')}")
        return f"Message send error, code = {code}"
    # Get message_id
    message_id = rsp_body['data']['message_id']
    logger.info(f'[MESSAGE_ID] {message_id}')
    return message_id
