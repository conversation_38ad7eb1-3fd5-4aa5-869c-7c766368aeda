import yaml
import os


def read_file(path):
    if not os.path.exists(path):
        raise Exception('配置文件不存在，请根据配置模板创建config.json文件')
    with open(path, mode='r', encoding='utf-8') as f:
        return f.read()


def load_config():
    # config_path = "/data/jarvis_robot/jarvis/config/config.yml"  # 配置存放位置
    config_path = "common/configs/jarvis_config.yml"  # 配置存放位置
    # config_path = "D:/OneDrive - Nomarl University/Project\jarvis_robot/jarvis/robot/config/config.yml"
    config_str = read_file(config_path)
    config = yaml.load(config_str, Loader=yaml.FullLoader)
    return config


conf = load_config()
# print(conf.get("ThirdPhone_msgcard_path"))
