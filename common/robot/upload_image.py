import requests
from .get_tenant_access_token import get_access_token


def upload_image(binary):
    url = "https://open.feishu.cn/open-apis/im/v1/images"
    access_token = get_access_token()
    payload = {'image_type': 'message'}
    files = [
        binary
    ]
    headers = {
        'Authorization': 'Bearer '
    }

    response = requests.request("POST", url, headers=headers, data=payload, files=files).json()

    print(response.text)
