# 撤回飞书消息
import requests
import json
from .get_tenant_access_token import get_access_token
from ..utils.log import Logger


logger = Logger.get_logger(__name__, 1)


def recall_message(message_id):
    url = f"https://open.feishu.cn/open-apis/im/v1/messages/{message_id}"
    payload = ''
    headers = {
        'Authorization': 'Bearer ' + get_access_token()
    }

    response = requests.delete(url=url, headers=headers, data=payload).json()
    code = response.get("code", -1)
    if code != 0:
        msg = response.get("msg", "error")
        logger.error(f"[RECALL_MESSAGE] ERROR: {msg}")
        return False
    logger.info(f"[RECALL_MESSAGE] {message_id} recall SUCCESS")
    return True
