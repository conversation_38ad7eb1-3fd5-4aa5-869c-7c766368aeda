import requests
from urllib import request, parse
import json
import yaml
from common.robot.config import conf
from common.utils.log import Logger

logger = Logger.get_logger(__name__, 1)


APP_ID = conf['APP_ID']
APP_SECRET = conf['APP_SECRET']
# APP_VERIFICATION_TOKEN = conf['APP_VERIFICATION_TOKEN']

# 用Charon
CHARON_APP_ID = conf['Charon_APP_ID']
CHARON_APP_SECRET = conf['Charon_APP_SECRET']


def get_tenant_access_token():
    # 获取tenant_access_token
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    headers = {
        "Content-Type": "application/json"
    }
    req_body = {
        "app_id": APP_ID,
        "app_secret": APP_SECRET
    }

    data = bytes(json.dumps(req_body), encoding='utf8')
    req = request.Request(url=url, data=data, headers=headers, method='POST')
    try:
        response = request.urlopen(req)
    except Exception as e:
        logger.error(f"[GET_TENANT_ACCESS_TOKEN] Error: {e}")
        return

    rsp_body = response.read().decode('utf-8')
    rsp_dict = json.loads(rsp_body)
    code = rsp_dict.get("code", -1)
    logger.debug(f"[robot ID] {APP_ID}")
    logger.info(f'[get_tenant_access_token success] {rsp_dict.get("tenant_access_token", "")}')
    if code != 0:
        logger.error(f"[GET_TENANT_ACCESS_TOKEN] Error: Code ={code}")
        return
    return rsp_dict.get("tenant_access_token", "")

def get_access_token():
    # 获取新机器人access_token
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    headers = {
        "Content-Type": "application/json"
    }
    req_body = {
        "app_id": CHARON_APP_ID,
        "app_secret": CHARON_APP_SECRET
    }

    data = bytes(json.dumps(req_body), encoding='utf8')
    req = request.Request(url=url, data=data, headers=headers, method='POST')
    try:
        response = request.urlopen(req)
    except Exception as e:
        logger.error(f"[GET_TENANT_ACCESS_TOKEN] Error: {e}")
        return

    rsp_body = response.read().decode('utf-8')
    rsp_dict = json.loads(rsp_body)
    code = rsp_dict.get("code", -1)
    logger.debug(f"[robot ID] {APP_ID}")
    logger.info(f'[get_tenant_access_token success] {rsp_dict.get("tenant_access_token", "")}')
    if code != 0:
        logger.error(f"[GET_TENANT_ACCESS_TOKEN] Error: Code ={code}")
        return
    return rsp_dict.get("tenant_access_token", "")

