# 正式配置文件
# setting配置

# 配置路径
debug_setting: True
allowed_hosts: ["*"]

# 数据库配置
databases_user: "itmp_rw"
databases_pswd: "itmp123456#"
databases_host: "**************"
databases_port: "3306"
databases_name: "itmp"

# 平台地址
fmp_url: "https://fmp.ttyuyin.com"
itmp_url: "http://itmp-test.ttyuyin.com"
itmp_robot_url: "http://*************:8556"
itmp_rtmp_host: "*************"

# obs配置
bucketname: "obs-test-hw-gz-yw-fmp-backend"
access_key_id: "NGGUPOFPCNK92SLZJUEK"
secret_access_key: "vNW4koVhzMd0VcFdR1zjlBJDO7Pi8kFYlL5YwErT"
server: "obs.cn-south-1.myhuaweicloud.com"

# sso
client_id: "itmp-test"
client_secret: "yzy5yjhjzmytntdhni00zmqxlwexywqtzmzmngu1ntrmm2nk"
redirect_uri: "https%3A%2F%2Fitmp-test.ttyuyin.com%3A8765%2Fapi%2FuserAuth%2Fgetidentity"
address: "https://test-ebc-sso.52tt.com"

# charon
APP_ID: "cli_a3180c1fddbad00c"
APP_SECRET: "svC1DyegbLo2To9JSc7J3e5vlMOhp20e"
encrypt_key: "kiHup9JUoOsLGOIL6aH5EgsiFB7pxSfL"
event: "测试"

# DI开放接口
#white_ip_list: ["*************", "*************"]
DI_white_ip_list:
  [
    "fmp-test1.ttyuyin.com",
    "xxx",
    "testing-dev-insight.ttyuyin.com",
    "dev-insight.ttyuyin.com",
  ]

# 天相白名单
Telemetry_white_token_list:
  [
    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************.bgKqKeuVE1BTKkslyZoM6Gb4xPhj_VxT8fryplw9M74",
  ]

# 天相SAL相关接口
Telemetry_sla_slo_url: "http://testing-tt-telemetry-web.ttyuyin.com/api/v1/sla/search"
Telemetry_slo_score_url: "http://testing-tt-telemetry-web.ttyuyin.com/api/v1/slo/score/statCost"
Telemetry_update_effectSLO_url: "http://testing-tt-telemetry-web.ttyuyin.com/api/v1/fmp/fault/effectSLO"

# 卡片位置
feedback_card_path: "circulation/template/user_report_fault.json"
exception_card_path: "circulation/template/exception.json"
dev_chat_id: "oc_f52d5bfd4b7f11b144ab2b995f45a419"

# jarvis
ITMP_card_url: "http://itmp.ttyuyin.com/circulation/api/card_operate/"
