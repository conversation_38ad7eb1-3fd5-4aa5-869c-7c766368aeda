# 正式配置文件
# setting配置

# 配置路径
debug_setting: "False"
allowed_hosts: ["*"]

# 数据库配置
databases_user: "itmp_rw"
databases_pswd: "itmp123456#"
databases_host: "**************"
databases_port: "3306"
databases_name: "itmp_sys"

# 平台地址
fmp_url: "https://fmp.ttyuyin.com"
itmp_url: "https://itmp.ttyuyin.com"
itmp_robot_url: "http://itmp-robot.ttyuyin.com"
itmp_rtmp_host: "************"

# obs配置
bucketname: "obs-prod-hw-bj-yw-fmp-backend"
access_key_id: "PTIF140E2NIGOIXBXWMN"
secret_access_key: "1i05SIkeIBb7Ms44xulQwC2Du8YryV2IGm9CZKws"
server: "obs.cn-north-4.myhuaweicloud.com"

# sso
client_id: "itmp"
client_secret: "nwrlmzc0ztktmta3nc00zdg1lwjizwetmgizogqxymyzyzzm"
redirect_uri: "https%3A%2F%2Fitmp.ttyuyin.com%3A8765%2Fapi%2FuserAuth%2Fgetidentity"
address: "https://ebc-sso.52tt.com"

# charon
APP_ID: "cli_a3180c1fddbad00c"
APP_SECRET: "svC1DyegbLo2To9JSc7J3e5vlMOhp20e"
encrypt_key: "kiHup9JUoOsLGOIL6aH5EgsiFB7pxSfL"
event: "生产"

# DI开放接口
#white_ip_list: ["*************", "*************"]
DI_white_ip_list:
  [
    "fmp-test1.ttyuyin.com",
    "xxx",
    "testing-dev-insight.ttyuyin.com",
    "dev-insight.ttyuyin.com",
  ]

# 天相白名单
Telemetry_white_token_list:
  [
    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************.bgKqKeuVE1BTKkslyZoM6Gb4xPhj_VxT8fryplw9M74",
  ]

# 天相SAL相关接口
Telemetry_sla_slo_url: "https://tt-telemetry-web.ttyuyin.com/api/v1/sla/search"
Telemetry_slo_score_url: "https://tt-telemetry-web.ttyuyin.com/api/v1/slo/score/statCost"
Telemetry_update_effectSLO_url: "https://tt-telemetry-web.ttyuyin.com/api/v1/fmp/fault/effectSLO"

# 卡片位置
feedback_card_path: "circulation/template/user_report_fault.json"
exception_card_path: "circulation/template/exception.json"
dev_chat_id: "oc_f52d5bfd4b7f11b144ab2b995f45a419"

# jarvis
ITMP_card_url: "http://itmp.ttyuyin.com/circulation/api/card_operate/"
