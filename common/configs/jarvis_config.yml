# tokens存放路径
tokens_path: '/data/jarvis_robot/jarvis/robot/config/tokens'

# jarvis相关tokens
APP_ID: "cli_a1d8686d873a500e"
APP_SECRET: "TEQqjQjYaRqGK4IFc2ADhfYNVhxs58gY"
APP_VERIFICATION_TOKEN: "RAj4vSyJtnojswdety4L7liipa0LIXjT"
# charon相关tokens
Charon_APP_ID: "cli_a3180c1fddbad00c"
Charon_APP_SECRET: "svC1DyegbLo2To9JSc7J3e5vlMOhp20e"

# 测试机器人信息
ULTRON_APP_ID: "cli_a34cb9c7647b500e"
ULTRON_APP_SECRET: "yUQua7OvFd2x4l2iyRUZKbAMdW5IxGYL"
ULTRON_APP_VERIFICATION_TOKEN: "yZzmYY12QIKUvAGRUlf5CfU3BHXZzsHb"

# 开发者id
DEV_LARK_USER_ID: "166710400"

LOG_PATH: "/data/jarvis_robot/jarvis/logs/service.log"
CSV_STORE_PATH: "/data/jarvis_robot/jarvis/static/data/"


YUNWEI_GROUP: "oc_662008e92b805230570f812483446bc5"

# decrypt_key
DECRYPT_KEY: "b'5MTn8td4hpZxp6KpnPyFTQilVgyNAgDDqqiVfqxwdQ0='"


# 后台重定向url
BACKEND_REDIRECT_URI: 'https://q9jvw0u5f5.feishu.cn/drive/home/'

DEV_LARK_OPEN_ID: "ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2"

# 基础卡片路径
help_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/help.json"
error_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/error.json"

# 日志路径
robot_log_path: "/data/jarvis_robot/jarvis/logs/robot.log"
fault_analysis_log_path: "/data/jarvis_robot/jarvis/logs/fault_analysis.log"

# CMDB相关tokens
CMDB_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/cmdb.json"
CMDB_Token: "NOLHTYVeHEdM3fIA4MLjzll2s1FWvdbsFfhNKGuGKUSzZYIWckSIdxV3NQ8fyKUc"
CMDB_host: "http://yw-inner-cmdb.ttyuyin.com:5000"
CMDB_dev_host: "http://*************:5000/"
CMDB_path: "/alarm/v2/resource/module_info/"


# 近期活动tokens
# https://q9jvw0u5f5.feishu.cn/sheets/shtcnRei1EFCPwfkai3lpMtVS5m?sheet=ypyHNw
act_spreadsheetToken: 'shtcnRei1EFCPwfkai3lpMtVS5m'
act_sheet: 'ypyHNw'

# 命令号tokens
# cmd1: https://q9jvw0u5f5.feishu.cn/sheets/shtcnpNMhCrRaSHVCAZL1aDWnDe?sheet=0d8675
# cmd2: https://q9jvw0u5f5.feishu.cn/sheets/shtcnlIzfLff7JNkr9yV9mIK49G?sheet=njoHTA
cmd_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/get_cmd.json"
cmd2_spreadsheetToken: 'shtcnlIzfLff7JNkr9yV9mIK49G'
cmd2_sheet: 'njoHTA'

cmd1_spreadsheetToken: 'shtcnpNMhCrRaSHVCAZL1aDWnDe'
cmd1_sheet: '0d8675'

# 服务tokens
# https://q9jvw0u5f5.feishu.cn/sheets/shtcnlIzfLff7JNkr9yV9mIK49G?sheet=Nxfv24
svc_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/get_svc.json"
svc_spreadsheetToken: 'shtcnlIzfLff7JNkr9yV9mIK49G'
svc_sheet: 'Nxfv24'

# 手机号tokens
# https://q9jvw0u5f5.feishu.cn/sheets/shtcnqSHFlAHKxvshxlZoIlNFmc?sheet=e96771
phone_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/get_tel.json"
phone_spreadsheetToken: 'shtcnqSHFlAHKxvshxlZoIlNFmc'
phone_sheet: 'RlqMHu'


# 云厂商tokens
# https://q9jvw0u5f5.feishu.cn/sheets/shtcnjJgPg5nuDSDMujEG2PD9xf?sheet=2266aa
ThirdPhone_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/get_cloud.json"
ThirdPhone_spreadsheetToken: 'shtcnjJgPg5nuDSDMujEG2PD9xf'
ThirdPhone_sheet: '2266aa'


# 错误码tokens
err_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/get_err.json"
err_wikiToken: 'wikcnRAhgexWDgoIyQvOANCwPXb'
err_wikisheet: '0Eyfbj'


# 值班信息tokens
schedule_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/daily_zhiban.json"
schedule_wikiToken: 'wikcndn3dcxhspGpiRmgdJtT61b'
schedule_wikisheet: 'eUXx8o'


# 天气tokens
weather_code: "1c876451a6bb4bff98fcc9fa98c8f4e6"
weather_AppKey: "204096647"
weather_AppSecret:  "yZsLxiGz5vZbV5HvoPjFHjQeG37U90Qq"


# mysql数据库信息
mysql_host: "**************"
mysql_user: "fmx"
mysql_password: "fmx123456"
mysql_database: "faults"


# 故障信息多维表格token
fault_app_token: 'bascndH4eirO5Hkjx9tN9rQFTrb'
fault_table_id: 'tblSA1GuT1ozSJtf'
fault_form_id: 'vewWcYYzw8'


# 定时推送警告
warning_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/warning.json"
open_chat_id1: "oc_7adc3624a795ead71e2528ee01bedb32"
open_chat_id2: "oc_ad5025c88cc114f0e93640ea49fc3a5d"
open_chat_id3: "oc_b7e02619fd4cee82d2e8dd5b846f9586"
open_chat_id4: "oc_96371e74589748c75dafad667e66f75b"


# 自动拉群
auto_create_group_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/auto_create_group.json"
bot_id: "cli_a1d8686d873a500e"
ywzb1_id: "6gd65242"
ywzb2_id: "9e193ea4"

# 定向反馈
reback_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/reback.json"
feedback_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/feedback.json"

# chatgpt 配置
openai_key: "***************************************************"
conversation_max_tokens: 3000
# AZURE_OPENAI_API_KEY: "********************************"
# AZURE_OPENAI_API_VERSION: "2023-05-15"
# AZURE_OPENAI_API_BASE: "https://west-europe-quwan-yw-test-01.openai.azure.com/"
# AZURE_OPENAI_API_TYPE: "azure"

AZURE_OPENAI_API_KEY: "********************************"
AZURE_OPENAI_API_VERSION: "2023-07-01-preview"
AZURE_OPENAI_API_BASE: "https://japan-east-quwan-yw-infra-01.openai.azure.com"
AZURE_OPENAI_API_TYPE: "azure"


# 获取用户id
get_user_id_url: "https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id?user_id_type="

# 根因定位
fault_analysis_rootanalysis: "https://yw-aiopsjupyter.ttyuyin.com:5666/apis/rootanalysis/"
fault_analysis_getresult: "https://yw-aiopsjupyter.ttyuyin.com:5666/apis/getresult/"
fault_analysis_return_path: "/data/jarvis_robot/jarvis/robot/template/fault_analysis_return.json"
fault_analysis_path: "/data/jarvis_robot/jarvis/robot/template/fault_analysis.json"


# 故障演练
fault_dirll_msgcard_path: "/data/fmp-robot/Charon/robot/template/fault_drill.json"

# 重点告警群自动根因定位群
auto_root_analysis_group: "oc_35595c12fe976f1e168f8ae6336edef5"

# 作业平台Token
job_platform_url: "https://yw-ops.ttyuyin.com/tt-job-salt-api/v1/api/run/script"
job_platform_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzeXNfYmFzZV9hbGFybSIsImlzcyI6ImFkbWluIiwiZXhwIjoxOTk3MjQzMDM4LCJ1c2VySWQiOiJzeXNfYmFzZV9hbGFybSJ9.mSWWUowtxsmmM5zQ1JJaiMNpFGCKj8tkgn7_AAyDPDU"


# jarvis token
jarvis_token: "vwpIJ7jiYsZHR_v.iGLRXE2Ste.P$fhX9ifWLOB._ZQ9piyqaSHuCqhknQg_TvuNLE9vu7yj#Aqo&#fcdhMVAVTbj2q5T4VfmMxSg5grUvt6aMkeo#gA.Oe#ASsgX-qD"


# 云测反馈群
test_feedback_group: "oc_4da6a42798974eb38099033142dc6fd0"
test_feedback_app_token: "bascnYiIWHunCei7Ojv8Zn5Q6Ve"
test_feedback_table_id: "tbl3C3FxDZ7pOL5b"
test_feedback_view_id: "vewc4NbOEp"

# 日志监听配置
log_monitor_card_path: "/data/jarvis_robot/jarvis/robot/template/log_report.json"

# 自动发出故障报告
fault_report_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/fault_report.json"

# 故障管理平台cookie
fault_platform_cookie: "EB3xwAfrZwz.262VbIioi755+oYP#4t39RF_0+NR3BWzeGM0Cc7HvWDeT_50f5i+tzvu9ZU$qXaj8LoV0gV2JhHQ9WmRTK9LnKuu#Q4ZYbHm7krnyCmrV&fIv2ScElTz"
create_report_api: "http://fmp.ttyuyin.com/api/V2/jarvis/createReport/"


# 监测告警卡片
alert_first_card_path: "/data/jarvis_robot/jarvis/robot/template/alert_first_line.json"

# 入口界面
entrance_card_path: "/data/jarvis_robot/jarvis/robot/template/entrance.json"

# 消息封装到卡片
message_into_card_path: "/data/jarvis_robot/jarvis/robot/template/message_into_card.json"

# 月报卡片
month_fault_report_path: "/data/jarvis_robot/jarvis/robot/template/month_fault_report.json"

# 人工服务
human_intervene_card_path: "/data/jarvis_robot/jarvis/robot/template/human_intervene.json"

# 知了转人工服务
human_service_card_path: "/data/jarvis_robot/jarvis/robot/template/human_service.json"

# 故障进展同步
fault_progress_syn_card_path: "/data/jarvis_robot/jarvis/robot/template/fault_progress_syn.json"

# 故障监听卡片
fault_detect_card_path: "/data/jarvis_robot/jarvis/robot/template/fault_detect_trigger.json"

# 知了人工服务人员列表
human_service_default: 
  - "6gd65242"
  - "9e193ea4"


# 电话呼叫卡片
phone_call_msgcard_path: "/data/jarvis_robot/jarvis/robot/template/phone_call.json"
phone_call_url: "http://tt-cloud-phone-call.ttyuyin.com/api/v2/callsync"

# 监听人员json配置
monitor_role_path: "/data/jarvis_robot/jarvis/config/monitor_role.json"


# 大模型请求链接
运维知识图谱: "http://10.112.15.208:8686/ywllmbase/"
用户反馈: 'http://10.112.15.208:8686/feedback/'
日志查询: 'http://10.112.15.208:8686/loggpt/'
CMDB资源查询: 'http://10.112.15.208:8686/cmdbgpt/'
OPSBRAIN: "http://10.65.128.7:8000/chat"


ITMP卡片操作: "http://fmp-test.ttyuyin.com:8765/circulation/api/card_operate/"

wechaty_token_1: "9d0033bc-0653-4007-931f-9974d052ca7e"
wechaty_token_2: "ea8b4026-b297-434b-b4e8-a21430e00220"
wechaty_endpoint: "**********:9001"


# 响应角色
response_role_list:
  - "Jarvis"
  - "运维值班-1"
  - "运维值班-2"

test_chat_list:
  - "oc_3e9e32cf6ed42cacb244eefe5c479024"
  - "oc_7adc3624a795ead71e2528ee01bedb32"
  - "oc_dc2b7c5e45fb3a1fa0bb8a08ac0565e5"

DEV&TEST_GROUP: "oc_7adc3624a795ead71e2528ee01bedb32"

handle_message_urls:
  - "http://yw-jarvis.ttyuyin.com/robot/handler/"


# 命令列表
action_list: 
  - help
  - tel
  - err
  - cloud
  - 值班
  - find_host
  - find_app
  - find_cmd
  - find_mongodb
  - find_mysql
  - find_redis
  - find_kafka
  - host
  - app
  - cmd
  - mongo
  - mysql
  - redis
  - kafka
  - 拉群
  - tt拉群
  - tc拉群
  - 进群
  - 测试拉群
  - 天气
  - 根因定位
  - 强制根因定位
  - 故障开始
  - 故障结束
  - 测试故障结束
  - 故障中止
  - 修改预设
  - 获取消息
  - 分析故障
  - 监测告警
  - 暂停监测
  - 已处理
  - 月报
  - 电话
  - call
  - 加急
  - 打电话给
  - 拨打
  - 呼叫
  - 查反馈
  - 查功能
  - caidan


err_codes : {
    0: "成功",

    # 200-599 预留给 http 状态码

    1001: "查询失败, 未查询到",
    1002: "查询失败，参数错误",
    1003: "插入失败",
    1004: "更新失败",
    1005: "删除失败",
    1006: "DB 连接失败",
    1007: "分页参数错误",
    1008: "定时任务创建失败",
    1009: "参数错误",
    1010: "发送失败",
    1011: "提交失败",
    1012: "撤回失败",
    1013: "获取失败",
    1014: "操作失败",
    1015: "无权限操作",

    2001: "用户未登录",
    2002: "token失效",
    2003: "sso_code失效",
    2004: "无权限编辑",
    2005: "无权限删除",
    2006: "无权限创建",
    2007: "权限操作失败",

    -1: "未知错误，请联系管理员",
    -2: "其他错误，请联系管理员",
    -3: "数据异常，请联系管理员"
}
