"""
Django settings for itmp project.

Generated by 'django-admin startproject' using Django 3.1.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""
import datetime
from pathlib import Path
import sys
sys.path.append("..")
from common.utils.config import load_config, path_config
conf = path_config()
# conf = load_config('common/configs/setting_config.yml')

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve(strict=True).parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '=3e^#ski%vb9t@bgx9ycbhdh6+n4l-*=fs-5(ql37sq31n5&zh'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = conf['debug_setting']
ALLOWED_HOSTS = conf['allowed_hosts']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'user_auth',
    'circulation',
    'framework',
    'problem',
    'fault',
    'opstools',
    'llm',
    'rest_framework.authtoken'
]

REST_FRAMEWORK = {
    # 认证
    'DEFAULT_AUTHENTICATION_CLASSES': (
        # 采用json格式在web上传输的认证字符串  token认证
        'rest_framework_jwt.authentication.JSONWebTokenAuthentication',
        # 会话认证session认证
        # 'rest_framework.authentication.SessionAuthentication',
        # 基本认证
        # 'rest_framework.authentication.BasicAuthentication',
        # 于permission组合使用 令牌TOKEN认证
        # 'rest_framework.authentication.TokenAuthentication'
    ),
    # 'DEFAULT_PERMISSION_CLASSES': (
    #     'rest_framework.permissions.IsAuthenticated',
    # )
}

JWT_AUTH = {
    # 过期时间
    'JWT_EXPIRATION_DELTA': datetime.timedelta(days=1)
    # 'JWT_EXPIRATION_DELTA': datetime.timedelta(seconds=30)
    # token 前缀
    # 'JWT_AUTH_HEADER_PREFIX': 'Bearer'
}



MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'user_auth.middleware.ExceptionChange'
]

ROOT_URLCONF = 'itmp.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'itmp.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': conf['databases_name'],
        'USER': conf['databases_user'],
        'PASSWORD': conf['databases_pswd'],
        'HOST': conf['databases_host'],
        'PORT': '3306',
        'CHARSET': 'utf8',
        # 'OPTIONS': {'isolation_level': None},
    }
}



DATE_FORMAT = 'Y-m-d'
DATETIME_FORMAT = 'Y-m-d H:i'


# Password validation
# https://docs.djangoproject.com/en/3.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# 使用自定义user_model
AUTH_USER_MODEL = 'user_auth.MyUserInfo'



# DJANGO_CELERY_BEAT_TZ_AWARE = False
# LANGUAGE_CODE = 'en-us'
USE_TZ = False
LANGUAGE_CODE = 'zh-hans'
# TIME_ZONE = 'UTC'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_L10N = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/

STATIC_URL = '/static/'
