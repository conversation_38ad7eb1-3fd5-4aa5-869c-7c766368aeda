"""itmp URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf.urls import url
from django.contrib import admin
from django.urls import path, include
from rest_framework_jwt.views import obtain_jwt_token
# from ..auth import views as auth_views
# from .. import circulation
# from .. import framework
urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/user/', include('user_auth.urls')),
    path('circulation/', include('circulation.urls')),
    path('api/', include('framework.urls')),
    path('api/', include('problem.urls')),
    path('api/', include('fault.urls')),
    path('api/', include('opstools.urls')),
    path('api/', include('llm.urls')),
    url(r'^jwt-auth/', obtain_jwt_token),
]
