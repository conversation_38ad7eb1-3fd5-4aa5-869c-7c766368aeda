import mysql.connector
import hashlib
import os
import django

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'itmp.settings')
django.setup()

from fault.models import RectifyInfo


# +-----------------+---------------+------+-----+---------+----------------+
# | Field           | Type          | Null | Key | Default | Extra          |
# +-----------------+---------------+------+-----+---------+----------------+
# | id              | int(11)       | NO   | PRI | NULL    | auto_increment |
# | faultDescribe   | varchar(256)  | YES  |     | NULL    |                |
# | rectifyDescribe | varchar(256)  | YES  |     | NULL    |                |
# | type            | varchar(256)  | YES  |     | NULL    |                |
# | fault_time      | varchar(256)  | YES  |     | NULL    |                |
# | start_time      | datetime(6)   | YES  |     | NULL    |                |
# | estimate_time   | datetime(6)   | YES  |     | NULL    |                |
# | consuming       | varchar(256)  | YES  |     | NULL    |                |
# | rate            | varchar(256)  | YES  |     | NULL    |                |
# | complete_time   | datetime(6)   | YES  |     | NULL    |                |
# | isDelay         | varchar(256)  | YES  |     | NULL    |                |
# | driver          | varchar(256)  | YES  |     | NULL    |                |
# | department      | varchar(256)  | YES  |     | NULL    |                |
# | fid_id          | varchar(256)  | NO   | MUL | NULL    |                |
# | stage           | varchar(256)  | YES  |     | NULL    |                |
# | delayDate       | datetime(6)   | YES  |     | NULL    |                |
# | fileUrl         | varchar(2048) | YES  |     | NULL    |                |
# +-----------------+---------------+------+-----+---------+----------------+
# 连接到数据库 A
db_a = mysql.connector.connect(
  host="**************",
  user="fmp",
  password="fmp123456#",
  database="faults_sys"
)

# +------------------+--------------+------+-----+---------+----------------+
# | Field            | Type         | Null | Key | Default | Extra          |
# +------------------+--------------+------+-----+---------+----------------+
# | id               | int(11)      | NO   | PRI | NULL    | auto_increment |
# | iid              | varchar(256) | YES  |     | NULL    |                | 1
# | fid              | varchar(256) | YES  |     | NULL    |                | 
# | faultDescribe    | varchar(256) | YES  |     | NULL    |                |
# | rectifyDescribe  | varchar(256) | YES  |     | NULL    |                |
# | type             | varchar(256) | YES  |     | NULL    |                |
# | faultTime        | varchar(256) | YES  |     | NULL    |                |
# | startTime        | datetime(6)  | YES  |     | NULL    |                |
# | estimateTime     | datetime(6)  | YES  |     | NULL    |                |
# | consuming        | varchar(256) | YES  |     | NULL    |                |
# | rate             | varchar(256) | YES  |     | NULL    |                |
# | completeTime     | datetime(6)  | YES  |     | NULL    |                |
# | isDelay          | varchar(256) | YES  |     | NULL    |                |
# | driver           | varchar(256) | YES  |     | NULL    |                |
# | department       | varchar(256) | YES  |     | NULL    |                |
# | stage            | varchar(256) | YES  |     | NULL    |                |
# | fileUrl          | json         | YES  |     | NULL    |                |
# | delayDate        | datetime(6)  | YES  |     | NULL    |                |
# | reportType       | varchar(256) | YES  |     | NULL    |                | 1
# | delayTime        | datetime(6)  | YES  |     | NULL    |                | 1
# | historyDelayTime | json         | YES  |     | NULL    |                | 1
# +------------------+--------------+------+-----+---------+----------------+

# 连接到数据库 B
db_b = mysql.connector.connect(
  host="**************",
  user="itmp_rw",
  password="itmp123456#",
  database="itmp_sys"
)

# 查询数据
print("查询数据:a")
query = "SELECT rectifyDescribe FROM api_v2_rectifyinfo where stage = '已发布' "
cursor_a = db_a.cursor()
cursor_a.execute(query)
result_set = cursor_a.fetchall()
for i in result_set:
    print(i)

print("查询数据:b")
query = "SELECT rectifyDescribe FROM rectify_info where stage = '已发布' "
cursor_b = db_b.cursor()
cursor_b.execute(query)
result_set = cursor_b.fetchall()
for i in result_set:
    print(i)