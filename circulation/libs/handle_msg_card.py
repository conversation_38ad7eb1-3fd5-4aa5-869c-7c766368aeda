import requests
import json
import copy
import datetime
from common.robot.send_message import send_message
from common.robot.get_tenant_access_token import get_tenant_access_token
from common.utils.log import Logger
from common.robot.reply_message import reply_message
from common.robot.config import conf
from common.utils.exception import send_exception
from common.robot.get_larkuser_info import get_larkuser_info
from circulation.models import MsgCard
from .create_remind import create_remind
from .auto_remind import auto_remind
from .auto_update_card import timing_update_card, del_timing_update_card, real_time_update_card, bulk_update_card
from .auto_remind import auto_delete_remind, auto_pause_remind, auto_resume_remind
from .syn_platform import itmp_syn  # itmp_handle, itmp_archive, itmp_appoint, itmp_dev_confirm_solved

logger = Logger.get_logger(__name__, 1)
msg_card_path = "itmp/circulation/template/user_report_fault.json"


class MsgCardHandler:
    """消息卡片回调处理类"""

    @staticmethod
    def handler(data):
        # 处理按钮类操作
        user_id = data["open_id"]  # 卡片操作人id
        message_id = data["open_message_id"]  # 消息卡片id
        chat_id = data["open_chat_id"]  # 消息所在群id
        card_token = data.get("token", "")  # 消息卡片token
        actions = data["action"]["value"]  # 操作动作
        access_token = get_tenant_access_token()  # 机器人token
        option = data["action"].get("option", None)  # 下拉框/时间选择器所选内容
        timezone = data["action"].get("timezone", None)  # 时区
        current_time = datetime.datetime.now()  # 当前时间
        time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
        timestamp = int(current_time.timestamp())
        is_lark_event = data.get("is_lark_event", True)
        logger.info(
            f"[HANDLE_MSG_CARD] user_id={user_id}, message_id={message_id}, card_token={card_token}, actions={actions}")
        operate_type = actions["type"]
        job_id = actions["job_id"]
        # with open(msg_card_path, "r") as json_file:
        #     card = json.load(json_file)

        # 从数据库拉取数据
        get_msgcard_obj = MsgCard.objects.filter(job_id=job_id).first()
        card = json.loads(get_msgcard_obj.content)
        driver_id_list = get_msgcard_obj.driver
        message_id_list = get_msgcard_obj.message_id
        logger.debug(f"[HANDLE_MSG_CARD] MESSAGE_ID_LIST = {message_id_list}")

        # 指派操作
        if operate_type == "appoint":
            if user_id not in driver_id_list:
                code = 1015
                logger.warning(f"[HANDLE_MSG_CARD] code = {code}, Unauthorized operation")
                remind_msg = {
                    'text': f'<at user_id="{user_id}">name</at> 只有当前处理人才可以操作该按钮哦~'}
                reply_message(access_token, message_id, remind_msg)
                return "{}"
            note = card["elements"][-1]["elements"]
            appoint_people_id = option
            # appoint_name = id_to_name(chat_id, appoint_people)
            appoint_people_name = get_larkuser_info(appoint_people_id, id_type="open_id")
            try:
                notice = {
                    "tag": "lark_md",
                    "content": f"{time_str} <at id={user_id}>name</at>将工单指派给了<at id={appoint_people_id}>name</at>\n"
                }
                # 删除认领按钮
                # del card["elements"][2]["actions"][1]
                # new_button = {
                #     "tag": "button",
                #     "text": {
                #         "content": "已解决",
                #         "tag": "plain_text"
                #     },
                #     "type": "danger",
                #     "value": {
                #         "card": "user_report_fault",
                #         "type": "resolved",
                #         "job_id": job_id
                #     }
                # }
                # card["elements"][2]["actions"].append(new_button)

                # 修改当前处理人
                card["elements"][0]["fields"][-1]["text"]["content"] = f'🧑‍🚀 当前处理人：<at id={appoint_people_id}>name</at>'
                note.append(notice)
                card["header"]["template"] = "blue"
                new_message_id = send_message(access_token, appoint_people_id, card)
                message_id_list.append(new_message_id)
                MsgCard.objects.filter(job_id=job_id).update(message_id=message_id_list, content=json.dumps(card),
                                                             driver=[appoint_people_id], status="已指派")
                remind_job_id = create_remind(job_id, new_message_id, [appoint_people_id])
                logger.debug(f"[HANDLE_MSG_CARD] remind_job_id = {remind_job_id}")
                if is_lark_event:
                    # 同步平台
                    itmp_syn(job_id, operate_type, user_id, option)
                # 批量同步更新其他卡片
                bulk_update_card(message_id_list=message_id_list, new_card=card, except_id=message_id)
                logger.info(f"[HANDLE_MSG_CARD] {job_id} appoint success")
                return card
            except Exception as e:
                code = 1014
                logger.error(f"[HANDLE_MSG_CARD] {job_id} appoint error: {e}")
                send_exception(code, f"[HANDLE_MSG_CARD] {job_id} appoint error: {e}")
                return "{}"

        # 抑制操作
        elif operate_type == "restrain":
            if user_id not in driver_id_list:
                code = 1015
                logger.warning(f"[HANDLE_MSG_CARD] code = {code}, Unauthorized operation")
                remind_msg = {
                    'text': f'<at user_id="{user_id}">name</at> 只有当前处理人才可以操作该按钮哦~'}
                reply_message(access_token, message_id, remind_msg)
                return "{}"
            note = card["elements"][-1]["elements"]
            restrain_until = (current_time + datetime.timedelta(hours=int(option))).strftime("%Y-%m-%d %H:%M:%S")
            # datetime_obj = datetime.datetime.strptime(option, "%Y-%m-%d %H:%M %z")
            # formatted_datetime = datetime_obj.strftime("%Y-%m-%d %H:%M:%S")
            # restrain_until = formatted_datetime
            notice = {
                "tag": "lark_md",
                "content": f"{time_str} <at id={user_id}>name</at>将工单提醒抑制到了{restrain_until}\n"
            }
            note.append(notice)
            card["header"]["template"] = "grey"
            MsgCard.objects.filter(job_id=job_id).update(content=json.dumps(card), status="已抑制")
            # 暂停提醒
            remind_job_id = auto_remind(job_id, restrain_until)
            logger.debug(f"[HANDLE_MSG_CARD] remind_job_id = {remind_job_id}")

            # 恢复后更新卡片
            new_card = copy.deepcopy(card)  # 深度复制
            new_notice = {
                "tag": "lark_md",
                "content": f"{restrain_until} <at id={user_id}>name</at> 抑制已到期"
            }
            new_note = new_card["elements"][-1]["elements"]
            new_note.append(new_notice)
            new_card["header"]["template"] = "blue"
            job_id_1 = timing_update_card(message_id_list, new_card, restrain_until)
            logger.debug(job_id_1)
            if is_lark_event:
                # 同步平台
                # restrain_time = f"{time_str} - {restrain_until}"
                itmp_syn(job_id, operate_type, user_id, option, time_str, restrain_until)
            # 批量同步更新其他卡片
            bulk_update_card(message_id_list=message_id_list, new_card=card, except_id=message_id)

            logger.info(f"[HANDLE_MSG_CARD] {job_id} restrain success")
            return card

        # 认领操作
        elif operate_type == "claim":
            note = card["elements"][-1]["elements"]
            notice = {
                "tag": "lark_md",
                "content": f"{time_str} <at id={user_id}>name</at>认领了工单\n"
            }
            try:
                note.append(notice)
                card["header"]["template"] = "blue"

                # 删除认领按钮
                # del card["elements"][1]["actions"][1]
                # del card["elements"][2]["actions"][1]
                # new_button = {
                #     "tag": "button",
                #     "text": {
                #         "content": "已解决",
                #         "tag": "plain_text"
                #     },
                #     "type": "danger",
                #     "value": {
                #         "card": "user_report_fault",
                #         "type": "resolved",
                #         "job_id": job_id
                #     }
                # }
                # card["elements"][2]["actions"].append(new_button)
                # 修改当前处理人
                card["elements"][0]["fields"][-1]["text"][
                    "content"] = f'🧑‍🚀 当前处理人：<at id={user_id}>name</at>'
                if is_lark_event:
                    # 同步平台
                    itmp_syn(job_id, operate_type, user_id, option)
                # 发送给认领人
                new_message_id = send_message(access_token, user_id, card)
                message_id_list.append(new_message_id)
                MsgCard.objects.filter(job_id=job_id).update(message_id=message_id_list, content=json.dumps(card),
                                                             driver=[user_id], status="已认领")
                remind_job_id = create_remind(job_id, new_message_id, [user_id])
                logger.debug(f"[HANDLE_MSG_CARD] job_id={job_id}")
                # 批量同步更新其他卡片
                bulk_update_card(message_id_list=message_id_list, new_card=card, except_id=new_message_id)
                if is_lark_event:
                    # 同步平台
                    itmp_syn(job_id, operate_type, user_id, option)
                # logger.info(f"[HANDLE_MSG_CARD] {job_id} claim success")
                return card
            except Exception as e:
                code = 1014
                logger.error(f"[HANDLE_MSG_CARD] {job_id} claim error: {e}")
                send_exception(code, f"[HANDLE_MSG_CARD] {job_id} claim error: {e}")
                return "{}"

        # 研发确认已解决操作
        elif operate_type == "resolved":
            # url = "http://fmp-test.ttyuyin.com:8765/circulation/api/confirm/"
            # headers = {
            #     'Content-Type': 'application/json',
            # }
            # data = {
            #     "job_id": job_id,
            #     "solver_id": user_id,
            #     "solve_time": time_str
            # }
            # try:
            #     response = requests.post(url, headers=headers, data=json.dumps(data)).json()
            #     logger.debug(response)
            #     # return "{}"
            # except Exception as e:
            #     code = 1014
            #     logger.error(f"[HANDLE_MSG_CARD] {job_id} resolved error: {e}")
            #     send_exception(code, f"[HANDLE_MSG_CARD] {job_id} resolved error: {e}")
            #     return "{}"
            try:
                code, msg_id_list, new_card = dev_confirm_resolved(job_id, user_id, time_str)
                if code != 20000:
                    return "{}"
                logger.debug(new_card)
                # 批量同步更新其他卡片
                bulk_update_card(message_id_list=message_id_list, new_card=new_card, except_id=message_id)
                if is_lark_event:
                    # 同步平台
                    itmp_syn(job_id, operate_type, user_id, option)
                return new_card
            except Exception as e:
                code = 1014
                logger.error(f"[HANDLE_MSG_CARD] {job_id} resolved error: {e}")
                send_exception(code, f"[HANDLE_MSG_CARD] {job_id} resolved error: {e}")
                return "{}"

        # 登记人确认解决操作
        elif operate_type == "confirm_resolve":
            try:
                # 验证登记人
                registrant = get_msgcard_obj.registrant
                if user_id != registrant:
                    logger.warning(f"[HANDLE_MSG_CARD] {user_id} not registrant")
                    # err_msg = f"[HANDLE_MSG_CARD] {user_id} not registrant, can not confirm resolve"
                    # send_exception(1015, err_msg)
                    remind_msg = {
                        'text': f'<at user_id="{user_id}">name</at> 只有登记人才可以操作该按钮哦~'}
                    reply_message(access_token, message_id, remind_msg)
                    return "{}"
                notice = {
                    "tag": "lark_md",
                    "content": f"{time_str} <at id={user_id}>name</at>确认工单解决\n"
                }
                note = card["elements"][-1]["elements"]
                note.append(notice)
                buttons = card["elements"][1]["actions"]
                # 删除无用按钮
                del buttons[1:4]
                card["header"]["template"] = "green"
                MsgCard.objects.filter(job_id=job_id).update(content=json.dumps(card), status="已解决")
                # 删除提醒任务
                auto_delete_remind(job_id)
                # 删除定时更新卡片任务（如有抑制场景）
                del_timing_update_card(message_id)

                remind_msg = {
                    'text': f'🎉工单{job_id}处理完成啦'}
                reply_message(access_token, message_id, remind_msg)

                logger.info(f"[HANDLE_MSG_CARD] {job_id} resolve success")
                if is_lark_event:
                    # 同步平台
                    itmp_syn(job_id, operate_type, user_id, option)
                # 批量同步更新其他卡片
                if len(message_id_list) <= 3:
                    bulk_update_card(message_id_list=message_id_list, new_card=card, except_id=message_id)
                    return card
                else:
                    bulk_update_card(message_id_list=message_id_list, new_card=card)
                # return card
            except Exception as e:
                code = 1014
                logger.error(f"[HANDLE_MSG_CARD] {job_id} confirm resolve error: {e}")
                send_exception(code, f"[HANDLE_MSG_CARD] {job_id} confirm resolve error: {e}")
                return "{}"

        # 登记人驳回工单
        elif operate_type == "not_resolve":
            # 验证登记人
            registrant = get_msgcard_obj.registrant
            if user_id != registrant:
                logger.warning(f"[HANDLE_MSG_CARD] {user_id} not registrant")
                remind_msg = {
                    'text': f'<at user_id="{user_id}">name</at> 只有登记人才可以操作该按钮哦~'}
                reply_message(access_token, message_id, remind_msg)
                return "{}"

            notice = {
                "tag": "lark_md",
                "content": f"{time_str} <at id={user_id}>name</at>驳回了工单\n"
            }
            msgcard_obj = MsgCard.objects.filter(job_id=job_id).first()
            job_id = msgcard_obj.job_id
            # 验证登记人
            registrant = msgcard_obj.registrant
            if user_id != registrant:
                logger.warning(f"[HANDLE_MSG_CARD] {user_id} not registrant")
                return "{}"
            # 获取备份卡片
            backup_card = json.loads(msgcard_obj.backup)
            note = backup_card["elements"][-1]["elements"]
            note.append(notice)
            backup_card["header"]["template"] = "blue"
            MsgCard.objects.filter(job_id=job_id).update(content=json.dumps(backup_card), status="已驳回")
            # 恢复提醒
            auto_resume_remind(message_id)
            access_token = get_tenant_access_token()
            remind_people = ' '.join(f'<at user_id="{open_id}">name</at>' for open_id in driver_id_list)
            remind_msg = {
                'text': f'{remind_people} 登记人驳回了工单，请查看并处理'}
            reply_message(access_token, message_id, remind_msg)
            if is_lark_event:
                # 同步平台
                itmp_syn(job_id, operate_type, user_id, option)
            # 批量同步更新其他卡片
            bulk_update_card(message_id_list=message_id_list, new_card=backup_card, except_id=message_id)

            logger.info(f"[HANDLE_MSG_CARD] {job_id} DISMISS")
            return backup_card
        return "{}"


def dev_confirm_resolved(job_id, solver_id, solve_time):
    # 研发确认工单解决
    msgcard_obj = MsgCard.objects.filter(job_id=job_id).first()
    message_id_list = msgcard_obj.message_id
    registrant_id = msgcard_obj.registrant
    driver_id_list = msgcard_obj.driver
    reply_message_id = message_id_list[0]
    if solver_id not in driver_id_list:
        code = 1015
        logger.warning(f"[DEV_CONFIRM_RESOLVE] code = {code}, Unauthorized operation")
        return code, None, None
    card = json.loads(msgcard_obj.content)
    try:
        # 暂停提醒
        auto_pause_remind(job_id)

        # 添加进度
        note = card["elements"][-1]["elements"]
        notice = {
            "tag": "lark_md",
            "content": f"{solve_time} <at id={solver_id}>name</at>提交工单已解决，等待登记人确认\n"
        }
        note.append(notice)
        card["header"]["template"] = "orange"
        # 备份当前卡片
        MsgCard.objects.filter(job_id=job_id).update(backup=json.dumps(card))

        # 删除定时更新卡片任务（如有抑制场景）
        for message_id in message_id_list:
            del_timing_update_card(message_id)
        # 发消息请登记人确认
        access_token = get_tenant_access_token()
        remind_msg = {
            'text': f'<at user_id="{registrant_id}">name</at> 技术侧已完成工单，请登记人确认是否已解决，并在卡片操作'}
        reply_message(access_token, reply_message_id, remind_msg)
        logger.info(f"[DEV_CONFIRM_RESOLVE] {job_id} wait confirm")
        MsgCard.objects.filter(job_id=job_id).update(content=json.dumps(card), status="待确认")
        code = 20000
        return code, message_id_list, card
    except Exception as e:
        logger.error(f"[DEV_CONFIRM_RESOLVE] {job_id} error: {e}")
        code = 1011
        send_exception(code, f"[DEV_CONFIRM_RESOLVE] {job_id} developer confirm resolve error: {e}")
        return code, None, None
