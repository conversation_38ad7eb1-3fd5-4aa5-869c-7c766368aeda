import requests
from common.robot.get_tenant_access_token import get_tenant_access_token
from common.utils.log import Logger


logger = Logger.get_logger(__name__, 1)


def get_message_content(message_id):
    url = f"https://open.feishu.cn/open-apis/im/v1/messages/{message_id}"
    payload = ''

    headers = {
        'Authorization': 'Bearer ' + get_tenant_access_token()
    }

    response = requests.request("GET", url, headers=headers, data=payload).json()
    logger.debug(response)
    code = response.get("code", -1)
    if code != 0:
        return None
    content = response["data"]["items"][0]["body"]["content"]
    logger.info(type(content))
    return content

