import requests
import json
from common.robot.config import conf
from common.utils.log import Logger
from common.robot.get_tenant_access_token import get_tenant_access_token
from circulation.models import MsgCard

logger = Logger.get_logger(__name__, 1)


def timing_update_card(message_id_list, new_card, update_time):
    # 定时更新卡片
    try:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': conf["jarvis_token"]
        }

        payload = json.dumps(new_card)
        create_scheduler_url = "http://yw-jarvis.ttyuyin.com/api_V1/jobs/create/"
        for message_id in message_id_list:
            # 删除之前的更新任务
            del_timing_update_card(message_id)
            data = {
                "func": "update_msg",
                "trigger": "date",
                "configs": {
                    "run_date": update_time,
                    "args": [message_id, payload]
                },
                "name": f"update_msg_{message_id}"
            }
            response = requests.post(url=create_scheduler_url, headers=headers, data=json.dumps(data)).json()
            logger.debug(response)
            job_id = response.get("job_id", "error")
            logger.info(f"[AUTO_REMIND] JOB_ID = {job_id}, Auto resume remind add success")
        return True
    except Exception as e:
        logger.error(f"[AUTO_REMIND] TIMING UPDATE CARD FAILED: {e}")
        return False


def real_time_update_card(message_id, new_card):
    # 更新消息
    access_token = get_tenant_access_token()
    url = f"https://open.feishu.cn/open-apis/im/v1/messages/{message_id}"
    headers = {
        'Content-Type': 'application/json; charset=utf-8',
        'Authorization': "Bearer " + access_token
    }
    payload = json.dumps({
        "content": json.dumps(new_card)
    })
    response = requests.patch(url=url, headers=headers, data=payload).json()
    code = response.get("code", -1)
    if code != 0:
        logger.error(f"[REAL_TIME_UPDATE_CARD] UPDATE ERROR: CODE={code}, msg={response.get('msg')}")
        return False
    else:
        logger.info("[REAL_TIME_UPDATE_CARD] UPDATE SUCCESS")
        logger.debug(f"[更新消息飞书返回值] {response}")
        return True


def del_timing_update_card(message_id):
    # 删除原有的定时update任务
    headers = {
        'Content-Type': 'application/json',
        'Authorization': conf["jarvis_token"]
    }
    payload = ""
    delete_url = f"http://yw-jarvis.ttyuyin.com/api_V1/jobs/update_msg_{message_id}/delete/"
    delete_response = requests.delete(url=delete_url, headers=headers, data=payload).json()
    logger.info(f"[DEL_TIMING_UPDATE_CARD] RESPONSE = {delete_response}")
    return delete_response


def bulk_update_card(message_id_list, new_card, except_id=None):
    # 批量更新卡片
    try:
        for message_id in message_id_list:
            if message_id != except_id:
                real_time_update_card(message_id, new_card)
        logger.info("[BULK_UPDATE_CARD] BULK UPDATE ALL SUCCESS")
        return True
    except Exception as e:
        logger.error(f"[BULK_UPDATE_CARD] BULK UPDATE FAILED: {e}")
        return False

