from wsgiref import headers

import requests
import json
from common.utils.log import Logger
from common.utils.config import itmp_conf
from .get_group_members import get_group_members
from common.utils.exception import send_exception
from common.robot.get_larkuser_info import get_larkuser_info

URL = itmp_conf["itmp_url"]
logger = Logger.get_logger(__name__, 1)


def id_to_name(chat_id, user_id):
    # id转名字 / 废弃，改用飞书接口获取
    members_dict = get_group_members(chat_id)
    # logger.debug(members_dict)
    try:
        user_name = members_dict[user_id]
        return user_name
    except Exception as e:
        logger.error(f"[ID_TO_NAME] GET NEME ERROR: {e}")
        code = 1013
        send_exception(1013, f"[ID_TO_NAME] GET NEME ERROR: {e}")
        return False


def itmp_handle(job_id, handler_id, response_timestamp):
    # 认领工单
    url = URL + "/api/feedback/handleTicket/"
    # url = URL + "/api/feedback/handle/"
    headers = {
        "Content-Type": "application/json"
    }
    # 处理人信息
    handler_info = get_larkuser_info(handler_id, id_type="open_id")
    handler_name = handler_info[0]
    payload = {
        "data": {
            "ticketID": job_id,
            "respondent": {"user_name": handler_name, "user_id": handler_id},
            "responseTime": response_timestamp
        }
    }
    try:
        response = requests.post(url=url, headers=headers, json=payload).json()
        logger.debug(response)
        logger.info(f"[ITMP_HANDLE] {job_id} handling")
        return True
    except Exception as e:
        logger.error(f"[ITMP_HANDLE] Change status error: {e}")
        send_exception(1011, f"[ITMP_HANDLE] Change status error: {e}")
        return False


def itmp_appoint(job_id, appoint_id, handler_id, response_timestamp):
    # 指派工单
    url = URL + "/api/feedback/assignTicket/"
    headers = {

    }
    # 指派人信息
    appoint_info = get_larkuser_info(appoint_id, id_type="open_id")
    appoint_name = appoint_info[0]
    # 被指派（处理人）信息
    handler_info = get_larkuser_info(handler_id, id_type="open_id")
    handler_name = handler_info[0]
    payload = {
        "data": {
            "ticketID": job_id,
            "assignor": {"user_name": appoint_name, "user_id": appoint_id},
            "assignee": {"user_name": handler_name, "user_id": handler_id},
            "responseTime": response_timestamp
        }
    }
    try:
        response = requests.post(url=url, headers=headers, json=payload).json()
        logger.debug(response)
        logger.info(f"[ITMP_HANDLE] {job_id} handling")
        return True
    except Exception as e:
        logger.error(f"[ITMP_HANDLE] Appoint error: {e}")
        send_exception(1011, f"[ITMP_HANDLE] Appoint error: {e}")
        return False


def itmp_archive(job_id):
    # 客服确认处理归档工单
    url = URL + "/api/feedback/checkTicket/"
    # url = URL + "/api/feedback/complete/"
    headers = {

    }
    payload = {
        "data": {
            "ticketID": job_id
        }
    }
    try:
        response = requests.post(url=url, headers=headers, json=payload).json()
        logger.debug(response)
        logger.info("[ITMP_ARCHIVE] Archive success")
        return True
    except Exception as e:
        logger.error(f"[ITMP_ARCHIVE] Archive error: {e}")
        send_exception(1011, f"[ITMP_ARCHIVE] Archive error: {e}")
        return False


def itmp_dev_confirm_solved(job_id, handler_id, response_timestamp):
    # 研发补充处理结果
    url = URL + "/api/feedback/completeTicket/"
    headers = {

    }
    # 处理人信息
    handler_info = get_larkuser_info(handler_id, id_type="open_id")
    handler_name = handler_info[0]
    payload = {
        "data": {
            "ticketID": job_id,
            "devProcessor": {"user_name": handler_name, "user_id": handler_id},
            "cause": "",
            "resolutionTime": response_timestamp
        }
    }
    try:
        response = requests.post(url, headers=headers, json=payload).json()
        logger.debug(response)
        logger.info("[ITMP_DEV_CONFIRM_SOLVED] Change status success")
        return True
    except Exception as e:
        logger.error(f"[ITMP_DEV_CONFIRM_SOLVED] Change status error: {e}")
        send_exception(1011, f"[ITMP_DEV_CONFIRM_SOLVED] Change status error: {e}")
        return False


def itmp_syn(ticket_id, action, operater_id, assignor_id="", start_time="", end_time=""):
    url = URL + "/api/feedback/cardupdate/"
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "data": {
            "ticketID": ticket_id,
            "operator": operater_id,
            "action": action,
            "assignor": assignor_id,
            "start_time": start_time,
            "end_time": end_time
        }
    }
    logger.debug(f"[ITMP_SYN] payload: {payload}")
    try:
        response = requests.post(url, headers=headers, json=payload).json()
        logger.debug(f"[ITMP_SYN] Response = {response}")
        code = response.get("code", -1)
        if code != 20000:
            logger.error(f"[ITMP_SYN] Platform response error, code={code}, msg={response.get('msg', 'Unknown error')}")
            send_exception(1011, f"[ITMP_SYN] Platform response error, code={code}")
            return False
        logger.info(f"[ITMP_SYN] Syn platform success")
        return True
    except Exception as e:
        logger.error(f"[ITMP_SYN] Syn platform error: {e}")
        send_exception(1011, f"[ITMP_SYN] Syn platform error: {e}")
        return False

