import requests
import json
from common.robot.config import conf
from common.utils.log import Logger

logger = Logger.get_logger(__name__, 1)
remind_frequency = 15


def create_remind(order_id, message_id, current_process_id_list):
    # 创建工单提醒
    headers = {
        'Content-Type': 'application/json',
        'Authorization': conf["jarvis_token"]
    }
    # 删除原有提醒（如有）
    try:
        delete_url = f"http://yw-jarvis.ttyuyin.com/api_V1/jobs/itmp_normal_remind_{order_id}/delete/"
        response = requests.delete(url=delete_url, headers=headers)
        logger.debug(response)
    except Exception as e:
        logger.error(f"[CREATE_REMIND] Error: {e}")

    create_scheduler_url = "http://yw-jarvis.ttyuyin.com/api_V1/jobs/create/"
    current_process_id = ' '.join(f'<at user_id=\\"{x}\\">name</at>' for x in current_process_id_list if x)
    data = {
        "func": "auto_reply_message",
        "trigger": "interval",
        "configs": {
            "minutes": remind_frequency,
            "args": [message_id, f'{{"text": "{current_process_id} 该问题还未处理完成哦，请及时处理~"}}']
        },
        "name": "itmp_normal_remind_" + order_id
    }
    response = requests.post(url=create_scheduler_url, headers=headers, data=json.dumps(data)).json()
    logger.debug(response)
    job_id = response.get("job_id", "error")
    logger.info(f"[CREATE_REMIND] JOB_ID = {job_id}")
    return job_id
