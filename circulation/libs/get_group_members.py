import requests
import json
from common.utils.log import Logger
from common.robot.get_tenant_access_token import get_tenant_access_token

logger = Logger.get_logger(__name__, 1)


def get_group_members(chat_id):
    url = f"https://open.feishu.cn/open-apis/im/v1/chats/{chat_id}/members?member_id_type=open_id&page_size=100"
    payload = ''
    access_token = get_tenant_access_token()
    headers = {
        'Authorization': 'Bearer ' + access_token
    }

    response = requests.get(url, headers=headers, data=payload).json()
    code = response.get("code", -1)
    if code != 0:
        msg = response.get('msg', '')
        logger.error(f"[GET_GROUP_INFO] Error: {msg}")
        return None
    members_info = response["data"]["items"]
    members_dict = {}
    for member in members_info:
        member_id = member["member_id"]
        member_name = member["name"]
        members_dict.update({member_id: member_name})
    members_dict.update({"cli_a1d8686d873a500e": "<PERSON>"})
    return members_dict
