import requests
import json
from common.robot.config import conf
from common.utils.log import Logger

logger = Logger.get_logger(__name__, 1)


def auto_remind(order_id, resume_time):
    # 自动工单提醒（抑制操作）
    headers = {
        'Content-Type': 'application/json',
        'Authorization': conf["jarvis_token"]
    }
    # 暂停提醒
    payload = ""
    pause_url = f"http://yw-jarvis.ttyuyin.com/api_V1/jobs/itmp_normal_remind_{order_id}/pause/"
    response = requests.get(url=pause_url, headers=headers, data=payload)
    logger.info(f"[AUTO_REMIND] {order_id} remind paused")
    logger.debug(response)

    # 自动定时恢复提醒
    create_scheduler_url = "http://yw-jarvis.ttyuyin.com/api_V1/jobs/create/"
    data = {
        "func": "auto_resume_job",
        "trigger": "date",
        "configs": {
            "run_date": resume_time,
            "args": [f"itmp_normal_remind_{order_id}"]
        }
    }
    response = requests.post(url=create_scheduler_url, headers=headers, data=json.dumps(data)).json()
    logger.debug(response)
    job_id = response.get("job_id", "error")
    logger.info(f"[AUTO_REMIND] JOB_ID = {job_id}, Auto resume remind add success")
    return job_id


def auto_delete_remind(job_id):
    # 自动工单提醒（解决操作）
    headers = {
        'Content-Type': 'application/json',
        'Authorization': conf["jarvis_token"]
    }
    # 删除提醒
    payload = ""
    delete_url = f"http://yw-jarvis.ttyuyin.com/api_V1/jobs/itmp_normal_remind_{job_id}/delete/"
    response = requests.delete(url=delete_url, headers=headers, data=payload)
    logger.info(f"[AUTO_REMIND] {job_id} remind deleted")
    logger.debug(response)


def auto_pause_remind(job_id):
    # 暂停提醒（待确认已解决）
    headers = {
        'Content-Type': 'application/json',
        'Authorization': conf["jarvis_token"]
    }
    # 暂停提醒
    payload = ""
    pause_url = f"http://yw-jarvis.ttyuyin.com/api_V1/jobs/itmp_normal_remind_{job_id}/pause/"
    response = requests.get(url=pause_url, headers=headers, data=payload)
    logger.info(f"[AUTO_REMIND] {job_id} remind paused")
    logger.debug(response)


def auto_resume_remind(job_id):
    # 恢复提醒（登记人驳回）
    headers = {
        'Content-Type': 'application/json',
        'Authorization': conf["jarvis_token"]
    }
    # 暂停提醒
    payload = ""
    resume_url = f"http://yw-jarvis.ttyuyin.com/api_V1/jobs/itmp_normal_remind_{job_id}/resume/"
    response = requests.get(url=resume_url, headers=headers, data=payload)
    logger.info(f"[AUTO_REMIND] {job_id} remind resumed")
    logger.debug(response)
