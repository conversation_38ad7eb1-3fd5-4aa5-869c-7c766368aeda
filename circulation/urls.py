"""fault_sys URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path, include
from django.shortcuts import redirect
from . import views
from rest_framework.routers import DefaultRouter

router = DefaultRouter()

urlpatterns = [
    # path('', views.hello),
    path('api/send_report/', views.ApiRequestHandler.send_report, name='send_report'),
    path('api/card_operate/', views.ApiRequestHandler.handle_card, name='handle_card'),
    path('api/confirm/', views.ApiRequestHandler.confirm_resolve, name='confirm_resolve'),
    path('api/recall/', views.ApiRequestHandler.recall_job, name='recall_job'),
    path('api/update_card/', views.ApiRequestHandler.update_card, name='update_card'),
    path('api/syn_card/', views.ApiRequestHandler.syn_card, name='syn_card'),
]
