# 已废除
import json
import requests
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.decorators import api_view
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from framework.models import CustomersFeedbackInfo
from user_auth.models import MyUserInfo
from common.utils.config import itmp_conf
from common.utils.utils import return_code
from common.utils.log import Logger
from common.robot.get_tenant_access_token import get_tenant_access_token
from common.robot.send_message import send_message
from common.robot.reply_message import reply_message
from common.robot.recall_message import recall_message
from common.robot.config import conf as robot_conf
from common.utils.exception import send_exception
from .libs.handle_msg_card import MsgCardH<PERSON><PERSON>, dev_confirm_resolved
from .libs.create_remind import create_remind
from .libs.auto_remind import auto_delete_remind, auto_pause_remind
from .libs.auto_update_card import real_time_update_card, del_timing_update_card, bulk_update_card
from .models import MsgCard

logger = Logger.get_logger(__name__, 1)
itmp_link = itmp_conf["itmp_url"]
msg_card_path = itmp_conf["feedback_card_path"]  # "/data/itmp/itmp/circulation/template/user_report_fault.json"
dev_chat_id = "oc_7adc3624a795ead71e2528ee01bedb32"


@method_decorator(csrf_exempt, name='dispatch')
class ApiRequestHandler(APIView):
    permission_classes = (AllowAny,)

    @staticmethod
    @api_view(['POST', 'GET'])
    def send_report(request):
        data = request.data
        # 工单id
        job_id = data["job_id"]
        logger.debug(f"[SEND_REPORT] request_data = {data}")
        # 故障描述
        describe = data["describe"]
        # 紧急程度
        severityLevel = data["severityLevel"]
        # 用户ttid
        tt_id_list = data["tt_id"]
        tt_id = '、'.join(tt_id_list)
        # 反馈时间
        # feedback_time_list = data.get("feedback_time", "未填写")
        # feedback_time = '、'.join(feedback_time_list)
        # 故障时间
        fault_time = '、'.join(data.get("fault_time", "未填写")) if data.get("fault_time") else "未填写"
        # fault_time_list = data.get("fault_time", "未填写")
        # if fault_time_list != "未填写":
        #     fault_time = '、'.join(fault_time_list)
        # else:
        #     fault_time = "未填写"
        # 工单创建时间
        create_time = data.get("create_time")
        # 用户设备
        device_list = data.get("device", "未填写")
        device = '、'.join(device_list)
        # 用户APP
        app_type_list = data.get("app_type", "未填写")
        app_type = '、'.join(app_type_list)
        # 用户版本
        app_version_list = data.get("app_version", "未填写")
        app_version = '、'.join(app_version_list)
        # 功能模块
        function = data.get("function", "未填写")
        # 工单链接
        report_url = data.get("url", itmp_link)
        # 截图/录屏
        photo_list = data.get("photo_url", [])
        if photo_list:
            photo_url = '，'.join(f"[截图/录屏{i + 1}]({x})" for i, x in enumerate(photo_list))
        else:
            photo_url = "未填写"
        # 处理群
        process_group = data.get("feishu_group")
        process_group_id = data.get("feishu_group_id")
        process_group_card = f"[{process_group}]({process_group_id})"
        # 登记人
        registrant = data.get("registrant", "未识别")
        if "ou_" not in registrant:
            registrant_id = f"{registrant}"
        else:
            registrant_id = f"<at id={registrant}>name</at>"
        # 研发负责人
        developer_list = data.get("developer_id_list", [])
        developer_id = ' '.join(f"<at id={x}>name</at>" for x in developer_list if x)
        # 运维负责人
        operator_list = data.get("operator_id_list", [])
        operator_id = ' '.join(f"<at id={x}>name</at>" for x in operator_list if x)

        # 当前处理人列表
        driver_id_list = developer_list + operator_list
        driver_id = ' '.join(f"<at id={x}>name</at>" for x in driver_id_list if x)

        # 当前工单状态
        stage = data.get('stage')
        # 消息卡片
        logger.info("[START CARED ETID]")
        with open(msg_card_path, "r") as json_file:
            card = json.load(json_file)
            logger.info("[SEND_REPORT] CARD LOAD SUCCESS")
        card_report_details = card["elements"][0]["fields"]
        report_details = [describe, severityLevel, tt_id, create_time, fault_time, device, app_type, app_version, function, process_group_card, photo_url,
                          developer_id, operator_id, registrant_id, driver_id, stage]
        # for i in range(len(card_report_details)):
        for i, detail in enumerate(card_report_details):
            content = card_report_details[i]["text"]["content"]
            card["elements"][0]["fields"][i]["text"]["content"] = content.format(text=report_details[i])

        # 每个按钮写入工单id
        # action_buttons_line = card["elements"]
        # action_buttons_line1 = card["elements"][1]
        # action_buttons_line2 = card["elements"][2]
        # for i in range(len(action_buttons_line1)):
        #     card["elements"][1]["actions"][i]["value"]["job_id"].format(text=job_id)
        # for i in range(len(action_buttons_line2)):
        #     card["elements"][2]["actions"][i]["value"]["job_id"].format(text=job_id)
        for j in range(len(card["elements"][1:-1])):
            action_buttons_line = card["elements"][j + 1]["actions"]
            for i in range(len(action_buttons_line)):
                if "value" in card["elements"][j + 1]["actions"][i]:
                    _id = card["elements"][j + 1]["actions"][i]["value"]["job_id"]
                    card["elements"][j + 1]["actions"][i]["value"]["job_id"] = _id.format(text=job_id)

        # 添加链接
        # details = card["elements"][2]["actions"][0]["url"]
        # card["elements"][2]["actions"][0]["url"] = details.format(url=report_url)     
        details = card["elements"][1]["actions"][0]["url"]
        card["elements"][1]["actions"][0]["url"] = details.format(url=report_url)
        card["header"]["title"]["content"] = card["header"]["title"]["content"].format(text=app_type)
        note = card["elements"][-1]["elements"]
        if "ou_" not in registrant:
            notice = {
                "tag": "lark_md",
                "content": f"{create_time} {registrant} 创建了工单，工单ID: {job_id}\n"
            }
        else:
            notice = {
                "tag": "lark_md",
                "content": f"{create_time} <at id={registrant}>name</at>创建了工单，工单ID: {job_id}\n"
            }
        note.append(notice)
        access_token = get_tenant_access_token()
        logger.info("[START SNED MESSAGE]")

        try:

            # send_message_id = send_message(access_token, process_group, card)
            # if not send_message_id.startswith("om_"):
            #     code = 1010
            #     msg = {
            #         "header": {
            #             "code": code,
            #             "msg": "failed"
            #         },
            #         "details": f"{send_message_id}"
            #     }
            #     send_exception(code, f"[SEND_REPORT] Card send error: {send_message_id}")
            #     return Response(msg)
            send_message_id_list = []
            for i in driver_id_list:
                send_message_id = send_message(access_token, i, card)
                if not send_message_id.startswith("om_"):
                    code = 1010
                    msg = {
                        "header": {
                            "code": code,
                            "msg": "failed"
                        },
                        "details": f"{send_message_id}"
                    }
                    send_exception(code, f"[SEND_REPORT] Card send error: {send_message_id}")
                    return Response(msg)
                else:
                    send_message_id_list.append(send_message_id)

            logger.info("[SEND MESSAGE SUCCESS]")
            current_time = datetime.now()
            timestamp = int(current_time.timestamp())

            # 添加定时提醒
            remind_job_id = create_remind(job_id, send_message_id, driver_id_list)

            # msg_card_obj = MsgCard(job_id=job_id, chat_id=process_group, message_id=[send_message_id],
                                #    content=json.dumps(card), driver=driver_id_list, registrant=registrant,
                                #    create_time=timestamp, status="已创建")
            msg_card_obj = MsgCard(job_id=job_id, chat_id=process_group_id, message_id=send_message_id_list,
                                   content=json.dumps(card), driver=driver_id_list, registrant=registrant,
                                   create_time=timestamp, status="已创建")
            msg_card_obj.save()
            logger.info("[SEND_REPORT] REPORT SEND SUCCESS")
            msg = {
                "header": {
                    "code": 20000,
                    "msg": "success"
                },
                "details": "发送成功",
                "message_id": send_message_id
            }
            return Response(msg)
        except Exception as e:
            logger.error("[SEND_REPORT] REPORT SEND FAILED: %s", e)
            code = 1010
            msg = {
                "header": {
                    "code": code,
                    "msg": "failed"
                },
                "details": f"{e}",
            }
            send_exception(code, f"[SEND_REPORT] {job_id} send card error: {e}")
            return Response(msg)

    @staticmethod
    @api_view(['POST', 'GET'])
    def update_card(request):
        """
        更新卡片
        """
        data = request.data
        logger.debug("[UPDATE_CARD] request_data = %s", data)
        try:
            user_id = data["operate_id"]  # 操作人open_id
            job_id = data["job_id"]  # 工单ID
            # 故障描述
            describe = data.get("describe", None)
            # 紧急程度
            severityLevel = data.get("severityLevel", '未填写')
            # 用户ttid
            tt_id_list = data.get("tt_id", None)
            tt_id = '、'.join(tt_id_list)
            # 反馈时间
            # feedback_time_list = data.get("feedback_time", "未填写")
            # feedback_time = '、'.join(feedback_time_list)
            # 故障时间
            fault_time = '、'.join(data.get("fault_time", "未填写")) if data.get("fault_time") else "未填写"
            # 工单创建时间
            create_time = data.get("create_time")
            # 用户设备
            device_list = data.get("device", "未填写")
            device = '、'.join(device_list)
            # 用户APP
            app_type_list = data.get("app_type", "未填写")
            app_type = '、'.join(app_type_list)
            # 用户版本
            app_version_list = data.get("app_version", "未填写")
            app_version = '、'.join(app_version_list)
            # 功能模块
            function = data.get("function", "未填写")
            # 截图/录屏
            photo_list = data.get("photo_url", [])
            logger.debug("[UPDATE_CARD] PHOTO_LIST=%s", photo_list)
            if photo_list:
                photo_url = '，'.join(f"[截图/录屏{i + 1}]({x})" for i, x in enumerate(photo_list))
            else:
                photo_url = "未填写"
            # 处理群
            process_group = data.get("feishu_group")
            process_group_id = data.get("feishu_group_id", "")
            if process_group_id == "":
                process_group_card = f"{process_group}"
            else:
                process_group_card = f"[{process_group}]({process_group_id})"
            # 获取当前时间
            current_time = datetime.now()  # 当前时间
            time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")

            # 拉取数据库卡片数据
            get_msgcard_obj = MsgCard.objects.filter(job_id=job_id).first()
            card = json.loads(get_msgcard_obj.content)
            driver_id_list = get_msgcard_obj.driver
            message_id_list = get_msgcard_obj.message_id

            card_report_details = card["elements"][0]["fields"]
            report_details = [describe, severityLevel, tt_id, create_time, fault_time, device, app_type, app_version, function, photo_url, process_group_card]
            logger.debug("[UPDATE_CARD] REPORT_DETAILS=%s", report_details)
            # for i, detail in enumerate(card_report_details):
            for i in range(len(card_report_details)-5):
                content = card_report_details[i]["text"]["content"]
                parts = content.split('**', 2)
                if len(parts) > 2:
                    parts[2] = '{text}'
                    content = '**'.join(parts)
                card["elements"][0]["fields"][i]["text"]["content"] = content.format(text=report_details[i])
            # stage
            stage = data.get('stage')
            card["elements"][0]["fields"][-1]["text"]["content"] = f"👉 当前工单状态：{stage}"

            logger.info("[UPDATE_CARD] Info update success")
            # 增加日志
            note = card["elements"][-1]["elements"]
            if "ou_" not in user_id:
                notice = {
                    "tag": "lark_md",
                    "content": f"{time_str} {user_id} 更新了工单信息\n"
                }
            else:
                notice = {
                    "tag": "lark_md",
                    "content": f"{time_str} <at id={user_id}>name</at> 更新了工单信息\n"
                }
            note.append(notice)
            logger.info("[UPDATE_CARD] note update success")
            # 同步卡片到数据库
            MsgCard.objects.filter(job_id=job_id).update(content=json.dumps(card))
            # 批量更新卡片
            bulk_update_card(message_id_list=message_id_list, new_card=card, except_id=None)
            logger.info("[UPDATE_CARD] All message card update success")
            access_token = get_tenant_access_token()
            # 发送提醒
            remind_people = ' '.join(f'<at user_id="{open_id}">name</at>' for open_id in driver_id_list)
            remind_msg = {'text': f'{remind_people} 工单{job_id} 反馈信息已更新，请及时查看'}
            # for msg_id in message_id_list:
            reply_message(access_token, message_id_list[-1], remind_msg)
            msg = {
                "header": {
                    "code": 20000,
                    "msg": "success"
                },
                "details": "更新成功"
            }
            return Response(msg)
        except Exception as e:
            logger.error(f"[UPDATE_CARD] Error: %s", e)
            send_exception(1011, f"[UPDATE_CARD] Error: %s", e)
            msg = {
                "header": {
                    "code": 1011,
                    "msg": "success"
                },
                "details": f"更新失败：{e}"
            }
            return Response(msg)

    @staticmethod
    @api_view(['POST', 'GET'])
    def syn_card(request):
        """
        同步平台变更到卡片
        """
        request_data = request.data
        logger.info(f"[SYN_CARD] request_data = %s", request_data)
        data = request_data["data"]
        try:
            user_id = data["open_id"]  # 操作人open_id
            job_id = data["job_id"]  # 工单ID
            option = data.get("option", "")  # 选项
            action = data["action"]  # 操作类型

            # 拉取数据库卡片数据
            get_msgcard_obj = MsgCard.objects.filter(job_id=job_id).first()
            card = json.loads(get_msgcard_obj.content)
            driver_id_list = get_msgcard_obj.driver
            message_id_list = get_msgcard_obj.message_id
            origin_message_id = message_id_list[0]
            handle_chat_id = get_msgcard_obj.chat_id

            # 模拟一个飞书按钮请求
            handle_url = itmp_conf["ITMP_card_url"]
            # handle_url = "http://fmp-test.ttyuyin.com:8765/circulation/api/card_operate/"

            payload = {
                "open_id": user_id,
                "open_chat_id": handle_chat_id,
                "open_message_id": origin_message_id,
                "action": {
                    "value": {
                        "card": "user_report_fault",
                        "job_id": job_id,
                        "type": action
                    },
                    "tag": "",
                    "option": option
                },
                "is_lark_event": False
            }
            headers = {
                "Content-Type": "application/json"
            }
            response = requests.post(url=handle_url, headers=headers, data=json.dumps(payload)).json()
            logger.debug(f"[SYN_CARD] Handle_card response={response}, type={type(response)}")
            if response != "{}":
                real_time_update_card(origin_message_id, response)
            msg = {
                "header": {
                    "code": 20000,
                    "msg": "success"
                },
                "details": "同步成功"
            }
            return Response(msg)
        except Exception as e:
            logger.error(f"[UPDATE_CARD] Error: {e}")
            send_exception(1014, f"[UPDATE_CARD] Error: {e}")
            msg = {
                "header": {
                    "code": 1014,
                    "msg": "success"
                },
                "details": f"同步失败：{e}"
            }
            return Response(msg)

    @staticmethod
    @api_view(['POST', 'GET'])
    def handle_card(request):
        # 处理消息卡片动作
        data = request.data
        logger.info(f"[HANDLE_CARD] request_data = {data}")
        action_tag = data["action"]["tag"]  # 获取操作类型
        logger.info(f"[MSG_CARD_REQUEST] Action_tag = {action_tag}")
        return_msg = MsgCardHandler.handler(data)
        return Response(return_msg)

    @staticmethod
    @api_view(['POST', 'GET'])
    def confirm_resolve(request):
        # 研发确认工单解决，待客服确认
        data = request.data
        solver_id = data["solver_id"]
        solve_time = data["solve_time"]
        job_id = data["job_id"]
        try:
            code, message_id_list, new_card = dev_confirm_resolved(job_id, solver_id, solve_time)

            if code != 0:
                msg = {
                    "header": {
                        "code": code,
                        "msg": "failed"
                    },
                    "details": "提交失败"
                }
                return Response(msg)

            # 批量同步更新其他卡片
            bulk_update_card(message_id_list=message_id_list, new_card=new_card, except_id=None)
            msg = {
                "header": {
                    "code": 0,
                    "msg": "success"
                },
                "details": "提交成功"
            }
            return Response(msg)
        except Exception as e:
            logger.error(f"[RESOLVE_ISSUE] {job_id} error: {e}")
            code = 1011
            msg = {
                "header": {
                    "code": code,
                    "msg": "failed"
                },
                "details": f"{e}"
            }
            send_exception(code, f"[RESOLVE_ISSUE] {job_id} resolve error: {e}")

        return Response(msg)

    @staticmethod
    @api_view(['POST', 'GET'])
    def recall_job(request):
        # 撤回工单
        data = request.data
        job_id = data.get("job_id")
        try:
            msgcard_obj = MsgCard.objects.filter(job_id=job_id).first()
            message_id_list = msgcard_obj.message_id
            driver_id_list = msgcard_obj.driver
            chat_id = msgcard_obj.chat_id
            if driver_id_list != [] and driver_id_list is not None:
                accesstoken = get_tenant_access_token()
                remind_people = ' '.join(f'<at user_id="{open_id}">name</at>' for open_id in driver_id_list)
                msg = {'text': f'{remind_people} 工单{job_id} 已被登记人撤回'}
                send_message(accesstoken, chat_id, msg)
            for message_id in message_id_list:
                recall_message(message_id)
                del_timing_update_card(message_id)
            auto_delete_remind(job_id)
            msgcard_obj.delete()
            msg = {
                "header": {
                    "code": 20000,
                    "msg": "success"
                },
                "details": "撤回成功"
            }
            return Response(msg)
        except Exception as e:
            code = 1012
            msg = {
                "header": {
                    "code": code,
                    "msg": "failed"
                },
                "details": str(e)
            }
            send_exception(code, f"[RECALL_REPORT] {job_id} recall error: {e}")
            return Response(msg)
