from django.db import models

# Create your models here.


class MsgCard(models.Model):
    job_id = models.CharField(max_length=255, null=True)       # 工单id
    chat_id = models.Char<PERSON>ield(max_length=255)       # 消息卡片发送的群
    message_id = models.JSONField(max_length=255)       # 消息卡片id
    content = models.TextField(null=True)        # 消息卡片内容
    backup = models.TextField(null=True)     # 上一次操作消息卡片内容（回滚用）
    driver = models.JSONField(max_length=255, null=True)  # 当前处理人
    # driver = models.Char<PERSON>ield(max_length=50, null=True)     # 当前处理人
    registrant = models.Char<PERSON>ield(max_length=50, null=True)     # 登记人
    create_time = models.CharField(max_length=50)   # 消息卡片发送时间
    status = models.CharField(max_length=50)        # 工单当前状态

    class Meta:
        # 指定数据表的表名
        db_table = 'msgcard_details'
