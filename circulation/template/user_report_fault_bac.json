{"config": {"wide_screen_mode": true, "update_multi": true}, "elements": [{"tag": "div", "fields": [{"is_short": false, "text": {"tag": "lark_md", "content": "**📌 问题描述：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "**📌 用户TTID：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "**📅 创建时间：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "**📅 发生时间：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "**📱 用户设备：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "**📱 APP类型：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "**📱 APP版本：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "**📱 功能模块：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "**🖼️ 截图/录屏：**{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "👨‍💻 研发负责人：{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "👨‍💻 运维负责人：{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "🧑‍💼 登记人：{text}"}}, {"is_short": false, "text": {"tag": "lark_md", "content": "🧑‍🚀 当前处理人：{text}"}}]}, {"tag": "action", "actions": [{"tag": "select_person", "placeholder": {"tag": "plain_text", "content": "工单指派"}, "value": {"card": "user_report_fault", "type": "appoint", "job_id": "{text}"}, "options": []}, {"tag": "select_static", "placeholder": {"tag": "plain_text", "content": "抑制提醒"}, "value": {"card": "user_report_fault", "type": "restrain", "job_id": "{text}"}, "options": [{"text": {"tag": "plain_text", "content": "2小时"}, "value": "2"}, {"text": {"tag": "plain_text", "content": "6小时"}, "value": "6"}, {"text": {"tag": "plain_text", "content": "12小时"}, "value": "12"}, {"text": {"tag": "plain_text", "content": "24小时"}, "value": "24"}, {"text": {"tag": "plain_text", "content": "48小时"}, "value": "48"}]}]}, {"actions": [{"tag": "button", "text": {"content": "查看详情", "tag": "plain_text"}, "type": "primary", "url": "{url}"}, {"tag": "button", "text": {"content": "认领", "tag": "plain_text"}, "type": "primary", "value": {"card": "user_report_fault", "type": "claim", "job_id": "{text}"}}], "tag": "action"}, {"tag": "note", "elements": []}], "header": {"template": "red", "title": {"content": "📣【{text}】有新的用户报障啦，请快速响应！", "tag": "plain_text"}}}