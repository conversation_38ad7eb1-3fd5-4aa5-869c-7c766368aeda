import json
import time
import jwt
from user_auth.models import MyUserInfo
from django.contrib.auth.models import User
from rest_framework import authentication
from rest_framework import exceptions
from common.utils.log import Logger
from common.utils.utils import return_code
from rest_framework_jwt.utils import jwt_decode_handler


set_log = Logger.get_logger(__name__, 3)

class MyTokenAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        set_log.info(f"+"*100)
        set_log.info(f"[MyTokenAuthentication] 开始Token验证")
        # token = request.META.get('X-Auth-Token')
        # set_log.debug(f"[token] {token}")
        token = request.headers.get("Authorization")
        if token[6] == ' ':
            token = token[7:]
        set_log.debug(f"[token] {token}")
        if not token:
            set_log.info(f"EMPTY TOKEN")
            # return None
            raise exceptions.AuthenticationFailed(return_code(2003))
        try:
            username = jwt_decode_handler(token)
            set_log.debug(f"[username] {username}")

            user = MyUserInfo.objects.filter(email=username['email'], not_send="False")[0]
            # user = User.objects.get(pk=jwt_decode_handler(token)['user_id'])
            set_log.debug(f"[user] {user}")
            if user.is_active:
                return (user, None)
            else:
                raise exceptions.AuthenticationFailed(return_code(2004))
        except Exception as e:
            set_log.debug(f"[ERROR] {e}")
            set_log.info(f"无效的token")

            raise exceptions.AuthenticationFailed(return_code(2004))

        # return (user, None)
def MyTokenAuthentication_by_hand(request):
    set_log.info(f"+"*100)
    set_log.info(f"[MyTokenAuthentication] 开始Token验证")
    # token = request.META.get('X-Auth-Token')
    # set_log.debug(f"[token] {token}")
    try:
        operator = request.data.get('data', {'operator': False}).get('operator', False)
        set_log.debug(f"[frontend data] {request.data}")
        if operator:
            return ({"user_name": "jarvis"}, 20000)
        llm_token = request.headers.get('token')
        if llm_token == 'LLM':
            return ({"user_name": "llm"}, 20000)
        token = request.headers.get("Authorization")
        set_log.debug(f"[token] {token} [llm_token] {llm_token}" )
        if token[6] == ' ':
            token = token[7:]
        if not token:
            set_log.info(f"[无token信息]")
            # 用户未登录
            return ("", 2002)

        username = jwt_decode_handler(token)
        set_log.debug(f"[username] {username}")
        now = int(time.time())
        if username['exp'] - now <= 0:
            return ("", 2003)
        user = MyUserInfo.objects.filter(email=username['email'], not_send="False")[0]
        set_log.debug(f"[user] {user}")
        if user.is_active:
            return (user, 20000)
        else:
            # token无效
            return 2004
    except jwt.ExpiredSignature:
        set_log.info('token 过期')
        return ("", 401)
        # return Response(return_code(401, data='token 过期', msg='token 过期', status='error'))
    except jwt.DecodeError:
        set_log.info(f"签名错误")
        return ("", 2004)
    except jwt.InvalidTokenError:
        set_log.info(f"无效的token")
        return ("", 2004)
    except exceptions.NotAuthenticated:
        set_log.info(f"EMPTY TOKEN")
        return ("", 2003)
    except Exception as e:
        set_log.debug(f"[认证模块异常] {e}")
        set_log.info(f"异常的token")

        return ("", 2004)

def createReportAuth(request):
    return True

def editReportAuth(request):
    return True

def delReportAuth(request):
    return True

def realdelReportAuth(request):
    return True

