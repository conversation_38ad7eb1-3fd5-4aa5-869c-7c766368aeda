import time

from ..models import *
from ..serializers import *
from common.utils.log import Logger

set_log = Logger.get_logger(__name__, 3)

def Jarvis_createFault(request):
    try:
        print("-" * 40)
        set_log.info("Here is Jarvis_createFault")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        # 生成新fid
        last_fid = FaultInfo.objects.all().order_by('-id')[0].fid
        set_log.debug(f"[last FID] {type(last_fid)} {last_fid}")
        today = time.strftime('%Y%m%d', time.localtime(time.time()))
        set_log.debug(f"[日期比较]  {last_fid[:8]} {today}")
        if last_fid[:8] == today:
            set_log.debug(f"SAME DAY")
            fid = str(int(last_fid) + 1)
        else:
            set_log.debug(f"[NEW DAY]")
            fid = f"{today}0001"
        set_log.debug(f"[NEW FID] {fid}")
        frontend_data['fid'] = fid

        cookie = request.META.get("HTTP_COOKIE")
        if cookie != 'EB3xwAfrZwz.262VbIioi755+oYP#4t39RF_0+NR3BWzeGM0Cc7HvWDeT_50f5i+tzvu9ZU$qXaj8LoV0gV2JhHQ9WmRTK9LnKuu#Q4ZYbHm7krnyCmrV&fIv2ScElTz':
            set_log.debug('鉴权失败')
            return 2002, "鉴权失败"

        # fid = data['fid']
        # rectify = data.get('improveAction', False)
        # set_log.debug(f"rectify:{rectify}, {type(rectify)}")
        # playback = frontend_data.get('playback', False)
        # if playback:
        #     frontend_data.pop('playback')
        # set_log.debug(f"playback:{playback}")
        # data['improveAction'] = str(data['improveAction'])
        # data['playback'] = str(data['playback'])
        if 'stage' not in frontend_data.keys():
            frontend_data['stage'] = '未发布'
        set_log.debug(f"[录入数据] {frontend_data}")
        fault = FaultInfo(**frontend_data)
        fault.save()
        set_log.debug(f"[录入成功]")

        # if playback and playback != []:
        #     set_log.debug(f"[get playback] {playback}")
        #     new_playback = []
        #     for i in playback:
        #         i['fid'] = fault
        #         if i.get('pid', False):
        #             i.pop('pid')
        #         new_playback.append(PlaybackInfo(**i))
        #         # playback = PlaybackInfo(stage='stage', ptime='ptime', content='content', image='image')
        #     set_log.debug(f"[new playback]{new_playback}")
        #     PlaybackInfo.objects.bulk_create(new_playback)

        # 创建该表权限
        # content_type = ContentType.objects.get_for_model(FaultInfo)
        # Permission.objects.create(codename=f'view_{fid}', name=f'Can View {fid}', content_type=content_type)
        # Permission.objects.create(codename=f'change_{fid}', name=f'Can Change {fid}', content_type=content_type)
        # Permission.objects.create(codename=f'delete_{fid}', name=f'Can Delete {fid}', content_type=content_type)
        # set_log.debug(f"单故障权限创建成功")
        # 创建所有者的权限
        # 判断所有者是否为管理员
        # 管理员则跳过 ，否则赋予该表所有权限
        # user = User.objects.get(username='T3647')
        # set_log.debug(f"user: {user}")
        # groups = [i['fields']['name'] for i in json.loads(serializers.serialize('json', user.groups.all()))]
        # 创建单一故障管理员权限
        # group = Group.objects.create(name=f"{fid}_admin")
        # set_log.debug(f"单故障管理员权限创建成功: {group}")
        # set_log.debug(f"用户所有groups: {groups}, {type(groups)}")
        # if 'superuser' not in groups:
            # 非管理员授予 该故障管理员权限
            # try:
            #     user.groups.add(group)
            # except Exception as e:
            #     set_log.error(f"授权失败: {e}")
            #     return Response(return_code(-3, data=e))
                # return (-3, e)

        return 20000, frontend_data

        # return Response(return_code(code, data="创建成功", url=f"{conf['fmp_url']}/#/report?type=view&fid={fid}"))

    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)
