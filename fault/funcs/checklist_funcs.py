import copy
import json
import time
import datetime
import calendar
import re
import hashlib
import requests

from django.db.models import Q
from rest_framework_jwt.utils import jwt_decode_handler


from ..models import *
from ..serializers import *
from common.utils.log import Logger
from common.utils.config import itmp_conf
from common.utils.utils import get_user

from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers

set_log = Logger.get_logger(__name__, 3)


def SelectChecklist(request, **kwargs):
    try:
        print("-" * 40)
        set_log.info("Here is SelectChecklist")
        set_log.debug(f"[request method] {request.method}")
        if request.method == 'GET':
            set_log.debug(f"[request method] --- GET ---")
            frontend_data = request.GET
        elif request.method == 'POST':
            set_log.debug(f"[request method] --- POST ---")
            frontend_data = request.data['data']
        else:
            return -4, '方法不允许'
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        page = int(frontend_data.get('page', 1))
        limit = int(frontend_data.get('limit', 10))



        # 分页参数
        set_log.debug(f"分页参数：page - {page}, limit - {limit}")
        start = page*limit-limit
        end = page*limit
        if kwargs.get('dashboard', False):
            start = 0
            end = None
        search_params = {}
        query = frontend_data.get('query', {})
        # json_fields = ['task', 'describe', 'creator']
        for key, value in query.items():
            set_log.debug(f"[one search] {key} {value} {type(value)}")
            # 精确搜索
            if not value or (isinstance(value, str) and value.strip() == ''):
                continue
            # if key in json_fields:
            #     q_objects = [Q(**{f"{key}__icontains": item}) for item in value]
            #     search_params[f"{key}__in"] = value if isinstance(value, list) else [value]
            #     search_params[f"{key}__in"] |= q_objects.pop()
            #     for q_obj in q_objects:
            #         search_params[f"{key}__in"] |= q_obj

                # 时间查询
            elif key == 'createTime' and value != None :
                search_params['createTime__range'] = value
            elif key == 'updateTime'  and value != None :
                search_params['updateTime__range'] = value
            else:
                if isinstance(value, list):
                    search_params[f"{key}__in"] = value
                else:
                    search_params[f"{key}__icontains"] = value            
        func_start_time = time.time()

        data_query = CheckList.objects.filter(**search_params).order_by('-createTime')
        total_count = data_query.count()
        
        data_results = CheckListBaseSerializers(data_query[start:end], many=True).data

        func_end_time = time.time()
        
        set_log.info(f"[查询 耗时] {func_end_time - func_start_time:.2f} seconds")

        if not data_results:
            set_log.error(f"查询结果为空：{data_results}")
            return [], 0

        return data_results, total_count
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def CreateChecklist(request):
    try:
        print("-" * 40)
        set_log.info("Here is CreateChecklist")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        # 创建人
        user = get_user(request)
        frontend_data['creator'] = user['user_name']
        frontend_data['updatetor'] = user['user_name']
        frontend_data['createTime'] = datetime.datetime.now()
        frontend_data['updateTime'] = datetime.datetime.now()
        try:
            checklist = CheckList(**frontend_data)
            checklist.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def EditChecklist(request):
    try:
        print("-" * 40)
        set_log.info("Here is EditChecklist")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:

            if not frontend_data.get("id", False):
                return 1009, "缺少fid"
            id = frontend_data["id"]
            # 获取要更新的对象
            checklist = CheckList.objects.get(pk=id)
            # 更新字段
            for key, value in frontend_data.items():
                setattr(checklist, key, value)
            # 更新人
            user = get_user(request)
            frontend_data['updatetor'] = user['user_name']
            # 更新时间
            frontend_data['updateTime'] = datetime.datetime.now()
            checklist.save()
        except Exception as e:
            set_log.info(f"[CREATE ONE CHECKLIST] FALSE]")
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.info(f"[CREATE ONE CHECKLIST FALSE]")
        set_log.error(f"error: {e}")
        return -1, str(e)

def DeleteChecklist(request):
    try:
        print("-" * 40)
        set_log.info("Here is DeleteChecklist")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("id", False):
                return 1009, "缺少id"
            id = frontend_data['id']
            checklist = CheckList.objects.get(pk=id)
            checklist.stage = "已删除"
            checklist.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def RealdeleteChecklist(request):
    try:
        print("-" * 40)
        set_log.info("Here is RealdeleteRectify")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("id", False):
                return 1009, "缺少id"
            id = frontend_data['id']
            checklist = CheckList.objects.get(pk=id)
            checklist.delete()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data

    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)