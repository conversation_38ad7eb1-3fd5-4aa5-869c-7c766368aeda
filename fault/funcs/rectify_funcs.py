import copy
import json
import time
import datetime
import calendar
import re
import hashlib
import requests

from django.db.models import Q
from rest_framework_jwt.utils import jwt_decode_handler


from ..models import *
from ..serializers import *
from common.utils.log import Logger
from common.utils.config import itmp_conf
from common.utils.utils import get_user

from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers

set_log = Logger.get_logger(__name__, 3)


def my_selectRectify(request, **kwargs):
    try:
        print("-" * 40)
        set_log.info("Here is my_selectRectify")
        set_log.debug(f"[request method] {request.method}")
        if request.method == 'GET':
            set_log.debug(f"[request method] --- GET ---")
            frontend_data = request.GET
        elif request.method == 'POST':
            set_log.debug(f"[request method] --- POST ---")
            frontend_data = request.data['data']
        else:
            return -4, '方法不允许'
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        page = int(frontend_data.get('page', 1))
        limit = int(frontend_data.get('limit', 10))
        query = frontend_data.get('query', {})
        # if query.get('reportType', False):
        #     reportType = query.pop('reportType')

        # 分页参数
        set_log.debug(f"分页参数：page - {page}, limit - {limit}")
        start = page*limit-limit
        end = page*limit
        if kwargs.get('dashboard', False):
            start = 0
            end = None
        # 获取当前用户
        # user = get_user(request)

        search = dict()
        search_by_hand = Q()
        search_json_icontains = []
        # 可多选参数
        # json_field = ["fid", "year", "month", "level", "cause", "current", "driver", "causelocator", "firstlaunch", "watch", "alert", "reportType"]
        # json_field = ["cause", "current", "driver", "causelocator", "firstlaunch", "watch", "alert", "reportType"]
        json_field = ["reportType"]
        # 单选参数
        one_field = ["fid", "faultDescribe", "rectifyDescribe"]
        for i, j in query.items():
            set_log.debug(f"[one search] {i} {j} {type(j)}")
            # 精确搜索
            if j == []:
                pass
            elif j == '':
                pass
            else:
                set_log.info(f"[Valid parameters]")
                # 模糊搜索
                if i == 'search':
                    if j == '':
                        continue
                    # Q(id__icontains=j) |
                    search_by_hand = Q(fid__icontains=j) | Q(faultDescribe__icontains=j) | \
                                     Q(rectifyDescribe__icontains=j) | Q(id__icontains=j)
                                     # | Q(faultTime__icontains=j) | Q(causeClassify__icontains=j) | \
                                     # Q(causeDescribe__icontains=j) | Q(effectContent__icontains=j) | Q(creator__icontains=j) | \
                                     # Q(stage__icontains=j) | Q(slaDetail__icontains=j) | Q(effectSLO__icontains=j)
                # 单选        
                elif i in one_field:
                    search[f'{i}__icontains'] = j
                # 多选参数
                # elif i in json_field:
                #     search_by_hand.connector = 'OR'
                #     for k in j:
                #         search_by_hand.children.append((f"{i}__icontains", k))
                # 多选模糊匹配参数
                elif i in json_field:
                    # search_by_hand.connector = 'AND'
                    search_json_icontains.append(Q())
                    for k in j:
                        # search_by_hand.connector = 'OR'
                        # search_by_hand.children.append((f"{i}__icontains", k))
                        search_json_icontains[-1].connector = 'OR'
                        search_json_icontains[-1].children.append((f"{i}__icontains", k))
                # 时间查询
                elif i == 'startTime' and j != None :
                    # [i for i in j]
                    search['startTime__range'] = j
                elif i == 'estimateTime':
                    search['estimateTime__range'] = j    
                else:
                    if j != None:
                        search[f'{i}__in'] = j
        set_log.debug(f"查询参数：{search} {search_by_hand} {len(search_by_hand)}")

        func_start_time = time.time()

        # 优化
        data = RectifyInfo.objects.all()
        if len(search) != 0:
            data = data.filter(**search)
        if len(search_by_hand) != 0:
            data = data.filter(search_by_hand)
        if len(search_json_icontains) != 0:
            for i in search_json_icontains:
                data = data.filter(i)
        data = data.order_by('-startTime')
        total = data.count()
        set_log.debug(f"[search data] {data}")
        data = RectifySerializers(data[start:end], many=True).data
        set_log.debug(f"[search data json len] {len(data)}")

        set_log.info(f"[sel data] SUCCESS")
        func_end_time = time.time()
        set_log.info(f"[查询 耗时] {func_end_time - func_start_time:.2f} seconds")
        # 时间格式转换
        set_log.debug(f"分页后数据长度：{len(data)}")

        # 查询为空
        if data == []:
            set_log.error(f"查询结果为空：{data}")
            return [], 0
        return data, total
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_createRectify(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_createRectify")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        # 生成新fid
        last_fid = RectifyInfo.objects.all().order_by('-id')[0].fid
        set_log.debug(f"[last FID] {type(last_fid)} {last_fid}")
        today = time.strftime('%Y%m%d', time.localtime(time.time()))
        set_log.debug(f"[日期比较]  {last_fid[:8]} {today}")
        if last_fid[:8] == today:
            set_log.debug(f"SAME DAY")
            fid = str(int(last_fid) + 1)
        else:
            set_log.debug(f"[NEW DAY]")
            fid = f"{today}0001"
        set_log.debug(f"[NEW FID] {fid}")
        frontend_data['fid'] = fid
        # 创建人
        user = get_user(request)
        frontend_data['creator'] = user['user_name']
        frontend_data['creatorID'] = user['TT_id']
        # 类型
        frontend_data['reportType'] = '故障'
        frontend_data['stage'] = '已发布'

        try:
            rectify = RectifyInfo(**frontend_data)
            rectify.save()
            fault = FaultInfo.objects.get(fid=fid)
            fault.currentProgress = change_fault_currentProgress(fault)
            fault.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_editRectify(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_editRectify")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("fid", False):
                return 1009, "缺少fid"
            fid = frontend_data["fid"]
            fault = FaultInfo.objects.get(fid=fid)
            if not frontend_data.get("iid", False):               
                frontend_data['faultDescribe'] = frontend_data.get('faultDescribe', fault.faultDescribe)
                frontend_data['faultTime'] = frontend_data.get('faultMonth', fault.faultMonth)
                frontend_data['reportType'] = frontend_data.get('reportType', fault.reportType)
                frontend_data['stage'] = '已发布'
                rectify = RectifyInfo(**frontend_data)
                rectify.save()
                rectify.iid = hashlib.md5(str(rectify.pk).encode("utf8")).hexdigest()
                rectify.save()
                set_log.info(f"[CREATE ONE RECTIFY SUCCESS]")

            else:
                # 更新
                iid = frontend_data.pop("iid")
                set_log.debug(f"[UPDATE {iid}]")
                RectifyInfo.objects.filter(iid=iid).update(**frontend_data)
                change_rectify_isDelay(iid=iid)
                set_log.info(f"[UPDATE ONE RECTIFY SUCCESS]")
            # 当是最后一个完成的整改措施同步问题为已归档
            fault.currentProgress = change_fault_currentProgress(fault)
            # 每次变更同步improvementFinishTime为最大时间
            fault.improvementFinishTime = change_fault_improvementFinishTime(fault)
            # 同步至fault
            fault.improveAction = RectifySerializers(RectifyInfo.objects.filter(fid=fid, stage="已发布"), many=True).data
            fault.save()
        except Exception as e:
            set_log.info(f"[CREATE ONE RECTIFY FALSE]")
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.info(f"[CREATE ONE RECTIFY FALSE]")
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_deleteRectify(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_deleteRectify")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("fid", False):
                return 1009, "缺少fid"
            fid = frontend_data['fid']
            fault = FaultInfo.objects.get(fid=fid)
            if not frontend_data.get("iid", False):
                return 1009, "缺少iid"
            rectify = RectifyInfo.objects.get(iid=frontend_data['iid'])
            rectify.stage = "已删除"
            rectify.save()
            # 同步至fault
            fault.improveAction = RectifySerializers(RectifyInfo.objects.filter(fid=fid, stage="已发布"), many=True).data
            fault.currentProgress = change_fault_currentProgress(fault)
            fault.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_realdeleteRectify(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_realdeleteRectify")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("fid", False):
                return 1009, "缺少fid"
            fid = frontend_data['fid']
            fault = FaultInfo.objects.get(fid=fid)
            if not frontend_data.get("iid", False):
                return 1009, "缺少iid"
            rectify = RectifyInfo.objects.get(fid=frontend_data['fid'])
            rectify.delete()
            # 同步至fault
            fault.improveAction = RectifySerializers(RectifyInfo.objects.filter(fid=fid, stage="已发布"), many=True).data
            fault.currentProgress = change_fault_currentProgress(fault)
            fault.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data

    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

# 当是最后一个完成的整改措施同步问题为已归档
def change_fault_currentProgress(fault):
    # fault = ""
    stage_list = [ i["rate"] for i in RectifySerializers(RectifyInfo.objects.filter(fid=fault.fid, stage="已发布"), many=True).data]
    set_log.debug(f"[stage_list] {stage_list}")
    if "进行中" not in stage_list and "未补充" not in stage_list and "问题" in fault.reportType:
        # fault.currentProgress = '已归档'
        set_log.info("问题状态变更 -> 已归档")
        return "已归档"
    else:
        # fault.currentProgress = '未归档'
        set_log.info("问题状态变更 -> 未归档")
        return "未归档"

# 每次变更同步improvementFinishTime为最大时间
def change_fault_improvementFinishTime(fault):
    # fault = ""
    finish_time_list = []
    for i in RectifySerializers(RectifyInfo.objects.filter(fid=fault.fid, stage="已发布"), many=True).data:
        set_log.debug(f"[historyDelayTime] {type(i['historyDelayTime'])} {i['historyDelayTime']}")
        set_log.debug(f"[estimateTime] {type(i['estimateTime'])} {i['estimateTime']}")
        finish_time_list += i['historyDelayTime'] + [i['estimateTime']]
    set_log.debug(f"[stage_list] {finish_time_list}")
    finish_time_list.sort()
    return finish_time_list[-1]

# 每次变更判断是否延期
def change_rectify_isDelay(iid):
    # 判断是否延期
    rectify = RectifyInfo.objects.get(iid=iid)
    estimateTime = rectify.estimateTime
    if not estimateTime:
        return "estimateTime 不能为空"
    completeTime = rectify.completeTime if rectify.completeTime else datetime.datetime.now()
    # time.strftime('%Y-%m-%d %H:%M', time.localtime(time.time()))
    set_log.debug(f"[是否延期判断] [completeTime] {completeTime} [estimateTime] {estimateTime}")
    if completeTime <= estimateTime:
        set_log.info(f"[整改措施 未延期]")
        rectify.isDelay = "未延期"
    else:
        set_log.info(f"[整改措施 已延期]")
        rectify.isDelay = "已延期"
    rectify.save()
