import copy
import json
import time
import requests
from rest_framework_jwt.utils import jwt_decode_handler

from ..models import *
from ..serializers import *
from common.utils.log import Logger
from common.utils.config import itmp_conf

from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers

set_log = Logger.get_logger(__name__, 3)

def get_cookie(request):
    print("-" * 50)
    set_log.info("Here is get frontend cookie")
    cookie = request.META.get("HTTP_COOKIE")
    temp = cookie.split('; ')
    cookie = dict()
    for i in temp:
        i = i.split('=')
        cookie[i[0]] = i[1]
    cookie['Authorization'] = cookie['Authorization'].replace('%20', ' ')
    set_log.debug(f"frontend cookie: {cookie}")
    return cookie

def get_user(request):
    token = request.headers.get("Authorization")
    if token[6] == ' ':
        token = token[7:]
    set_log.debug(f"[frontend token] {token}")
    username = jwt_decode_handler(token)
    set_log.debug(f"[username] {username}")

    user = MyUserInfoSerializers(MyUserInfo.objects.filter(eamil=username['username'], not_send="False")[0]).data
    set_log.info(f"[get user] {user}")

    return user
