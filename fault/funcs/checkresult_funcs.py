import copy
import json
import random
import time
import datetime
import calendar
import re
import hashlib
import requests

from django.db.models import Q
from rest_framework_jwt.utils import jwt_decode_handler
from common.utils.config import path_config

from ..models import *
from ..serializers import *
from common.utils.log import Logger
from common.utils.config import itmp_conf
from common.utils.utils import get_user

from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers

set_log = Logger.get_logger(__name__, 3)



def SelectCheckresult(request, **kwargs):
    try:
        print("-" * 40)
        set_log.info("Here is SelectCheckresult")
        set_log.debug(f"[request method] {request.method}")
        if request.method == 'GET':
            set_log.debug(f"[request method] --- GET ---")
            frontend_data = request.GET
        elif request.method == 'POST':
            set_log.debug(f"[request method] --- POST ---")
            frontend_data = request.data['data']
        else:
            return -4, '方法不允许'
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        page = int(frontend_data.get('page', 1))
        limit = int(frontend_data.get('limit', 10))


        # 分页参数
        set_log.debug(f"分页参数：page - {page}, limit - {limit}")
        start = page*limit-limit
        end = page*limit
        if kwargs.get('dashboard', False):
            start = 0
            end = None
        search_params = {}
        query = frontend_data.get('query', {})
        # json_fields = ['iscomplete', 'checkResult', 'checkPerson','approver', 'approverResult']
        for key, value in query.items():
            set_log.debug(f"[one search] {key} {value} {type(value)}")
            # 精确搜索
            if not value or (isinstance(value, str) and value.strip() == ''):
                continue 
                # 时间查询
            elif key == 'checkTime' and value != None :
                search_params['checkTime__range'] = value
            elif key == 'approverTime' and value != None :
                search_params['approverTime__range'] = value
            # elif key == 'stage' and value != None :
            #     search_params['stage__icontains'] = value  
            else:
                # q_objects = [Q(**{f"{key}__icontains": item}) for item in value]
                # set_log.debug(f"[q_objects] {q_objects} type {type(q_objects)}")
                if isinstance(value, list):
                    search_params[f"{key}__in"] = value
                    # search_params[f"{key}__in"] |= q_objects.pop()
                else:
                    search_params[f"{key}__icontains"] = value    
                # for q_obj in q_objects:
                #     search_params[f"{key}__in"] |= q_obj
                set_log.info(f"[search_params] {search_params}")           
        func_start_time = time.time()

        data_query = CheckResult.objects.filter(**search_params).order_by('-checkTime')
        total_count = data_query.count()
        
        data_results = CheckResultBaseSerializers(data_query[start:end], many=True).data

        func_end_time = time.time()
        
        set_log.info(f"[查询 耗时] {func_end_time - func_start_time:.2f} seconds")

        if not data_results:
            set_log.error(f"查询结果为空：{data_results}")
            return [], 0

        return data_results, total_count
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def get_md5_value(value):
    my_md5 = hashlib.md5()  # 获取一个MD5的加密算法对象
    my_md5.update(value.encode("utf8"))  # 得到MD5消息摘要
    my_md5_Digest = my_md5.hexdigest()  # 以16进制返回消息摘要，32位
    return my_md5_Digest


def sign(pass_id, timestamp, nonce, app_secret):
    params = {"x-tt-paasid": pass_id,
              "x-tt-timestamp": str(timestamp),
              "x-tt-nonce": nonce}
    temp_string = ""
    for k in sorted(params):
        temp_string = temp_string + k + params[k]
    temp_string = temp_string + app_secret
    md5_value = get_md5_value(temp_string)
    return md5_value

# 传入工号
def get_userinfo(userNo):
    app_secret = 'ztjiowuynmqtnzcxms00mzjlltk5mgmtymjjytflmdjkytbm'
    app_id = 'itmp-backend'
    timestamp = str(time.time()).split('.')[0]
    nonce = str(random.randint(10000000, 99999999))
    set_log.info(timestamp, nonce)
    # .strftime("%Y-%m-%d %H:%M:%S")
    params = {"x-tt-paasid": "itmp-backend",
              "x-tt-timestamp": timestamp,
              "x-tt-nonce": nonce}
    siginature = sign(app_id, timestamp, nonce, app_secret)
    headers = {
        'x-tt-paasid': 'itmp-backend',
        'x-tt-signature': siginature,
        'x-tt-timestamp': timestamp,
        'x-tt-nonce': nonce
    }
    params = {
        'userNo': userNo,
    }
    set_log.info(headers, params)
    q = requests.get(
        #f"https://test-union-b-gateway.52tt.com/ebc-api/api-user/restApi/user/userRel",
		f"https://union-b-gateway.52tt.com/ebc-api/api-user/restApi/user/userRel",
        headers=headers, params=params)
    set_log.info(q.text)
    try:
        superiorName = q.json()['data']['superiors'][0]['superiorName']
    except:
        superiorName = ''
    return superiorName

def CreateCheckresult(request):
    try:
        print("-" * 40)
        set_log.info("Here is CreateCheckre")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        # 判断approver是否存在
        if frontend_data.get('approver', None):
            frontend_data['approver'] = get_userinfo(frontend_data['approver'])
        if frontend_data.get('checkPerson', None):
            # 通过MyUserInfo 查询 TT id
            TT_id = MyUserInfo.objects.get(user_name=frontend_data['checkPerson']).TT_id
            # 通过TT id 获取 直属上级
            frontend_data['approver'] = get_userinfo(TT_id)
        # 创建人
        # user = get_user(request)
        # frontend_data['creator'] = user['user_name']
        # 创建外键
        frontend_data['rectify'] = RectifyInfo.objects.get(iid=frontend_data['iid'])
        # frontend_data['check_list'] = CheckList.objects.get(id=int(frontend_data['check_list']))
        del frontend_data['iid']
        # del frontend_data['check_list']
        try:
            Checkresult = CheckResult(**frontend_data)
            Checkresult.save()
            frontend_data = CheckResultBaseSerializers(Checkresult).data
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def EditCheckresult(request):
    try:
        print("-" * 40)
        set_log.info("Here is EditCheckresult")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:

            if not frontend_data.get("id", False):
                return 1009, "缺少fid"
            id = frontend_data["id"]
            # 获取要更新的对象
            checkresult = CheckResult.objects.get(pk=id)
            # 更新字段
            for key, value in frontend_data.items():
                setattr(checkresult, key, value)
            # 更新人
            # user = get_user(request)
            # frontend_data['creator'] = user['user_name']
            checkresult.save()
        except Exception as e:
            set_log.info(f"[CREATE ONE CHECKRESULT] FALSE]")
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.info(f"[CREATE ONE CHECKRESULT FALSE]")
        set_log.error(f"error: {e}")
        return -1, str(e)

def DeleteCheckresult(request):
    try:
        print("-" * 40)
        set_log.info("Here is DeleteCheckresult")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("id", False):
                return 1009, "缺少id"
            id = frontend_data['id']
            checkresult = CheckResult.objects.get(pk=id)
            checkresult.stage = "已删除"
            checkresult.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def RealdeleteCheckresult(request):
    try:
        print("-" * 40)
        set_log.info("Here is RealdeleteCheckresult")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("id", False):
                return 1009, "缺少id"
            id = frontend_data['id']
            checkresult = CheckResult.objects.get(pk=id)
            checkresult.delete()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data

    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)