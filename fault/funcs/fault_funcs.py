import copy
import json
import time
import calendar
import re
import hashlib

import requests
from django.db.models import Q
from rest_framework_jwt.utils import jwt_decode_handler
import datetime
from datetime import datetime, timedelta
from django.core import serializers

from ..models import *
from ..serializers import *
from common.utils.log import Logger
from common.utils.config import itmp_conf
from common.utils.utils import get_user

from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers

set_log = Logger.get_logger(__name__, 3)


def my_selectFault(request, **kwargs):
    try:
        set_log.info("-" * 40)
        set_log.info("Here is my_selectFault")
        set_log.debug(f"[request method] {request.method}")
        if request.method == 'GET':
            set_log.debug(f"[request method] --- GET ---")
            frontend_data = request.GET
        elif request.method == 'POST':
            set_log.debug(f"[request method] --- POST ---")
            frontend_data = request.data['data']
        else:
            return -4, '方法不允许'
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        page = int(frontend_data.get('page', 1))
        limit = int(frontend_data.get('limit', 10))
        query = frontend_data.get('query', {})
        # if query.get('reportType', False):
        #     reportType = query.pop('reportType')

        # 分页参数
        set_log.debug(f"分页参数：page - {page}, limit - {limit}")
        start = page*limit-limit
        end = page*limit
        if kwargs.get('dashboard', False):
            start = 0
            end = None
        # 获取当前用户
        # user = get_user(request)

        search = dict()
        search_by_hand = Q()
        search_json_icontains = []
        # 可多选参数
        # json_field = ["fid", "year", "month", "level", "cause", "current", "driver", "causelocator", "firstlaunch", "watch", "alert", "reportType"]
        json_field = ["cause", "current", "driver", "causelocator", "firstlaunch", "watch", "alert", "reportType", "stage"]
        # 问题类型使用 startTime 工单才用 enterTime
        time_range_field = ["startTime", "improvementFinishTime", "finishRateTime"]
        # 单选参数
        # one_field = ["fid"]
        for i, j in query.items():
            set_log.debug(f"[one search] {i} {j} {type(j)}")
            # 精确搜索
            if j == []:
                pass
            # elif j == '':
            #     pass
            else:
                set_log.info(f"[Valid parameters]")
                # 模糊搜索
                if i == 'search':
                    if j == '':
                        continue
                    # Q(id__icontains=j) |
                    search_by_hand = Q(fid__icontains=j) | Q(faultDescribe__icontains=j)
                                     # | \
                                     # Q(startTime__icontains=j) | Q(recoverTime__icontains=j) | Q(causeClassify__icontains=j) | \
                                     # Q(causeDescribe__icontains=j) | Q(effectContent__icontains=j) | Q(creator__icontains=j) | \
                                     # Q(stage__icontains=j) | Q(slaDetail__icontains=j) | Q(effectSLO__icontains=j)
                elif i == 'faultDescribe':
                    search['faultDescribe__icontains'] = j

                # 影响产品正则查询
                elif i == 'isEffectProduce':
                    for item in j:
                        if item == "":
                            null_itmp = "\\[\\]"
                            j.append(null_itmp)
                            break
                    # 判断列表是否都为空
                    all_empty = all(not item for item in j)
                    if not all_empty:
                        filtered_list = [item for item in j if item]
                        temp = '|'.join(filtered_list)
                        search['isEffectProduce__iregex'] = temp
                    else:
                        search['isEffectProduce__iregex'] = "\\[\\]"
                    set_log.debug(f"[isEffectProduce 参数] : {j}")
                # 批量查询fid    
                elif i == 'fid':
                    # 判读value类型
                    if type(j) == list:
                        # 判断列表是否都为空
                        all_empty = all(not item for item in j)
                        if not all_empty:
                            filtered_list = [item for item in j if item]
                            temp = '|'.join(filtered_list)
                            search['fid__iregex'] = temp
                    elif type(j) == str:
                        search[f'{i}__icontains'] = j
                    set_log.debug(f"[fid 参数] : {j}")

                # 多选模糊匹配参数
                elif i in json_field:
                    # search_by_hand.connector = 'AND'
                    search_json_icontains.append(Q())
                    for k in j:
                        # search_by_hand.connector = 'OR'
                        # search_by_hand.children.append((f"{i}__icontains", k))
                        search_json_icontains[-1].connector = 'OR'
                        search_json_icontains[-1].children.append((f"{i}__icontains", k))
                # # 单选        
                # elif i in one_field:
                #     search[f'{i}__icontains'] = j
                # 时间查询
                elif i in time_range_field:
                    # [i for i in j]
                    # 排除None

                    if j:
                        if i == "finishRateTime":
                            search[f'improvementFinishTime__range'] = j
                        else:
                            search[f'{i}__range'] = j
                elif i == 'year' or i == 'month' or i == "data":
                    pass
                else:
                    search[f'{i}__in'] = j
        year = query.get('year', [])
        month = query.get('month', [])
        rangeTime = query.get('rangeTime', False)
        set_log.debug(f"[q-date] {rangeTime} {year} {month}")
        if year != [] or month != []:
            set_log.debug(f"[year] {type(year)} {year}")
            set_log.debug(f"[month] {type(month)} {month}")
            date = []
            if year != [] and month != []:
                search['faultMonth__in'] = date
            elif year == []:
                date = [f"{j}年{i}月" for i in month for j in ['2021', '2022', '2023']]
                search['faultMonth__in'] = date
            elif month == []:
                date = [f"{i}年" for i in year]
                search['faultYear__in'] = date
            set_log.debug(f"[date] {date}")
        elif rangeTime:
            try: 
                start_time = datetime.strptime(rangeTime[0], '%Y-%m')
                input_date = datetime.strptime(rangeTime[1], '%Y-%m')
            except ValueError:
                start_time = datetime.strptime(rangeTime[0], '%Y-%m-%d %H:%M:%S')
                input_date = datetime.strptime(rangeTime[1], '%Y-%m-%d %H:%M:%S')
            _, last_day = calendar.monthrange(input_date.year, input_date.month)
            end_time = input_date.replace(day=last_day, hour=23, minute=59, second=59)
            search['startTime__range'] = [start_time, end_time]
        if 'rangeTime__in' in search:
            del search['rangeTime__in']
        set_log.debug(f"查询参数：{search} {search_by_hand} {len(search_by_hand)} {search_json_icontains}")

        func_start_time = time.time()

        data = FaultInfo.objects.all()
        if len(search) != 0:
            data = data.filter(**search)
        if len(search_by_hand) != 0:
            data = data.filter(search_by_hand)
        if len(search_json_icontains) != 0:
            for i in search_json_icontains:
                data = data.filter(i)
        data = data.order_by('-startTime')
        fid_list = data.values_list('fid', flat=True)
        rectify_data = RectifyInfo.objects.filter(fid__in=fid_list)
        total = data.count()
        set_log.debug(f"[search data] {data}")
        data = FaultSerializers(data[start:end], many=True).data
        set_log.debug(f"[FaultSerializers data] {data}")
        set_log.debug(f"[search data json len] {len(data)}")


        set_log.info(f"[sel data] SUCCESS")
        func_end_time = time.time()
        set_log.info(f"[查询 耗时] {func_end_time - func_start_time:.2f} seconds")
        # 时间格式转换
        set_log.debug(f"分页后数据长度：{len(data)}")

        # 查询为空
        if data == []:
            set_log.info(f"查询结果为空：{data}")
            return [], 0, []
        set_log.debug(f"[search data and total] {data} {total}")    
        return data, total, rectify_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e), []

def get_dashboard(request):
    set_log.debug(f"[request method] {request.method}")
    if request.method == 'GET':
        frontend_data = request.GET
    elif request.method == 'POST':
        frontend_data = request.data['data']
    else:
        return -4, '方法不允许'
    set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
    faults, faultCount, rectifys_data = my_selectFault(request, dashboard=True)
    rectifys = [] 
    if isinstance(faults, int):
        return faults
    faultCount = len(faults)
    dashboard = {
        'mttr': 0,
        'mttf': 0,
        # 平均发现时长
        'distime': 0,
        # 平均响应时长
        'restime': 0,
        # 平均定位时长
        'loctime': 0,
        # 平均恢复时长
        'reptime': 0,
        # 首发率
        'firstlan': 0,
        # 运维定位
        'opslocate': 0,
        # 故障总时长
        'totalDuration': 0,

        # 故障数
        'faultCount': faultCount,
        # 定级比例
        'isGrade': 0,
        # 定级数
        'isGradeCount': 0,
        # 未定级数
        'unGradeCount': 0,
        # 暂未定级数
        'noneGradeCount': 0,

        # P2以上故障数
        'overP2Count': 0,
        # P3以上故障数
        'overP3Count': 0,
        'p1Count': 0,
        'p2Count': 0,
        'p3Count': 0,
        'p4Count': 0,
        'p5Count': 0,

        # 整改措施数
        'rectifyCount': 0,
        # 整改措施完成率
        'improveActionFinish': 0,
        # 待完成数
        'improveActionTodoCount': 0,
        # 已超时数
        'improveActionDelayCount': 0,
        # 整改措施延期率
        'improveActiondelay': 0,

        # 复盘完成率
        'replayDelayCount': 0,
        # 待复盘数
        'todoReplayCount': 0,
        # 简报及时率
        'briefreport': 0,
        # 复盘及时率
        'replay': 0,
        # 归档及时率
        'file': 0,

         # 预案覆盖率
        'recoveryplan': 0,

    }
    if faults == []:
        return dashboard
    for i in faults:
        # i = i['fields']
        set_log.debug(f"[dashboard one data] {i}")
        # set_log.debug(f"[rectifyCount] {dashboard['rectifyCount']} {type(i['improveAction'])}")
        rectifys = i['improveAction']
        set_log.debug(f"[rectifys] {rectifys} {type(rectifys)}")
        dashboard['rectifyCount'] += len(rectifys)
        dashboard['totalDuration'] += i['duration'] if i['duration'] != '' else 0
        # dashboard['mttf'] += i['duration']
        dashboard['distime'] += i['discoveryConsuming'] if i['discoveryConsuming'] != '' else 0
        dashboard['restime'] += i['responseConsuming'] if i['responseConsuming'] != '' else 0
        dashboard['loctime'] += i['loacteConsuming'] if i['loacteConsuming'] != '' else 0
        dashboard['reptime'] += i['recoverConsuming'] if i['recoverConsuming'] != '' else 0
        set_log.debug(f"[[ok]]")

        if i['isFirstLaunch'] == '首发':
            dashboard['firstlan'] += 1
        #  运维定位
        if i['isOpsLocate'] == '是':
        # if i['causeLocator'] == '运维定位':
            dashboard['opslocate'] += 1
        # if i['level'] == '已定级':
        #     dashboard['isGrade'] += 1
        if i['level'] == '非定级':
            dashboard['unGradeCount'] += 1
        if i['level'] == '未定级':
            dashboard['noneGradeCount'] += 1
        # if i['level'] == 'P1' or i['level'] == 'P2':
        #     dashboard['overP2Count'] += 1
        if i['level'] == 'P1':
            dashboard['p1Count'] += 1
        if i['level'] == 'P2':
            dashboard['p2Count'] += 1
        if i['level'] == 'P3':
            dashboard['p3Count'] += 1
        if i['level'] == 'P4':
            dashboard['p4Count'] += 1
        if i['level'] == 'P5':
            dashboard['p5Count'] += 1

        if i['replayDelay'] == '未超时' or i['replayDelay'] == '已超时':
            dashboard['replayDelayCount'] += 1
        if i['currentProgress'] == '未复盘' or i['currentProgress'] == '待复盘':
            dashboard['todoReplayCount'] += 1
        if i['simpleReportDelay'] == '未超时':
            dashboard['briefreport'] += 1
        if i['replayDelay'] == '未超时':
            dashboard['replay'] += 1
        if i['finishFileDelay'] == '未超时':
            dashboard['file'] += 1

        if i['recoveryPlan'] == '有预案':
            dashboard['recoveryplan'] += 1

    rectifys_data = RectifyBaseSerializers(rectifys_data, many=True).data
    rectifyCount = len(rectifys_data)
    dashboard['rectifyCount'] =  rectifyCount
    set_log.debug(f"[rectifys_data and rectifyCount ] {rectifys_data} {rectifyCount}")
    for i in rectifys_data:
        # i = i['fields']
        if i['rate'] == '已完成':
            dashboard['improveActionFinish'] += 1
        if i['isDelay'] == '已延期':
            dashboard['improveActionDelayCount'] += 1

    # 初始化 默认全选
    mttf_times = [(i, j) for i in ['2021', '2022', '2023'] for j in
                  ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']]
    # 年-月
    year = frontend_data.get('year', False)
    month = frontend_data.get('month', False)
    rangeTime = frontend_data.get('rangeTime', False)
    if frontend_data.get('date', False):
        for i in frontend_data['date']:
            mttf_times.append((i[:4], re.findall('(?:年)(.*)(?:月)', i)[0]))
    if year or month:
        set_log.debug(f"[year] {type(year)} {year}")
        set_log.debug(f"[month] {type(month)} {month}")
        date = []
        if year and month:
            mttf_times = []
            for i in year:
                for j in month:
                    date.append(f"{i}年{j}月")
                    mttf_times.append((i, j))
        elif not year:
            date = [f"{j}年{i}月" for i in month for j in ['2021', '2022', '2023']]
            mttf_times = [(j, i) for i in month for j in ['2021', '2022', '2023']]

        elif not month:
            date = [f"{i}年" for i in year]
            mttf_times = [(i, j) for i in year for j in
                          ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']]
        set_log.debug(f"[date] {date}")
    elif rangeTime:
        set_log.debug(f"[rangeTime] {type(rangeTime)} {rangeTime}")
        date = []
        mttf_times = []
        start_year = int(rangeTime[0][0:4])
        end_year = int(rangeTime[1][0:4])
        start_month = int(rangeTime[0][5:7])
        end_month = int(rangeTime[1][5:7])
        current_date = datetime.date(start_year, start_month, 1)
        while (current_date.year, current_date.month) <= (end_year, end_month):
            mttf_times.append((current_date.year, current_date.month))
            date.append(f"{current_date.year}年{current_date.month}月")
            if current_date.month == 12:
                current_date = datetime.date(current_date.year + 1, 1, 1)
            else:
                current_date = datetime.date(current_date.year, current_date.month + 1, 1)
    set_log.debug(f"[mttf_times] {mttf_times}")
    days = 0
    for i in mttf_times:
        set_log.debug(f"[year_mounth i] {i}")
        days += calendar.monthrange(int(i[0]), int(i[-1]))[-1]
    set_log.debug(f"[mttf_days_count faultCount] {days} {faultCount}")
    mttf_times = 60*24*days
    dashboard['mttf'] = round((mttf_times - dashboard['totalDuration']) / faultCount / 60 / 24, 1)
    dashboard['mttr'] = round(dashboard['totalDuration'] / faultCount, 1)
    dashboard['distime'] = round(dashboard['distime'] / faultCount, 1)
    dashboard['restime'] = round(dashboard['restime'] / faultCount, 1)
    dashboard['loctime'] = round(dashboard['loctime'] / faultCount, 1)
    dashboard['reptime'] = round(dashboard['reptime'] / faultCount, 1)
    dashboard['firstlan'] = round(dashboard['firstlan'] / faultCount * 100, 1)
    dashboard['opslocate'] = round(dashboard['opslocate'] / faultCount * 100, 1)
    dashboard['overP2Count'] = dashboard['p1Count'] + dashboard['p2Count']
    dashboard['overP3Count'] = dashboard['p1Count'] + dashboard['p2Count'] + dashboard['p3Count']
    dashboard['isGradeCount'] = dashboard['overP2Count'] + dashboard['p3Count'] + dashboard['p4Count'] + dashboard['p5Count']
    dashboard['isGrade'] = round(dashboard['isGradeCount'] / faultCount * 100, 1)

    dashboard['improveActionTodoCount'] = rectifyCount - dashboard['improveActionFinish']
    dashboard['improveActionFinish'] = round(dashboard['improveActionFinish'] / rectifyCount * 100, 1) if dashboard['improveActionFinish'] != 0 else dashboard['improveActionFinish']
    dashboard['improveActiondelay'] = round(dashboard['improveActionDelayCount'] / rectifyCount * 100, 1) if dashboard['improveActionDelayCount'] != 0 else dashboard['improveActionDelayCount']


    set_log.debug(f"[replayDelayCount] {dashboard['replayDelayCount']} {dashboard['todoReplayCount']}")
    dashboard['replayDelayCount'] = round((faultCount - dashboard['todoReplayCount']) / faultCount * 100, 1)
    dashboard['briefreport'] = round(dashboard['briefreport'] / faultCount * 100, 1)
    dashboard['replay'] = round(dashboard['replay'] / faultCount * 100, 1)
    dashboard['file'] = round(dashboard['file'] / faultCount * 100, 1)

    dashboard['recoveryplan'] = round(dashboard['recoveryplan'] / faultCount * 100, 1)

    set_log.debug(f"[dashboard data] {dashboard}")

    return dashboard


def get_chartline(request):
    set_log.debug(f"[request method] {request.method}")
    if request.method == 'GET':
        frontend_data = request.GET
    elif request.method == 'POST':
        frontend_data = request.data['data']
    else:
        return -4, '方法不允许'
    set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
    if 'rangeTime' not in request.data['data']['query'] or request.data['data']['query']['rangeTime'] == None or request.data['data']['query']['rangeTime'] == '':
        # 获取当前时间格式为 2024-01
        endTime = datetime.now().strftime('%Y-%m')
        startTime = (datetime.now() - timedelta(days=30*5)).strftime('%Y-%m')
        request.data['data']['query']['rangeTime'] = [startTime, endTime]
    request.data['data']['query']['level'] = ["非定级", "P1", "P2", "P4", "P3", "P5"]
    set_log.info(request.data)
    faults, _ , _ = my_selectFault(request, dashboard=True)
    chartline = {
        'time': [],
        # mttr的趋势图数据
        'mttr': {
            # 图例
            'legend': ['所有故障', '定级故障'],
            # 所有故障
            'all': [],
            # 定级故障
            'isGrade': []
        },
        # 故障数量的趋势图数据
        'count': {
            # 图例
            'legend': ['所有故障', '定级故障'],
            # 所有故障
            'all': [],
            # 定级故障
            'isGrade': [],
        },
        # 故障首发的趋势图数据
        'firstlaunch': {
            # 图例
            'legend': ['所有故障', '定级故障'],
            # 所有故障
            'all': [],
            # 定级故障
            'isGrade': [],
        },
        # 定级故障数的趋势图数据
        'level': {
            #图例
            'legend': ['P1', 'P2', 'P3', 'P4', 'P5', '非定级', 'P2及以上', 'P3及以上'],
            'p1': [],
            'p2': [],
            'p3': [],
            'p4': [],
            'p5': [],
            # 非定级
            'unGrade': [],
            # 二级及以上故障数
            'overP2': [],
            # 三级及以上故障数
            'overP3': [],
        },

    }
    # 定义起始和结束时间
    try: 
        start_time = datetime.strptime(request.data['data']['query']['rangeTime'][0], '%Y-%m')
        end_time = datetime.strptime(request.data['data']['query']['rangeTime'][1], '%Y-%m')
    except ValueError:
        start_time = datetime.strptime(request.data['data']['query']['rangeTime'][0], '%Y-%m-%d %H:%M:%S')
        end_time = datetime.strptime(request.data['data']['query']['rangeTime'][1], '%Y-%m-%d %H:%M:%S')



    # 生成时间序列
    time_list = []
    time_date = []
    while start_time <= end_time:
        year = start_time.year
        month = start_time.month
        month_last_day = start_time.replace(day=28) + timedelta(days=4)  # 快速跳转到下个月第一天，再减去一天得到当月的最后一天
        month_last_day = month_last_day.replace(day=1) - timedelta(days=1)
        time_list.append(f"{year}年{month}月")
        time_date.append(f"{year}-{month}")
        if month_last_day <= end_time:
            start_time = month_last_day + timedelta(days=1)
        else:
            break
    chartline['time'] = time_list
    if faults == []:
        return chartline

    for j in time_date: 
        all_fault_totalDuration = 0
        all_fault_count = 0
        all_fault_firstlaunch_count = 0
        non_fault_totalDuration = 0
        non_fault_count = 0
        P5_fault_totalDuration = 0
        P5_fault_firstLaunch_count = 0
        P5_fault_count = 0
        P4_fault_totalDuration = 0
        P4_fault_firstLaunch_count = 0
        P4_fault_count = 0
        P3_fault_totalDuration = 0
        P3_fault_firstLaunch_count = 0
        P3_fault_count = 0
        P2_fault_totalDuration = 0
        P2_fault_firstLaunch_count = 0
        P2_fault_count = 0
        P1_fault_totalDuration = 0
        P1_fault_firstLaunch_count = 0
        P1_fault_count = 0   
        for i in faults:
        # i = i['fields']
            search_datetime = datetime.strptime(j, '%Y-%m')
            fault_starttime = datetime.strptime(i['startTime'], '%Y-%m-%d %H:%M')
            # 提取年份和月份
            search_year = search_datetime.year
            search_month = search_datetime.month

            start_year = fault_starttime.year
            start_month = fault_starttime.month
            # 判断年月是否相等
            if search_year == start_year and search_month == start_month:
                # 所有故障总时长
                all_fault_totalDuration += i['duration'] if i['duration'] != '' else 0
                # 故障数量
                all_fault_count += 1
                # 所有故障首发数量
                if i['isFirstLaunch'] == '首发':
                    all_fault_firstlaunch_count += 1
                # 获取非定级故障数量和持续时间
                if i['level'] == '非定级':
                    # 非定级故障总时长
                    non_fault_totalDuration += i['duration'] if i['duration'] != '' else 0
                    # 非定级故障数量
                    non_fault_count += 1     
                # 获取P5故障数量和持续时间
                if i['level'] == 'P5':
                    P5_fault_totalDuration += i['duration'] if i['duration'] != '' else 0
                    P5_fault_count += 1 
                    if i['isFirstLaunch'] == '首发':
                        P5_fault_firstLaunch_count += 1
                    continue   
                # 获取P4故障数量和持续时间
                if i['level'] == 'P4':
                    P4_fault_totalDuration += i['duration'] if i['duration'] != '' else 0
                    P4_fault_count += 1
                    if i['isFirstLaunch'] == '首发':
                        P4_fault_firstLaunch_count += 1
                    continue
                # 获取P3故障数量和持续时间
                if i['level'] == 'P3':
                    P3_fault_totalDuration += i['duration'] if i['duration'] != '' else 0
                    P3_fault_count += 1
                    if i['isFirstLaunch'] == '首发':
                        P3_fault_firstLaunch_count += 1
                    continue
                # 获取P2故障数量和持续时间
                if i['level'] == 'P2':
                    P2_fault_totalDuration += i['duration'] if i['duration'] != '' else 0
                    P2_fault_count += 1
                    if i['isFirstLaunch'] == '首发':
                        P2_fault_firstLaunch_count += 1
                    continue
                # 获取P1故障数量和持续时间
                if i['level'] == 'P1':
                    P1_fault_totalDuration += i['duration'] if i['duration'] != '' else 0
                    P1_fault_count += 1
                    if i['isFirstLaunch'] == '首发':
                        P1_fault_firstLaunch_count += 1
                    continue
        
        
        if all_fault_count == 0:
            # 所有故障 mttr
            all_fault_mttr = 0
            # 所有故障 首发率
            all_fault_firstlaunch = 100
        else:
            all_fault_firstlaunch = round(all_fault_firstlaunch_count / all_fault_count * 100 , 1) 
            all_fault_mttr = round(all_fault_totalDuration / all_fault_count, 1)
        # 所有故障 首发率
        set_log.debug(f"所有首发数量 {all_fault_firstlaunch_count} {all_fault_count}")
        chartline['firstlaunch']['all'].append(all_fault_firstlaunch)
        # 所有故障 mttr    
        chartline['mttr']['all'].append(all_fault_mttr)
        # 所有故障 count
        chartline['count']['all'].append(all_fault_count)
        # 定级故障 mttr 和 count
        grade_fault_totalDuration = P1_fault_totalDuration + P2_fault_totalDuration + P3_fault_totalDuration + P4_fault_totalDuration + P5_fault_totalDuration
        grade_fault_count = P1_fault_count + P2_fault_count + P3_fault_count + P4_fault_count + P5_fault_count
        grade_fault_firstlaunch_count = P1_fault_firstLaunch_count + P2_fault_firstLaunch_count + P3_fault_firstLaunch_count + P4_fault_firstLaunch_count + P5_fault_firstLaunch_count
        set_log.debug(f"定级首发数量 {grade_fault_firstlaunch_count} {grade_fault_count}")
        if grade_fault_count == 0:
            grade_fault_mttr = 0
            grade_fault_firstlaunch = 100
        else:
            grade_fault_mttr = round(grade_fault_totalDuration / grade_fault_count, 1)
            grade_fault_firstlaunch = round(grade_fault_firstlaunch_count / grade_fault_count * 100, 1) 
        chartline['count']['isGrade'].append(grade_fault_count)
        chartline['mttr']['isGrade'].append(grade_fault_mttr)
        chartline['firstlaunch']['isGrade'].append(grade_fault_firstlaunch)
        # P1 故障数量
        chartline['level']['p1'].append(P1_fault_count)
        # P2 故障数量
        chartline['level']['p2'].append(P2_fault_count)
        # P3 故障数量
        chartline['level']['p3'].append(P3_fault_count)
        # P4 故障数量
        chartline['level']['p4'].append(P4_fault_count)
        # P5 故障数量
        chartline['level']['p5'].append(P5_fault_count)
        # 非定级 故障数量
        chartline['level']['unGrade'].append(non_fault_count)
        # P2以上 故障数量
        overP2_fault_count = P2_fault_count + P1_fault_count
        chartline['level']['overP2'].append(overP2_fault_count)
        # P3 以上故障数量
        overP3_fault_count = P3_fault_count + P2_fault_count + P1_fault_count
        chartline['level']['overP3'].append(overP3_fault_count)

        set_log.debug(f"[chartline data] {chartline}")
    return chartline

def my_createFault(request):
    try:
        set_log.info("-" * 40)
        set_log.info("Here is my_createFault")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        # 生成新fid
        today = time.strftime('%Y%m%d', time.localtime(time.time()))
        if FaultInfo.objects.exists():
            last_fid = FaultInfo.objects.all().order_by('-id')[0].fid
            set_log.debug(f"[last FID] {type(last_fid)} {last_fid}")
            set_log.debug(f"[日期比较]  {last_fid[:8]} {today}")
            if last_fid[:8] == today:
                set_log.debug(f"SAME DAY")
                fid = str(int(last_fid) + 1)
            else:
                set_log.debug(f"[NEW DAY]")
                fid = f"{today}0001"
        set_log.debug(f"[NEW FID] {fid}")
        frontend_data['fid'] = fid
        # 创建人
        user = get_user(request)
        frontend_data['creator'] = user['user_name']
        frontend_data['creatorID'] = user['TT_id']
        # 类型
        reportType = frontend_data.get('reportType', '')
        frontend_data['stage'] = '未发布'

        try:
            set_log.debug(f"[rectify]")
            if frontend_data.get('improveAction', False):
                for i in frontend_data['improveAction']:

                    i['fid'] = fid
                    i['faultDescribe'] = frontend_data['faultDescribe']
                    i['faultTime'] = frontend_data.get('faultMonth', '')
                    i['reportType'] = reportType
                    rectify = RectifyInfo(**i)
                    rectify.save()
                    rectify.iid = hashlib.md5(str(rectify.pk).encode("utf8")).hexdigest()
                    rectify.save()
        except Exception as e:
            set_log.error(f"[save rectify error]{e}")
            return 1003, e

        try:
            set_log.debug(f"[playback]")
            if frontend_data.get('playback', False):
                for i in frontend_data['playback']:
                    i['fid'] = fid
                    playback = PlaybackInfo(**i)
                    playback.save()
        except Exception as e:
            set_log.error(f"[save playback error]{e}")
            return 1003, e

        try:
            set_log.debug(f"[fault]")
            frontend_data['improveAction'] = RectifySerializers(RectifyInfo.objects.filter(fid=fid, stage="已发布"), many=True).data
            frontend_data['playback'] = PlaybackSerializers(PlaybackInfo.objects.filter(fid=fid), many=True).data
            fault = FaultInfo(**frontend_data)
            fault.save()
        except Exception as e:
            set_log.error(f"[save fault error]{e}")
            return 1003, e


        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_uploadPhotp(request):
    from obs import ObsClient, HeadPermission  # 用于连接华为云OBS的库
    set_log.info("-" * 50)
    set_log.debug("Here is my_uploadPhotp")
    # cookie = myget_cookie(request)
    # operator = get_user(request)
    if not request.FILES.get('file', False):
        return 1010, '图片上传失败,未获取到上传图片'
    img = request.FILES.get('file').read()
    set_log.debug(f"frontend files {request.FILES}")
    data = request.data
    set_log.debug(f"[frontend] {request.data}")
    obsClient = ObsClient(
        # access_key_id='PTIF140E2NIGOIXBXWMN',
        # secret_access_key='1i05SIkeIBb7Ms44xulQwC2Du8YryV2IGm9CZKws',
        # server='obs.cn-north-4.myhuaweicloud.com'
        access_key_id=itmp_conf['access_key_id'],  # 你的华为云的ak码
        secret_access_key=itmp_conf['secret_access_key'],  # 你的华为云的sk
        server=itmp_conf['server']  # 你的桶的地址
    )
    # bucketname = 'obs-test-hw-gz-yw-fmp-backend'
    bucketname = itmp_conf['bucketname']
    set_log.debug(f"bucket: {itmp_conf['bucketname'], itmp_conf['server']}")

    # uid = request.FILES.get('file')
    # set_log.debug(f"uid: {uid}")
    # 同名会覆盖
    title = request.FILES.get('file').name
    set_log.debug(f"[img name] {title}")

    # type_name = ""
    uid = data.get('uid', 'error_id')

    # img = base64.b64decode(img)
    set_log.debug(f"img: {type(img)},uid: {uid}")
    # 上传图片
    res = obsClient.putContent(bucketname, uid, content=img)
    set_log.debug(f"obs res: {res}")

    if res.status < 300:
        url = res.body.objectUrl
        # 开权限
        obsClient.setObjectAcl(bucketname, uid, aclControl=HeadPermission.PUBLIC_READ)

        return_data = {
            "uid": uid,
            "url": url,
            "name": title,
            "type": title.split(".")[-1]
        }
        return 20000, return_data
    else:
        set_log.error(f"errorCode: {res.errorCode}")
        set_log.error(f"errorMessage: {res.errorMessage}")
        # return Response(return_code(-99, data='调试中', msg='调试中', operator=operator))
        # return Response(return_code(-2, data='图片上传失败', msg='图片上传失败', operator=operator))
        # 触发告警
        # return 1010, data='图片上传失败', msg='图片上传失败', operator=operator)
        return 1010, "图片上传失败"

def my_editFault(request):
    try:
        set_log.info("-" * 40)
        set_log.info("Here is my_editFault")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            fid = frontend_data.get("fid", False)
            if not fid:
                return 1009, "缺少fid"
            fault = FaultInfo.objects.get(fid=fid)
            effectSLO = fault.effectSLO
            slaDetail = fault.slaDetail
            # 更新整改措施
            try:
                rectify = frontend_data.get('improveAction', False)
                if rectify:
                    # 对比区别
                    # rectify = json.dumps(rectify)
                    set_log.debug(f"[rectify] {rectify}")
                    set_log.debug(f"rectify比较 \n [NEW] {rectify} \n [OLD] {fault.improveAction}")
                    if json.dumps(rectify) != json.dumps(fault.improveAction):
                        set_log.info(f"[rectify 发生改变开始同步]")
                        del_iid = [k['id'] for k in RectifyInfo.objects.filter(fid=fid).values('id')]
                        set_log.debug(f"[old id] {del_iid}")
                        for i in rectify:
                            set_log.debug(f"[one rectify] {i}")
                            if i.get('id', False):
                                set_log.info(f"[rectify {i['id']} 已存在 UPDATE]")
                                del_iid.remove(i['id'])
                                RectifyInfo.objects.filter(pk=i['id']).update(**i)
                            else:
                                set_log.info(f"[NEW RECTIFY CREATE]")
                                i['fid'] = fid
                                i['faultDescribe'] = frontend_data['faultDescribe']
                                i['faultTime'] = frontend_data.get('faultMonth', '')
                                i['reportType'] = fault.reportType
                                rectify = RectifyInfo(**i)
                                rectify.save()
                            # i = RectifySerializers(rectify).data
                        # 删除多余rectify
                        set_log.debug(f"[del_id] {del_iid}")
                        # for d in del_id:
                        #     del_rectify = RectifyInfo.objects.get(PK=d)
                        #     del_rectify.delete()
                        del_rectify = RectifyInfo.objects.filter(pk__in=del_iid)
                        set_log.debug(f"需要删除的rectify: {del_rectify}")
                        del_rectify.delete()
                    # temp =
            except Exception as e:
                set_log.error(f"[save rectify error]{e}")
                return 1003, e
            # 更新故障回放
            try:
                playback = frontend_data.get('playback', False)
                if playback:
                    # 对比区别
                    set_log.debug(f"[playback] {playback}")
                    set_log.debug(f"playback比较 \n [NEW] {playback} \n [OLD] {fault.playback}")
                    if json.dumps(playback) != json.dumps(fault.playback):
                        set_log.info(f"[playback 发生改变开始同步]")
                        del_pid = [k['id'] for k in PlaybackInfo.objects.filter(fid=fid).values('id')]
                        set_log.debug(f"[old id] {del_pid}")
                        for i in playback:
                            set_log.debug(f"[one playback] {i}")
                            if i.get('id', False):
                                set_log.info(f"[playback {i['id']} 已存在 UPDATE]")
                                del_pid.remove(i['id'])
                                PlaybackInfo.objects.filter(pk=i["id"]).update(**i)
                            else:
                                set_log.info(f"[NEW PLAYBACK CREATE]")
                                i['fid'] = fid
                                playback = PlaybackInfo(**i)
                                playback.save()
                            # i = PlaybackSerializers(playback).data
                        # 删除多余playback
                        set_log.debug(f"[del_pid] {del_pid}")
                        # for d in del_pid:
                        #     del_playback = PlaybackInfo.objects.get(PK=d)
                        #     del_playback.delete()
                        del_playback = PlaybackInfo.objects.filter(pk__in=del_pid)
                        #set_log.debug(f"需要删除的rectify: {del_playback}")
                        if del_playback.exists():  # 检查是否存在需要删除的记录
                            del_playback.delete()
                            set_log.info(f"成功删除指定记录 {del_playback}")
                        else:
                            set_log.info("没有匹配到需要删除的记录")
            except Exception as e:
                set_log.error(f"[save playback error]{e}")
                return 1003, e
            # 变更 整改措施/问题同步字段
            try:
                # 变更字段同步
                RectifyInfo.objects.filter(fid=fid).update(faultDescribe=frontend_data.get('faultDescribe', fault.faultDescribe), faultTime=frontend_data.get('faultMonth', fault.faultMonth))
                set_log.info(f"[save rectify.faultinfo SUCCESS]")
            except Exception as e:
                set_log.error(f"[save rectify.faultinfo error] {e}")
                return 1003, e
            try:
                frontend_data['improveAction'] = RectifySerializers(RectifyInfo.objects.filter(fid=fid, stage="已发布"), many=True).data
                frontend_data['playback'] = PlaybackSerializers(PlaybackInfo.objects.filter(fid=fid), many=True).data
                # if frontend_data['reportType'] == '故障':
                #     frontend_data['currentProgress'] = "待复盘"
                # elif frontend_data['reportType'] != '故障':
                #     frontend_data['currentProgress'] = "未归档"
                FaultInfo.objects.filter(fid=fid).update(**frontend_data)
            except Exception as e:
                set_log.error(f"[save fault error] {e}")
                return 1003, e
            # 天相SLA关联故障
            # try:
            #     if frontend_data['reportType'] == '故障':
            #         # 判断effectSLO是否修改
            #         frontend_data['effectSLO'] = json.dumps(frontend_data.get('effectSLO', effectSLO))
            #         frontend_data['slaDetail'] = json.dumps(frontend_data.get('slaDetail', slaDetail))
            #         set_log.debug(f"[effectSLO] {type(effectSLO)}")
            #         set_log.debug(f"[frontend_data effectSLO] {type(frontend_data['effectSLO'])}")

            #         # 发生改变同步天相
            #         set_log.debug(f"effectSLO比较 \n [NEW] {frontend_data['effectSLO']} \n [OLD] {effectSLO}")
            #         if frontend_data['effectSLO'] != effectSLO:
            #             url = itmp_conf['Telemetry_update_effectSLO_url']
            #             post_data = {
            #                 "fid": fid,
            #                 "faultDescribe": frontend_data['faultDescribe'],
            #                 "reportType": frontend_data['reportType'],
            #                 "startTime": frontend_data['startTime'],
            #                 "recoverTime": frontend_data['recoverTime'],
            #                 "effectSLO": effectSLO
            #             }
            #             headers = {'Content-Type': 'application/json'}
            #             set_log.debug(f"[天相同步数据] {post_data}")
            #             req = requests.post(url=url, data=json.dumps(post_data), headers=headers)
            #             if req.status_code != 200:
            #                 set_log.error(f"change fault false : {req} {req.json()}")
            #                 # return (1004, req.json())
            #         set_log.info(f"[同步结果] {req} {req.json()}")
            # except Exception as e:
            #     set_log.error(f"[同步天相失败] {e}")
            #     return 1003, e

        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e 
        data = FaultInfo.objects.filter(fid=fid)
        data = FaultSerializers(data[0:10], many=True).data
        set_log.info(f"[查询结果] {data}")
        return 20000, data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_deleteFault(request):
    try:
        set_log.info("-" * 40)
        set_log.info("Here is my_deleteFault")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("fid", False):
                return 1009, "缺少fid"
            fault = FaultInfo.objects.get(fid=frontend_data['fid'])
            fault.stage = "已删除"
            fault.save()
            # 同步整改措施删除
            RectifyInfo.objects.filter(fid=frontend_data['fid']).update(stage="已删除")
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_realdeleteFault(request):
    try:
        set_log.info("-" * 40)
        set_log.info("Here is my_realdeleteFault")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("fid", False):
                return 1009, "缺少fid"
            fault = FaultInfo.objects.get(fid=frontend_data['fid'])
            fault.delete()
            # 同步整改措施删除
            RectifyInfo.objects.filter(fid=frontend_data['fid']).delete()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data

    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def editFilterTree(request):
    try:
        print("-" * 40)
        set_log.info("Here is editFilterTree")
        frontend_data = request.data
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            id = frontend_data.get("id")
            set_log.debug(f"[id] {id}")
            if id is not None:
                id = frontend_data["id"]
                # 获取要更新的对象
                fltertree = FilterTree.objects.get(pk=id)
                # 更新字段
                fltertree.data = frontend_data["data"]
                fltertree.save()
            else:
                fltertree = FilterTree(**frontend_data)
                fltertree.save()
        except Exception as e:
            set_log.info(f"[editFilterTree FALSE]")
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.info(f"[editFilterTree FALSE]")
        set_log.error(f"error: {e}")
        return -1, str(e)

def selectFilterTree(request, **kwargs):
    try:
        print("-" * 40)
        set_log.info("Here is selectFilterTree")
        set_log.debug(f"[request method] {request.method}")
        if request.method == 'GET':
            set_log.debug(f"[request method] --- GET ---")
            frontend_data = request.GET
        elif request.method == 'POST':
            set_log.debug(f"[request method] --- POST ---")
            frontend_data = request.data['data']
        else:
            return -4, '方法不允许'
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        if not frontend_data.get("id", True):
            filter_data = FilterTree.objects.filter(pk=id, stage="已发布")
        else:
            filter_data = FilterTree.objects.filter(stage="已发布")
            set_log.debug(f"[filter_data] {filter_data}")
        data_results = FilterTreeBaseSerializers(filter_data, many=True).data
        if not data_results:
            set_log.error(f"查询结果为空：{data_results}")
            return [], 0
        set_log.debug(f"[data_results] {data_results}")   
        data = data_results[0].get('data')
        return 20000, data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)
def deleteFilterTree(request):
    try:
        print("-" * 40)
        set_log.info("Here is deleteFilterTree")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("id", False):
                return 1009, "缺少id"
            id = frontend_data['id']
            filtertree = FilterTree.objects.get(pk=id)
            filtertree.stage = "已删除"
            filtertree.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def editFilterTreeNode(request):
    try:
        print("-" * 40)
        set_log.info("Here is editFilterTreeNode")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("id", True):
                id = frontend_data["id"]
                # 获取要更新的对象
                fltertreenode = FilterTreeNode.objects.get(pk=id)
                return_data = fltertreenode.__dict__
                return_data.pop('_state', None)
                # 更新字段
                for key, value in frontend_data.items():
                    setattr(fltertreenode, key, value)
                fltertreenode.save()
            else:
                fltertreenode = FilterTreeNode(**frontend_data)
                fltertreenode.save()
                saved_data = FilterTreeNode.objects.get(pk=fltertreenode.pk)
                return_data = saved_data.__dict__
                # 移除不需要的内部属性
                return_data.pop('_state', None)
        except Exception as e:
            set_log.info(f"[editFilterTreeNode FALSE]")
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, return_data
    except Exception as e:
        set_log.info(f"[editFilterTreeNode FALSE]")
        set_log.error(f"error: {e}")
        return -1, str(e)

def selectFilterTreeNode(request, **kwargs):
    try:
        print("-" * 40)
        set_log.info("Here is electFilterTreeNode")
        set_log.debug(f"[request method] {request.method}")
        if request.method == 'GET':
            set_log.debug(f"[request method] --- GET ---")
            frontend_data = request.GET
        elif request.method == 'POST':
            set_log.debug(f"[request method] --- POST ---")
            frontend_data = request.data['data']
        else:
            return -4, '方法不允许'
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        id = frontend_data.get('id', "")  
        filter_data = FilterTreeNode.objects.filter(pk=id, stage="已发布")
        data_results = FilterTreeNodeBaseSerializers(filter_data, many=True).data
        if not data_results:
            set_log.error(f"查询结果为空：{data_results}")
            return [], 0
        return 20000, data_results
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)
def deleteFilterTreeNode(request):
    try:
        print("-" * 40)
        set_log.info("Here is deleteFilterTreeNode")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("id", False):
                return 1009, "缺少id"
            id = frontend_data['id']
            checklist = FilterTreeNode.objects.get(pk=id)
            checklist.stage = "已删除"
            checklist.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            return 1003, e
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)
def get_SLA_SLO_list(request):
    try:
        # cookie = get_cookie(request)
        # 获取请求参数
        frontend_data = request.data
        url = itmp_conf['Telemetry_sla_slo_url']
        data = {
            "page": 1,
            "limit": 1
        }
        req = requests.post(url=url, data=data)
        set_log.debug(f"[SLA req_data] {req.json()}")
        data = req.json()['data']['sla']
        ret_data = []
        for i in data:
            temp = {"value": i['id'], "label": i['sla_name']}
            if i['slo_info']['slo_list'] == None:
                pass
            else:
                temp["children"] = []
                for j in i['slo_info']['slo_list']:
                    temp["children"].append({"value": j['id'], "label": j['slo_conf_name']})
            ret_data.append(temp)
        set_log.debug(f"[ret_data] {ret_data}")
        return 0, ret_data
    except Exception as e:
        set_log.error(f"[error] {e}")
        return -1, "SLA信息获取失败"

def get_SLO_score(request):
    try:
        frontend_data = request.data
        set_log.debug(f"[frontend_data] {frontend_data}")
        # set_log.debug(f"[time type] {type(frontend_data['endTime'])}")
        startTime = frontend_data['startTime']
        if len(startTime) != 16 and startTime != "":
            startTime = startTime[-3]
        endTime = frontend_data['endTime']
        if len(endTime) != 16 and endTime != "":
            endTime = endTime[-3]

        url = itmp_conf['Telemetry_slo_score_url']
        data = {
            "startTime": startTime+":00",
            "endTime": endTime+":00",
            "sloId": frontend_data['sloId']
        }
        headers = {'Content-Type': 'application/json'}
        set_log.debug(f"[post] [url] {url} [data] {data}")
        req = requests.post(url=url, data=json.dumps(data), headers=headers)
        set_log.debug(f"[req slo score] {req} {req.json()}")
        return_data = req.json()['data']
        return 0, return_data
    except Exception as e:
        set_log.error(f"[error] {e}")
        return -1, "SLO信息获取失败"




