
from django.db import models
import django.utils.timezone as timezone


# Create your models here.

class FaultInfo(models.Model):


    id = models.AutoField(primary_key=True)
    # blank=True  允许表单为空  null=True 允许数据库值为空
    # fid = models.IntegerField(null=True)
    fid = models.CharField(max_length=256, db_column="fid", blank=True, null=True, verbose_name="fid", unique=True)
    # 故障描述
    faultDescribe = models.CharField(max_length=256, blank=True, null=True, db_column="faultDescribe",
                                     verbose_name="故障描述", default="")
    # 会议纪要
    meetingMinutes = models.CharField(max_length=256, blank=True, null=True, db_column="meetingMinutes",
                                      verbose_name="会议纪要", default="")
    # 当前进度
    currentProgress = models.CharField(max_length=256, blank=True, null=True, db_column="currentProgress",
                                       verbose_name="当前进度", default="")
    # 年份 时间
    # faultYear = models.DateField(blank=True, db_column="fault_year")
    faultYear = models.CharField(max_length=256, blank=True, null=True, db_column="faultYear", verbose_name="年份", default="")
    # 月份
    faultMonth = models.CharField(max_length=256, blank=True, null=True, db_column="faultMonth", verbose_name="月份", default="")
    # 季度
    faultQuarter = models.CharField(max_length=256, blank=True, null=True, db_column="faultQuarter", verbose_name="季度", default="")
    # 故障开始时间
    startTime = models.DateTimeField(blank=True, null=True, db_column="startTime", verbose_name="故障开始时间")
    # 故障发现时间
    discoveryTime = models.DateTimeField(blank=True, null=True, db_column="discoveryTime", verbose_name="故障发现时间")
    # 发现耗时
    discoveryConsuming = models.IntegerField(blank=True, null=True, db_column="discoveryConsuming",
                                             verbose_name="发现耗时")
    # 响应时间
    responseTime = models.DateTimeField(blank=True, null=True, db_column="responseTime", verbose_name="故障响应时间")
    # 响应耗时
    responseConsuming = models.IntegerField(blank=True, null=True, db_column="responseConsuming", verbose_name="响应耗时")
    # 定位时间
    loacteTime = models.DateTimeField(blank=True, null=True, db_column="loacteTime", verbose_name="定位时间")
    # 定位耗时
    loacteConsuming = models.IntegerField(blank=True, null=True, db_column="loacteConsuming", verbose_name="定位耗时")
    # 恢复时间
    recoverTime = models.DateTimeField(blank=True, null=True, db_column="recoverTime", verbose_name="恢复时间")
    # 恢复耗时
    recoverConsuming = models.IntegerField(blank=True, null=True, db_column="recoverConsuming", verbose_name="恢复耗时")
    # 持续时长
    duration = models.IntegerField(blank=True, null=True, db_column="duration", verbose_name="持续时长")
    # 故障回放 长文本
    playback = models.JSONField(blank=True, null=True, db_column="playback", verbose_name="故障回放", default=[])
    # 根因归类
    causeClassify = models.CharField(max_length=256, blank=True, null=True, db_column="causeClassify",
                                     verbose_name="根因归类", default="")
    # 变更或其他原因
    isChange = models.CharField(max_length=256, blank=True, null=True, db_column="isChange", verbose_name="是否变更", default="")
    # 基础设施
    infrastructure = models.CharField(max_length=256, blank=True, null=True, db_column="infrastructure",
                                      verbose_name="基础设施", default="")
    # 其他原因维度
    otherReason = models.JSONField(blank=True, null=True, db_column="otherReason",
                                   verbose_name="其他维度原因", default=[])
    # 根因描述
    causeDescribe = models.TextField(blank=True, null=True, db_column="causeDescribe", verbose_name="根因描述")
    # 服务故障归类
    serviceClassification = models.CharField(max_length=256, blank=True, null=True, db_column="serviceClassification",
                                             verbose_name="服务故障归类", default="")
    # 影响产品
    isEffectProduce = models.JSONField(blank=True, null=True, db_column="isEffectProduce",
                                       verbose_name="影响产品", default=[])
    # 对营收的影响
    directEffectRevenue = models.CharField(max_length=256, blank=True, null=True, db_column="directEffectRevenue",
                                           verbose_name="对营收的影响", default="")
    # 是否定级
    isGrade = models.CharField(max_length=256, blank=True, null=True, db_column="isGrade", verbose_name="是否定级", default="")
    # 故障等级
    level = models.CharField(max_length=256, blank=True, null=True, db_column="level", verbose_name="故障等级", default="")
    # 定级依据
    levelBasis = models.CharField(max_length=256, blank=True, null=True, db_column="levelBasis", verbose_name="定级依据", default="")
    # 影响内容
    effectContent = models.TextField(blank=True, null=True, db_column="effectContent", verbose_name="影响内容")
    # 是否影响对外业务
    isEffectOutside = models.CharField(max_length=256, blank=True, null=True, db_column="isEffectOutside",
                                       verbose_name="是否影响对外业务", default="")
    # 是否有监控
    hasWatch = models.CharField(max_length=256, blank=True, null=True, db_column="hasWatch", verbose_name="是否有监控", default="")
    # 是否有告警
    hasAlert = models.CharField(max_length=256, blank=True, null=True, db_column="hasAlert", verbose_name="是否有告警", default="")
    # 是否首发
    isFirstLaunch = models.CharField(max_length=256, blank=True, null=True, db_column="isFirstLaunch",
                                     verbose_name="是否首发", default="")
    # 首发来源
    firstLaunchSource = models.JSONField(blank=True, null=True, db_column="firstLaunchSource",
                                         verbose_name="首发来源", default=[])
    # 反馈来源
    feedbackSource = models.CharField(max_length=256, blank=True, null=True, db_column="feedbackSource",
                                      verbose_name="反馈来源", default="")
    # 根因定位角色
    causeLocator = models.CharField(max_length=256, blank=True, null=True, db_column="causeLocator",
                                    verbose_name="根因定位角色", default="")
    # 是否运维定位
    isDevLocate = models.CharField(max_length=256, blank=True, null=True, db_column="isDevLocate", verbose_name="是否运维定位", default="")
    # 是否研发定位
    isOpsLocate = models.CharField(max_length=256, blank=True, null=True, db_column="isOpsLocate", verbose_name="是否研发定位", default="")


    # 协助根因定位工具
    causeLocateTool = models.JSONField(blank=True, null=True, db_column="causeLocateTool",
                                       verbose_name="协助根因定位工具", default=[])
    # 故障整改措施
    improveAction = models.JSONField(blank=True, null=True, db_column="improveAction", verbose_name="故障整改措施", default=[])
    # 故障处理人员
    handler = models.JSONField(blank=True, null=True, db_column="handler", verbose_name="故障处理人员",
                               default=[])
    # Driver
    driver = models.CharField(max_length=256, blank=True, null=True, db_column="driver", verbose_name="Driver", default="")
    # 记录员
    recorder = models.CharField(max_length=256, blank=True, null=True, db_column="recorder", verbose_name="记录员", default="")
    # 简报完成时间
    simpleReportTime = models.DateTimeField(blank=True, null=True, db_column="simpleReportTime",
                                            verbose_name="简报完成时间", default=timezone.now)
    # 简报是否超时
    simpleReportDelay = models.CharField(max_length=256, blank=True, null=True, db_column="simpleReportDelay",
                                         verbose_name="简报是否超时", default="")
    # 会前准备时间
    preparationTime = models.DateTimeField(blank=True, null=True, db_column="preparationTime", verbose_name="会前准备时间")
    # 复盘时间
    replayTime = models.DateTimeField(blank=True, null=True, db_column="replayTime", verbose_name="复盘时间")
    # 复盘是否超时
    replayDelay = models.CharField(max_length=256, blank=True, null=True, db_column="replayDelay",
                                   verbose_name="复盘是否超时", default="")
    # 归档时间
    finishFileTime = models.DateTimeField(blank=True, null=True, db_column="finishFileTime", verbose_name="归档时间")
    # 归档是否超时
    finishFileDelay = models.CharField(max_length=256, blank=True, null=True, db_column="finishFileDelay",
                                          verbose_name="归档是否超时", default="")
    # 恢复预案
    recoveryPlan = models.CharField(max_length=256, blank=True, null=True, db_column="recoveryPlan",
                                    verbose_name="恢复预案", default="")
    # 创建者
    creator = models.CharField(max_length=256, blank=True, null=True, db_column="creator", verbose_name="创建者", default="")
    creatorID = models.CharField(max_length=256, blank=True, null=True, db_column="creatorID", verbose_name="创建者ID", default="")
    # 状态
    stage = models.CharField(max_length=256, blank=True, null=True, db_column="stage", verbose_name="状态", default="未发布")

    # 报告类型 故障报告/需复盘问题/问题
    reportType = models.CharField(max_length=256, blank=True, null=True, db_column="reportType", verbose_name="报告类型", default="")

    # 架构图
    architectureDiagram = models.JSONField(blank=True, null=True, db_column="architectureDiagram",
                                           verbose_name="架构图", default=[])
    # 故障排查过程汇总
    troubleshooting = models.JSONField(blank=True, null=True, db_column="troubleshooting",
                                       verbose_name="故障排查汇总", default=[])
    # 是否运营类
    isOperation = models.CharField(max_length=256, blank=True, null=True, db_column="isOperation", verbose_name="是否运营类", default="")

    problem = models.CharField(max_length=65535, blank=True, null=True, db_column="problem", verbose_name="问题", default="")

    # 处理群连接
    groupLink = models.CharField(max_length=2048, blank=True, null=True, db_column="groupLink", verbose_name="处理群", default="")

    # 是否有SLA
    hasSLA = models.CharField(max_length=256, blank=True, null=True, db_column="hasSLA", verbose_name="是否有SLA协议", default="")

    # 是否影响SLO达标率
    isEffectSLA = models.CharField(max_length=256, blank=True, null=True, db_column="isEffectSLA", verbose_name="是否影响SLA达标率", default="")

    # 达标率下降比
    declineSLA = models.IntegerField(blank=True, null=True, db_column="declineSLA", verbose_name="达标率下降比")

    # 关联sla
    slaDetail = models.JSONField(blank=True, null=True, db_column="slaDetail", verbose_name="关联sla", default=[])

    # 关联SLO
    effectSLO = models.JSONField(blank=True, null=True, db_column="effectSLO", verbose_name="关联SLO", default=[])

    # 预算消耗时长
    budgetSLA = models.IntegerField(blank=True, null=True, db_column="budgetSLA", verbose_name="预算消耗时长")

    # 是否需要复盘
    isReplay = models.CharField(max_length=256, blank=True, null=True, db_column="isReplay", verbose_name="是否需要复盘", default="")

    # 复盘参与人
    participants = models.JSONField(blank=True, null=True, db_column="participants", verbose_name="复盘参与人", default=[])

    # 整改措施完成时间
    improvementFinishTime = models.DateTimeField(blank=True, null=True, db_column="improvementFinishTime", verbose_name="整改措施完成时间")

    # 故障报告最后修改人
    lastUpdateUser = models.CharField(max_length=256, blank=True, null=True, db_column="lastUpdateUser", verbose_name="故障报告最后修改人", default="")

    # 故障报告最后修改时间
    lastUpdateTime = models.DateTimeField(blank=True, null=True, db_column="lastUpdateTime", verbose_name="故障报告最后修改时间")
    objects = models.Manager()

    class Meta:
        app_label = "fault"
        db_table = 'fault_info'

    def __str__(self):
        return self.fid


class CheckList(models.Model):
    id = models.AutoField(primary_key=True)  
    task = models.CharField(max_length=256, blank=True, null=True, db_column="task", verbose_name="任务项")
    describe = models.CharField(max_length=256, blank=True, null=True, db_column="describe", verbose_name="描述")
    creator = models.CharField(max_length=256, blank=True, null=True, db_column="creator", verbose_name="创建人")
    updatetor = models.CharField(max_length=256, blank=True, null=True, db_column="updatetor", verbose_name="更新人")
    createTime = models.DateTimeField(blank=True, null=True, db_column="createTime", verbose_name="创建时间")
    updateTime = models.DateTimeField(blank=True, null=True, db_column="updateTime", verbose_name="更新时间")
    stage = models.CharField(max_length=256, blank=True, null=True, db_column="stage", verbose_name="状态")
    objects = models.Manager()
    class Meta:
        app_label = "fault"
        db_table = 'checkList'
    
class FilterTree(models.Model):
    id = models.AutoField(primary_key=True)
    data = models.JSONField(blank=True, null=True, db_column="data", verbose_name="数据", default=[])
    stage = models.CharField(max_length=256, blank=True, null=True, db_column="stage", verbose_name="状态", default="已发布")
    objects = models.Manager()
    class Meta:
        app_label = "fault"
        db_table = 'filterTree'

class FilterTreeNode(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=256, blank=True, null=True, db_column="name", verbose_name="名称")
    query = models.JSONField(blank=True, null=True, db_column="query", verbose_name="查询条件", default=[])
    filterTreeID = models.CharField(max_length=256, blank=True, null=True, db_column="filterTreeID", verbose_name="预设树节点ID")
    stage = models.CharField(max_length=256, blank=True, null=True, db_column="stage", verbose_name="状态", default="已发布")
    saveType = models.CharField(max_length=256, blank=True, null=True, db_column="saveType", verbose_name="保存类型", default="")
    objects = models.Manager()
    class Meta:
        app_label = "fault"
        db_table = 'filterTreeNode'

class RectifyInfo(models.Model):
    # id, fid,fault_describe, rectify_describe, type, fault_time, start_time, estimate_time, consuming, rate, complete_time, is_delay, driver, department,
    id = models.AutoField(primary_key=True)
    iid = models.CharField(max_length=256, blank=True, null=True, db_column="iid", verbose_name="iid")
    fid = models.CharField(max_length=256, blank=True, null=True, db_column="fid", verbose_name="fid")
    faultDescribe = models.CharField(max_length=256, blank=True, null=True, db_column="faultDescribe",  verbose_name="故障描述")
    rectifyDescribe = models.CharField(max_length=256, blank=True, db_column="rectifyDescribe", null=True, verbose_name="整改描述")
    type = models.CharField(max_length=256, blank=True, null=True, db_column="type", verbose_name="类型", default="")
    faultTime = models.CharField(max_length=256, blank=True, null=True, db_column="faultTime", verbose_name="故障时间", default="")
    startTime = models.DateTimeField(blank=True, null=True, db_column="startTime", verbose_name="整改开始时间")
    # 首次预计完成时间
    estimateTime = models.DateTimeField(blank=True, null=True, db_column="estimateTime", verbose_name="预计完成时间")
    # consuming = models.IntegerField(blank=True, null=True, db_column="consuming", verbose_name="耗时")
    consuming = models.CharField(max_length=256, blank=True, null=True, db_column="consuming", verbose_name="耗时", default="")
    rate = models.CharField(max_length=256, blank=True, null=True, db_column="rate", verbose_name="整改进度", default="")
    completeTime = models.DateTimeField(blank=True, null=True, db_column="completeTime", verbose_name="实际完成时间")
    isDelay = models.CharField(max_length=256, blank=True, null=True, db_column="isDelay", verbose_name="是否超时", default="")
    driver = models.CharField(max_length=256, blank=True, null=True, db_column="driver",  verbose_name="整改负责人", default="")
    department = models.CharField(max_length=256, blank=True, null=True, db_column="department",  verbose_name="所属部门", default="")
    # 状态
    stage = models.CharField(max_length=256, blank=True, null=True, db_column="stage", verbose_name="状态", default="")
    # 添加相关文档
    fileUrl = models.JSONField(blank=True, null=True, db_column="fileUrl", verbose_name="相关文档", default=[])
    # 延期时间
    delayDate = models.DateTimeField(blank=True, null=True, db_column="delayDate", verbose_name="延期完成时间")
    # 当前预计完成时间
    delayTime = models.DateTimeField(blank=True, null=True, db_column="delayTime", verbose_name="当前预计完成时间")
    # 历史延期时间
    historyDelayTime = models.JSONField(blank=True, null=True, db_column="historyDelayTime", verbose_name="历史延期时间", default=[])
    # 报告类型 故障报告/需复盘问题/问题
    reportType = models.CharField(max_length=256, blank=True, null=True, db_column="reportType", verbose_name="报告类型", default="")
    # 整改措施类型
    rectifyType = models.CharField(max_length=256, blank=True, null=True, db_column="rectifyType", verbose_name="整改措施类型", default="")
    # 紧急程度
    level = models.CharField(max_length=256, blank=True, null=True, db_column="level", verbose_name="紧急程度", default="")
    # 备注
    remark = models.CharField(max_length=256, blank=True, null=True, db_column="remark",  verbose_name="整改", default="")
    objects = models.Manager()

    class Meta:
        app_label = "fault"
        db_table = 'rectify_info'

class CheckResult(models.Model):
    id = models.AutoField(primary_key=True)
    checklist = models.ForeignKey(CheckList, on_delete=models.CASCADE)
    iscomplete = models.CharField(max_length=256, blank=True, null=True, db_column="iscomplete", verbose_name="是否验收完成")
    checkResult = models.CharField(max_length=256, blank=True, null=True, db_column="checkResult", verbose_name="验收结果")
    checkPerson = models.CharField(max_length=256, blank=True, null=True, db_column="checkPerson", verbose_name="验收人")
    checkTime = models.DateTimeField(blank=True, null=True, db_column="checkTime", verbose_name="验收完成时间")
    approver = models.CharField(max_length=256, blank=True, null=True, db_column="approver", verbose_name="审批人")
    approverResult = models.CharField(max_length=256, blank=True, null=True, db_column="approverResult", verbose_name="审批结果")
    approverTime = models.DateTimeField(blank=True, null=True, db_column="approverTime", verbose_name="审批完成时间")
    stage = models.CharField(max_length=256, blank=True, null=True, db_column="checkresultstage", verbose_name="状态")
    rectify = models.ForeignKey(RectifyInfo, on_delete=models.CASCADE, db_column="Rectify", verbose_name="关联整改措施")
    objects = models.Manager()
    class Meta:
        app_label = "fault"
        db_table = 'checkresult'


class PlaybackInfo(models.Model):
    # 唯一id:pid, 绑定故障id: fid, 阶段: stage, 内容:content, 具体时间:time
    id = models.AutoField(primary_key=True)
    fid = models.CharField(max_length=256, blank=True, null=True, db_column="fid", verbose_name="fid")
    stage = models.CharField(max_length=256, blank=True, null=True, db_column="stage", verbose_name="阶段")
    time = models.DateTimeField(blank=True, null=True, db_column="time", verbose_name="时间")
    content = models.CharField(max_length=256, blank=True, null=True, db_column="content", verbose_name="内容", default="")
    image = models.JSONField(blank=True, null=True, db_column="image", default=[])

    objects = models.Manager()


    class Meta:
        app_label = "fault"
        db_table = 'playback_info'


    def __str__(self):
        return 

    def __unicode__(self):
        return 

