from rest_framework import serializers
from rest_framework.decorators import action
from .models import *


class FaultBaseSerializers(serializers.ModelSerializer):
    """故障信息基础序列化"""

    class Meta:
        model = FaultInfo
        fields = '__all__'

class FaultSerializers(serializers.ModelSerializer):
    """故障信息序列化"""
    startTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    discoveryTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    responseTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    loacteTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    recoverTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    simpleReportTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    preparationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    replayTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    finishFileTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    improvementFinishTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    lastUpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    class Meta:
        model = FaultInfo
        field = '__all__'
        exclude = ["id"]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for i,j in data.items():
            if not j and j != [] and j != 0:
                data[i] = ""
        return data

class CheckListBaseSerializers(serializers.ModelSerializer):
    """
    检查项基础序列化
    """
    createTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M", required=False, read_only=True)
    updateTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M", required=False, read_only=True)
    class Meta:
        model = CheckList
        fields = "__all__"

class FilterTreeBaseSerializers(serializers.ModelSerializer):        
    """
    树形结构基础序列化
    """
    class Meta:
        model = FilterTree
        fields = "__all__"


class FilterTreeNodeBaseSerializers(serializers.ModelSerializer):
    """
    树形结构节点基础序列化
    """
    class Meta:
        model = FilterTreeNode
        fields = "__all__"

class RectifyBaseSerializers(serializers.ModelSerializer):
    """整改措施基础序列化"""

    class Meta:
        model = RectifyInfo
        fields = '__all__'

class RectifySerializers(serializers.ModelSerializer):
    """整改措施序列化"""
    startTime = serializers.DateTimeField(format='%Y-%m-%d')
    estimateTime = serializers.DateTimeField(format='%Y-%m-%d')
    completeTime = serializers.DateTimeField(format='%Y-%m-%d')
    delayDate = serializers.DateTimeField(format='%Y-%m-%d')
    delayTime = serializers.DateTimeField(format='%Y-%m-%d')
    class Meta:
        model = RectifyInfo
        field = '__all__'
        exclude = ["id"]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for i,j in data.items():
            if not j and j != []:
                data[i] = ""
        # data['iid'] = data.pop('id')
        # if not data['startTime']:
        #     data['startTime'] = ""
        # if not data['estimateTime']:
        #     data['estimateTime'] = ""
        # if not data['completeTime']:
        #     data['completeTime'] = "
        # if not data['delayDate']:
        #     data['delayDate'] = ""
        # if not data['delayTime']:
        #     data['delayTime'] = ""
        return data

class CheckResultBaseSerializers(serializers.ModelSerializer):
    """
    检查结果基础序列化
    """
    checkTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M", required=False, read_only=True)
    approverTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M", required=False, read_only=True)
    checklist = CheckListBaseSerializers()
    rectify = RectifyBaseSerializers()
    
    class Meta:
        model = CheckResult
        fields = "__all__"
class PlaybackBaseSerializers(serializers.ModelSerializer):
    """故障回放基础序列化"""

    class Meta:
        model = PlaybackInfo
        fields = '__all__'

class PlaybackSerializers(serializers.ModelSerializer):
    """故障回放序列化"""
    time = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    class Meta:
        model = PlaybackInfo
        fields = '__all__'

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for i,j in data.items():
            if not j and j != []:
                data[i] = ""
        # data['pid'] = data.pop('id')
        # if not data['content']:
        #     data['content'] = ""
        # if not data['time']:
        #     data['time'] = ""
        return data

class TelemetryProbelmSerializers(serializers.ModelSerializer):
    """天相平台SLA问题序列化"""
    startTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    recoverTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')


    class Meta:
        model = FaultInfo
        field = ["reportType", "faultDescribe", "fid", "Driver", "startTime", "recoverTime"]
        # fields = '__all__'

# class GrafanaAlertsEventSerializers(serializers.ModelSerializer):
#     """组序列化"""
#     startsat = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
#     endsat = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
#     updatedat = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
#
#     class Meta:
#         model = GrafanaAlertsEventInfo
#         fields = '__all__'
#         # fields = (
#         # 'id', 'startsat', 'endsat', 'labels', 'alertstatus', 'autorecover', 'changelogs', 'alerttext', 'eventid',
#         # 'flybookemails', 'flybookgroupids', 'flybookmobiles', 'flybookusers', 'handlestatus', 'marks', 'messageseq',
#         # 'updatedat', 'reason', 'notes', 'reasonHandleUser', 'cmdbDeveloper', 'cmdbMaintainer', 'groupMessageSeq',
#         # 'recoveryPlan', 'source', 'isFirstLaunch')
#
# class GrafanaAlertsEventCurrencySerializers(serializers.ModelSerializer):
#     """组序列化"""
#
#     class Meta:
#         model = GrafanaAlertsEventInfo
#         fields = '__all__'


