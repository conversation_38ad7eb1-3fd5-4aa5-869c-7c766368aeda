"""fault_sys URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path, include
from django.shortcuts import redirect
from . import views
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r'V2/fault', views.FaultViewset)
router.register(r'V2/rectify', views.RectifyViewset)
router.register(r'V2/jarvis', views.JarvisViewset, basename='jarvis')
router.register(r'V2/scheduler', views.SchedulertestViewset, basename='scheduler')
router.register(r'V2/checklist', views.CheckListViewset, basename='checklist')
router.register(r'V2/checkresult', views.CheckResultViewset,basename='checkresult')


urlpatterns = [
    # path('', views.hello),
    path('', include(router.urls)),
    path('V3/mttr/search', views.MttrView.as_view()),
    path('V3/Telemetry/search', views.TelemetryView.as_view()),
    path('V3/Telemetry/create', views.TelemetryCreateView.as_view()),
    path('V3/Telemetry/Update', views.TelemetryUpdateView.as_view())
]

