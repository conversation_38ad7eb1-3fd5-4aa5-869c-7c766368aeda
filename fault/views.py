import json
import time
import requests
from datetime import datetime
from django.db.models import Q

# Create your views here.
from rest_framework.permissions import AllowAny
from .serializers import *
from .models import *
from rest_framework.response import Response
from rest_framework.views import APIView

from framework.models import PersonsInitInfo
from user_auth.models import MyUserInfo
from common.utils.utils import NewViewBase, return_code, get_user
from common.utils.log import Logger
from common.utils.config import itmp_conf
from .funcs import fault_funcs, playback_funcs, rectify_funcs, jarvis_funcs, my_authentication,checklist_funcs,checkresult_funcs

set_log = Logger.get_logger(__name__, 3)


class FaultViewset(NewViewBase):
    """故障信息增删改查"""
    queryset = FaultInfo.objects.all()
    serializer_class = FaultBaseSerializers
    permission_classes = [AllowAny]

    # 自定义查询全故障消息
    @action(methods=['get', 'post'], detail=False, url_path='all')
    def sel_all(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is fault/all")
            fault = FaultInfo.objects.all()
            count = fault.count()
            fault = FaultSerializers(fault, many=True).data
            set_log.info(f"[fault/all SUCCESS]")
            return Response(return_code(20000, data=fault, count=count, interface="fault/sel"))
        except Exception as e:
            set_log.info(f"[fault/all FALSE]")
            set_log.error(f"{e}")
            return Response(return_code(-1, e, interface="fault/sel"))

    # 自定义条件查询
    @action(methods=['get', 'post'], detail=False, url_path='selectFault')
    def selectFault(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api_V2/fault/sel")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/sel"))
            data, total, _ = fault_funcs.my_selectFault(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"异常状态码：{data}")
                set_log.info(f"[fault/sel FALSE]")
                return Response(return_code(data, data=[], status='error', operator=operator, interface="fault/sel"))
            set_log.info(f"[fault/sel SUCCESS]")
            return Response(return_code(20000, data={'list': data, 'total': total}, status='ok'))
        except Exception as e:
            set_log.info(f"[fault/sel FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/sel"))

    # chartline
    @action(methods=['get', 'post'], detail=False, url_path='chartline')
    def chartline_sel(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api_V2/fault/chartline")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/chartline"))
            data = fault_funcs.get_chartline(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"异常状态码：{data}")
                set_log.info(f"[chartline FALSE]")
                return Response(return_code(data, data=[], status='error', operator=operator, interface="fault/chartline"))
            set_log.info(f"[chartline SUCCESS]")
            return Response(return_code(20000, data=data, status='ok'))
        except Exception as e:
            set_log.info(f"[chartline FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=str(e), operator=operator, interface="fault/chartline"))

    # dashboard
    @action(methods=['get', 'post'], detail=False, url_path='dashboard')
    def dashboard_sel(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api_V2/fault/dashboard")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/dashboard"))
            data = fault_funcs.get_dashboard(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"异常状态码：{data}")
                set_log.info(f"[dashboard FALSE]")
                return Response(return_code(data, data=[], status='error', operator=operator, interface="fault/dashboard"))
            set_log.info(f"[dashboard SUCCESS]")
            return Response(return_code(20000, data=data, status='ok'))
        except Exception as e:
            set_log.info(f"[dashboard FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=str(e), operator=operator, interface="fault/dashboard"))
    # 创建FID
    @action(methods=['get', 'post'], detail=False, url_path='createFault')
    def createFault(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/createFault")
            # 权限判断
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/createFault"))
            if my_authentication.createReportAuth(request):
                code, data = fault_funcs.my_createFault(request)
                set_log.debug(f"[createFault code data] {code} {data}")
                if data['reportType'] == '故障':
                    # 飞书多维表格webhook
                    feishu_webhook = "https://q9jvw0u5f5.feishu.cn/base/workflow/webhook/event/Vu19aZI56wGNYDh8TIMcvlbfn6e"
                    webhook_data = {
                        "fault": data['faultDescribe'],
                        "driver": data['driver']
                    }
                    headers = { 
                        "Content-Type": "application/json"
                    }
                    set_log.debug(f"[feishu webhook data] {webhook_data}")
                    response = requests.post(feishu_webhook, data=json.dumps(webhook_data), headers=headers)
                    set_log.debug(f"[feishu webhook response] {response.text} {response.status_code}")
                if code == 20000:
                    set_log.info(f"[CREATE FAULT SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="fault/createFault"))
                else:
                    set_log.info(f"[CREATE FAULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="fault/createFault"))
            else:
                set_log.info(f"[CREATE FAULT FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="fault/createFault"))
        except Exception as e:
            set_log.info(f"[CREATE FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/createFault"))

    # 编辑故障预设树
    @action(methods=['get', 'post'], detail=False, url_path='editFilterTree')
    def editFilterTree(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/editFilterTree")
            # 权限判断
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/editFilterTree"))
            if my_authentication.createReportAuth(request):
                code, data = fault_funcs.editFilterTree(request)
                if code == 20000:
                    set_log.info(f"[editFilterTree SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="fault/editFilterTree"))
                else:
                    set_log.info(f"[editFilterTree FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="fault/editFilterTree"))
            else:
                set_log.info(f"[editFilterTree FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="fault/editFilterTree"))
        except Exception as e:
            set_log.info(f"[editFilterTree FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/editFilterTree"))

    # 查询故障预设树
    @action(methods=['get', 'post'], detail=False, url_path='selectFilterTree')
    def selectFilterTree(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/selectFilterTree")
            # 权限判断
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/selectFilterTree"))
            if my_authentication.createReportAuth(request):
                code, data = fault_funcs.selectFilterTree(request)
                if code == 20000:
                    set_log.info(f"[selectFilterTree SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="fault/selectFilterTree"))
                else:
                    set_log.info(f"[selectFilterTree FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="fault/selectFilterTree"))
            else:
                set_log.info(f"[selectFilterTree FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="fault/selectFilterTree"))
        except Exception as e:
            set_log.info(f"[selectFilterTree FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/selectFilterTree"))

    # 伪删除故障预设树
    @action(methods=['get', 'post'], detail=False, url_path='deleteFilterTree')
    def deleteFilterTree(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/deleteFilterTree")
            # 权限判断
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/deleteFilterTree"))
            if my_authentication.createReportAuth(request):
                code, data = fault_funcs.deleteFilterTree(request)
                if code == 20000:
                    set_log.info(f"[deleteFilterTree SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="fault/deleteFilterTree"))
                else:
                    set_log.info(f"[deleteFilterTree FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="fault/deleteFilterTree"))
            else:
                set_log.info(f"[deleteFilterTree FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="fault/deleteFilterTree"))
        except Exception as e:
            set_log.info(f"[deleteFilterTree FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/deleteFilterTree"))
    # 编辑故障预设树节点
    @action(methods=['get', 'post'], detail=False, url_path='editFilterTreeNode')
    def editFilterTreeNode(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/editFilterTreeNode")
            # 权限判断
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/editFilterTreeNode"))
            if my_authentication.createReportAuth(request):
                code, data = fault_funcs.editFilterTreeNode(request)
                if code == 20000:
                    set_log.info(f"[editFilterTreeNode SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="fault/editFilterTreeNode"))
                else:
                    set_log.info(f"[editFilterTreeNode FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="fault/editFilterTreeNode"))
            else:
                set_log.info(f"[editFilterTreeNode FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="fault/editFilterTreeNode"))
        except Exception as e:
            set_log.info(f"[editFilterTreeNode FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/editFilterTreeNode"))

    # 查询故障预设树节点
    @action(methods=['get', 'post'], detail=False, url_path='selectFilterTreeNode')
    def selectFilterTreeNode(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/selectFilterTreeNode")
            # 权限判断
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/selectFilterTreeNode"))
            if my_authentication.createReportAuth(request):
                code, data = fault_funcs.selectFilterTreeNode(request)
                if code == 20000:
                    set_log.info(f"[selectFilterTreeNode SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="fault/selectFilterTreeNode"))
                else:
                    set_log.info(f"[selectFilterTreeNode FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="fault/selectFilterTreeNode"))
            else:
                set_log.info(f"[selectFilterTreeNode FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="fault/selectFilterTreeNode"))
        except Exception as e:
            set_log.info(f"[selectFilterTreeNode FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/selectFilterTreeNode"))

    # 伪删除故障预设树节点
    @action(methods=['get', 'post'], detail=False, url_path='deleteFilterTreeNode')
    def deleteFilterTreeNode(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/deleteFilterTreeNode")
            # 权限判断
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/deleteFilterTreeNode"))
            if my_authentication.createReportAuth(request):
                code, data = fault_funcs.deleteFilterTreeNode(request)
                if code == 20000:
                    set_log.info(f"[deleteFilterTreeNode SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="fault/deleteFilterTreeNode"))
                else:
                    set_log.info(f"[deleteFilterTreeNode FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="fault/deleteFilterTreeNode"))
            else:
                set_log.info(f"[deleteFilterTreeNode FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="fault/deleteFilterTreeNode"))
        except Exception as e:
            set_log.info(f"[deleteFilterTreeNode FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/deleteFilterTreeNode"))            
    # 上传图片
    @action(methods=['get', 'post'], detail=False, url_path='upload')
    def uploadPhotp(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.info("Here is V2/fault/upload")
            code, data = fault_funcs.my_uploadPhotp(request)
            if code == 20000:
                set_log.info(f"[CREATE FAULT SUCCESS]")
                return Response(return_code(code, data=data, status='ok', interface="fault/upload"))
            else:
                set_log.info(f"[CREATE FAULT FALSE]")
                set_log.error(f"[error] {code} {data}")
                return Response(return_code(code, data=data, operator=operator, interface="fault/upload"))
        except Exception as e:
            set_log.info(f"[CREATE FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error', operator=operator, interface="fault/upload"))

    # 修改故障内容
    @action(methods=['post'], detail=False, url_path='editFault')
    def editFault(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/editFault")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/editFault"))
            # 权限判断
            if my_authentication.editReportAuth(request):
                code, data = fault_funcs.my_editFault(request)
                if code == 20000:
                    set_log.info(f"[EDIT FAULT SUCCESS]")
                    return Response(return_code(20000, data={'list': data}, status='ok'))
                    # return Response(return_code(code, data="编辑成功", status='ok', interface="fault/editFault"))
                else:
                    set_log.info(f"[EDIT FAULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=f"编辑失败, {data}", operator=operator, interface="fault/editFault"))
            else:
                # 无权限
                return Response(return_code(2004, operator=operator, interface="fault/editFault"))
        except Exception as e:
            set_log.info(f"[EDIT FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/editFault"))

    # 伪删除
    @action(methods=['post'], detail=False, url_path='deleteFault')
    def deleteFault(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/del")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/del"))
            # 权限判断
            if my_authentication.delReportAuth(request):
                code, data = fault_funcs.my_deleteFault(request)
                if code == 20000:
                    set_log.info(f"[DEL FAULT SUCCESS]")
                    return Response(return_code(code, data="删除成功", status='ok', interface="fault/del"))
                else:
                    set_log.info(f"[DEL FAULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="删除失败", operator=operator, interface="fault/del"))
            else:
                return Response(return_code(2005, operator=operator, interface="fault/del"))
        except Exception as e:
            set_log.info(f"[DEL FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="fault/del"))

    # 真删除
    @action(methods=['post'], detail=False, url_path='realdelFault')
    def realdelFault(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/realdel")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="fault/realdel"))
            # 权限判断
            if my_authentication.delReportAuth(request):
                code, data = fault_funcs.my_realdeleteFault(request)
                if code == 20000:
                    set_log.info(f"[REALDEL FAULT SUCCESS]")
                    return Response(return_code(code, data="删除成功", status='ok', interface="fault/realdel"))
                else:
                    set_log.info(f"[REALDEL FAULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="删除失败", operator=operator, interface="fault/realdel"))
            else:
                return Response(return_code(2005, operator=operator, interface="fault/realdel"))
        except Exception as e:
            set_log.info(f"[REALDEL FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))

    # SLA-SLO列表
    @action(methods=['get', 'post'], detail=False, url_path='getSLAList')
    def getSLAList(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/getSLAList")
            code, data = fault_funcs.get_SLA_SLO_list(request)
            if code == 20000:
                set_log.info(f"[GET SLAList SUCCESS]")
                return Response(return_code(code, data=data, status='ok', interface="fault/getSLAList"))
            else:
                set_log.info(f"[GET SLAList FALSE]")
                set_log.error(f"[error] {code} {data}")
                return Response(return_code(code, data=data, operator=operator, interface="fault/getSLAList"))
        except Exception as e:
            set_log.info(f"[GET SLAList FALSE]")
            set_log.error(f"{e}")
            return Response(return_code(-1, data=e, interface="fault/getSLAList"))

    # 获取SLO达标率
    @action(methods=['get', 'post'], detail=False, url_path='getSLOscore')
    def getSLOscore(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/getSLOscore")
            code, data = fault_funcs.get_SLO_score(request)
            if code == 20000:
                set_log.info(f"[get_SLO_score SUCCESS]")
                return Response(return_code(code, data=data, status='ok', interface="fault/getSLOscore"))
            else:
                set_log.info(f"[get_SLO_score FALSE]")
                set_log.error(f"[error] {code} {data}")
                return Response(return_code(code, data=data, operator=operator, interface="fault/getSLOscore"))
        except Exception as e:
            set_log.info(f"[get_SLO_score FALSE]")
            set_log.error(f"{e}")
            return Response(return_code(-1, data=e, interface="fault/getSLOscore"))


    # 测试模板
    @action(methods=['get', 'post'], detail=False, url_path='test/test')
    def test(self, request, *args, **kwargs):
        try:
            # fault = FaultInfo.objects.all()
            # for i in fault:
            #     set_log.debug(f"[start] {i.fid}")
            #     i.improveAction = RectifySerializers(RectifyInfo.objects.filter(fid=i.fid), many=True).data
            #     i.playback = PlaybackSerializers(PlaybackInfo.objects.filter(fid=i.fid).order_by("time", "stage"), many=True).data
            #     set_log.debug(f"[one data] \n{i.improveAction} \n{i.playback}")
            #     i.save()

            set_log.info(f"[test SUCCESS]")
            return Response(return_code(20000, data="测试成功"))
        except Exception as e:
            set_log.info(f"[test FALSE]")
            set_log.error(f"{e}")
            return Response(return_code(-1, e, interface="fault/test"))

class RectifyViewset(NewViewBase):
    """整改措施信息增删改查"""
    queryset = RectifyInfo.objects.all()
    serializer_class = RectifyBaseSerializers
    permission_classes = [AllowAny]

    # 自定义查询全整改措施消息
    @action(methods=['get', 'post'], detail=False, url_path='all')
    def sel_all(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is rectify/all")
            rectify = RectifyInfo.objects.all()
            count = rectify.count()
            rectify = RectifySerializers(rectify, many=True).data
            set_log.info(f"[rectify/all SUCCESS]")
            return Response(return_code(20000, data=rectify, count=count, interface="rectify/all"))
        except Exception as e:
            set_log.info(f"[fault/all FALSE]")
            set_log.error(f"{e}")
            return Response(return_code(-1, e, interface="rectify/all"))

    # 自定义条件查询
    @action(methods=['get', 'post'], detail=False, url_path='selectRectify')
    def selectRectify(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api_V2/rectify/sel")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="rectify/sel"))
            data, total = rectify_funcs.my_selectRectify(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"异常状态码：{data}")
                set_log.info(f"[fault/sel FALSE]")
                return Response(return_code(data, data=[], status='error', operator=operator, interface="rectify/sel"))
            set_log.info(f"[fault/sel SUCCESS]")
            return Response(return_code(20000, data={'list': data, 'total': total}, status='ok', interface="rectify/sel"))
        except Exception as e:
            set_log.info(f"[fault/sel FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="rectify/sel"))


    # 创建RID
    @action(methods=['get', 'post'], detail=False, url_path='createRectify')
    def createRectify(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/rectify/createRectify")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="rectify/text"))
            # 权限判断
            if my_authentication.createReportAuth(request):
                code, data = rectify_funcs.my_createRectify(request)
                if code == 20000:
                    set_log.info(f"[CREATE FAULT SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="rectify/text"))
                else:
                    set_log.info(f"[CREATE FAULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="rectify/text"))
            else:
                set_log.info(f"[CREATE FAULT FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="rectify/text"))
        except Exception as e:
            set_log.info(f"[CREATE FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="rectify/text"))


    # 修改故障内容
    @action(methods=['post'], detail=False, url_path='editRectify')
    def editRectify(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/rectify/editRectify")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.editReportAuth(request):
                code, data = rectify_funcs.my_editRectify(request)
                if code == 20000:
                    set_log.info(f"[EDIT RECTIFY SUCCESS]")
                    return Response(return_code(code, data=data, status='ok'))
                else:
                    set_log.info(f"[EDIT RECTIFY FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="编辑失败", operator=operator))
            else:
                # 无权限
                return Response(return_code(2004, operator=operator))
        except Exception as e:
            set_log.info(f"[EDIT FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))

    # 伪删除
    @action(methods=['post'], detail=False, url_path='deleteRectify')
    def deleteRectify(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/del")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.delReportAuth(request):
                code, data = rectify_funcs.my_deleteRectify(request)
                if code == 20000:
                    set_log.info(f"[DEL FAULT SUCCESS]")
                    return Response(return_code(code, data="删除成功", status='ok'))
                else:
                    set_log.info(f"[DEL FAULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="删除失败", operator=operator))
            else:
                return Response(return_code(2005, operator=operator))
        except Exception as e:
            set_log.info(f"[DEL FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))

    # 真删除
    @action(methods=['post'], detail=False, url_path='realdelRectify')
    def realdelRectify(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/fault/realdel")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.delReportAuth(request):
                code, data = rectify_funcs.my_realdeleteRectify(request)
                if code == 20000:
                    set_log.info(f"[REALDEL FAULT SUCCESS]")
                    return Response(return_code(code, data="删除成功", status='ok'))
                else:
                    set_log.info(f"[REALDEL FAULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="删除失败", operator=operator))
            else:
                return Response(return_code(2005, operator=operator))
        except Exception as e:
            set_log.info(f"[REALDEL FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))


    # 测试模板
    @action(methods=['get', 'post'], detail=False, url_path='test/test')
    def test(self, request, *args, **kwargs):
        try:
            set_log.info(f"[test SUCCESS]")
            return Response(return_code(20000, data="测试成功"))
        except Exception as e:
            set_log.info(f"[test FALSE]")
            set_log.error(f"{e}")
            return Response(return_code(-999, e))

class JarvisViewset(NewViewBase):
    """故障信息增删改查"""
    queryset = FaultInfo.objects.all()
    serializer_class = FaultSerializers
    permission_classes = [AllowAny]

    # 创建
    @action(methods=['get', 'post'], detail=False, url_path='createReport')
    def createReport(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is V2/jarvis/createReport")


            code, data = jarvis_funcs.Jarvis_createFault(request)
            if code == 20000:
                set_log.info(f"[Jarvis CREATE FAULT SUCCESS]")
                # 飞书多维表格webhook
                feishu_webhook = "https://q9jvw0u5f5.feishu.cn/base/workflow/webhook/event/Vu19aZI56wGNYDh8TIMcvlbfn6e"
                webhook_data = {
                    "fault": data['faultDescribe'],
                    "driver": data['driver']
                }
                headers = { 
                    "Content-Type": "application/json"
                }
                set_log.debug(f"[feishu webhook data] {webhook_data}")
                response = requests.post(feishu_webhook, data=json.dumps(webhook_data), headers=headers)
                set_log.debug(f"[feishu webhook response] {response.text} {response.status_code}")
                if response.status_code != 200:
                    set_log.error(f"[feishu webhook error] {response.status_code}")
                return Response(return_code(code, data="创建成功", url=f"{itmp_conf['itmp_url']}/fault/detail/{data['fid']}"))
            else:
                set_log.info(f"[Jarvis CREATE FAULT FALSE]")
                set_log.error(f"[error] {code} {data}")
                return Response(return_code(code, data=data, operator='Jarvis'))
        except Exception as e:
            set_log.info(f"[Jarvis CREATE FAULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator='Jarvis'))

class CheckListViewset(NewViewBase):
    """检核项增删改查"""

    # 自定义查询全检核项
    @action(methods=['get', 'post'], detail=False, url_path='all')
    def sel_all(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is CheckList/all")
            checklist = CheckList.objects.all()
            count = checklist.count()
            checklist = CheckListBaseSerializers(checklist, many=True).data
            set_log.info(f"[checklist/all SUCCESS]")
            return Response(return_code(20000, data=checklist, count=count, interface="checklist/all"))
        except Exception as e:
            set_log.info(f"[checklist/all FALSE]")
            set_log.error(f"{e}")
            return Response(return_code(-1, e, interface="checklist/all"))

    # 自定义条件查询
    @action(methods=['get', 'post'], detail=False, url_path='selectChecklist')
    def selectChecklist(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api checklist/sel")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="checklist/sel"))
            data, total = checklist_funcs.SelectChecklist(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"异常状态码：{data}")
                set_log.info(f"[checklist/sel FALSE]")
                return Response(return_code(data, data=[], status='error', operator=operator, interface="checklist/sel"))
            set_log.info(f"[checklist/sel SUCCESS]")
            return Response(return_code(20000, data={'list': data, 'total': total}, status='ok', interface="checklist/sel"))
        except Exception as e:
            set_log.info(f"[checklist/sel FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="checklist/sel"))


    # 创建
    @action(methods=['get', 'post'], detail=False, url_path='createChecklist')
    def createChecklist(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api createChecklist")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="createChecklist/text"))
            # 权限判断
            if my_authentication.createReportAuth(request):
                code, data = checklist_funcs.CreateChecklist(request)
                if code == 20000:
                    set_log.info(f"[CREATE CHECKLIST SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="createChecklist/text"))
                else:
                    set_log.info(f"[CREATE CHECKLIST FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="createChecklist/text"))
            else:
                set_log.info(f"[CREATE CHECKLIST FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="createChecklist/text"))
        except Exception as e:
            set_log.info(f"[CREATE CHECKLIST FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="createChecklist/text"))


    # 修改检核项
    @action(methods=['post'], detail=False, url_path='editChecklist')
    def editChecklist(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api editChecklist")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.editReportAuth(request):
                code, data = checklist_funcs.EditChecklist(request)
                if code == 20000:
                    set_log.info(f"[EDIT CHECKLIST SUCCESS]")
                    return Response(return_code(code, data="编辑成功", status='ok'))
                else:
                    set_log.info(f"[EDIT CHECKLIST FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="编辑失败", operator=operator))
            else:
                # 无权限
                return Response(return_code(2004, operator=operator))
        except Exception as e:
            set_log.info(f"[EDIT CHECKLIST FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))

    # 伪删除
    @action(methods=['post'], detail=False, url_path='deleteChecklist')
    def deleteChecklist(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api deleteChecklist")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.delReportAuth(request):
                code, data = checklist_funcs.DeleteChecklist(request)
                if code == 20000:
                    set_log.info(f"[DEL CHECKLIST SUCCESS]")
                    return Response(return_code(code, data="删除成功", status='ok'))
                else:
                    set_log.info(f"[DEL CHECKLIST FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="删除失败", operator=operator))
            else:
                return Response(return_code(2005, operator=operator))
        except Exception as e:
            set_log.info(f"[DEL CHECKLIST FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))

    # 真删除
    @action(methods=['post'], detail=False, url_path='realdelChecklist')
    def realdelChecklist(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api realdelChecklist")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.delReportAuth(request):
                code, data = checklist_funcs.RealdeleteRectify(request)
                if code == 20000:
                    set_log.info(f"[REALDEL CHECKLIST SUCCESS]")
                    return Response(return_code(code, data="删除成功", status='ok'))
                else:
                    set_log.info(f"[REALDEL CHECKLIST FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="删除失败", operator=operator))
            else:
                return Response(return_code(2005, operator=operator))
        except Exception as e:
            set_log.info(f"[REALDEL CHECKLIST FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))

class CheckResultViewset(NewViewBase):
    """结合结果信息增删改查"""
    permission_classes = [AllowAny]

    # 自定义查询全整改措施消息
    @action(methods=['get', 'post'], detail=False, url_path='all')
    def sel_all(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api CheckResultViewset all")
            checkresult = CheckResult.objects.all()
            count = checkresult.count()
            checkresult = CheckResultBaseSerializers(checkresult, many=True).data
            set_log.info(f"[CheckResult/all SUCCESS]")
            return Response(return_code(20000, data=checkresult, count=count, interface="CheckResult/all"))
        except Exception as e:
            set_log.info(f"[CheckResult/all FALSE]")
            set_log.error(f"{e}")
            return Response(return_code(-1, e, interface="CheckResult/all"))

    # 自定义条件查询
    @action(methods=['get', 'post'], detail=False, url_path='selectCheckresult')
    def selectCheckResult(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api selectCheckresult")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="CheckResult/sel"))
            data, total = checkresult_funcs.SelectCheckresult(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"异常状态码：{data}")
                set_log.info(f"[checksult/sel FALSE]")
                return Response(return_code(data, data=[], status='error', operator=operator, interface="checksult/sel"))
            set_log.info(f"[checksult/sel SUCCESS]")
            return Response(return_code(20000, data={'list': data, 'total': total}, status='ok', interface="checksult/sel"))
        except Exception as e:
            set_log.info(f"[checksult/sel FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="checksult/sel"))


    # 创建RID
    @action(methods=['get', 'post'], detail=False, url_path='createCheckresult')
    def createCheckresult(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api createCheckresult")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code, interface="checksult/text"))
            # 权限判断
            if my_authentication.createReportAuth(request):
                code, data = checkresult_funcs.CreateCheckresult(request)
                if code == 20000:
                    set_log.info(f"[CREATE CHECKRESULT SUCCESS]")
                    return Response(return_code(code, data=data, status='ok', interface="checksult/text"))
                else:
                    set_log.info(f"[CREATE CHECKRESULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data=data, operator=operator, interface="checksult/text"))
            else:
                set_log.info(f"[CREATE CHECKRESULT FALSE]")
                set_log.info(f"[{operator} 用户无权限创建]")
                return Response(return_code(2006, operator=operator, interface="checksult/text"))
        except Exception as e:
            set_log.info(f"[CREATE CHECKRESULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator, interface="checksult/text"))


    # 修改故障内容
    @action(methods=['post'], detail=False, url_path='editCheckresult')
    def editRectify(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api editCheckresult")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.editReportAuth(request):
                code, data = checkresult_funcs.EditCheckresult(request)
                if code == 20000:
                    set_log.info(f"[EDIT CHECKRESULT SUCCESS]")
                    return Response(return_code(code, data="编辑成功", status='ok'))
                else:
                    set_log.info(f"[EDIT CHECKRESULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="编辑失败", operator=operator))
            else:
                # 无权限
                return Response(return_code(2004, operator=operator))
        except Exception as e:
            set_log.info(f"[EDIT CHECKRESULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))

    # 伪删除
    @action(methods=['post'], detail=False, url_path='deleteCheckresult')
    def deleteCheckresult(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api deleteCheckresult")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.delReportAuth(request):
                code, data = checkresult_funcs.DeleteCheckresult(request)
                if code == 20000:
                    set_log.info(f"[DEL CHECKRESULT SUCCESS]")
                    return Response(return_code(code, data="删除成功", status='ok'))
                else:
                    set_log.info(f"[DEL CHECKRESULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="删除失败", operator=operator))
            else:
                return Response(return_code(2005, operator=operator))
        except Exception as e:
            set_log.info(f"[DEL CHECKRESULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))

    # 真删除
    @action(methods=['post'], detail=False, url_path='realdelCheckresult')
    def realdelCheckresult(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api realdelCheckresult")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # 权限判断
            if my_authentication.delReportAuth(request):
                code, data = checkresult_funcs.RealdeleteCheckresult(request)
                if code == 20000:
                    set_log.info(f"[REALDEL CHECKRESULT SUCCESS]")
                    return Response(return_code(code, data="删除成功", status='ok'))
                else:
                    set_log.info(f"[REALDEL CHECKRESULT FALSE]")
                    set_log.error(f"[error] {code} {data}")
                    return Response(return_code(code, data="删除失败", operator=operator))
            else:
                return Response(return_code(2005, operator=operator))
        except Exception as e:
            set_log.info(f"[REALDEL CHECKRESULT FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, operator=operator))


# from django.views.decorators.csrf import csrf_exempt
# @csrf_exempt
class SchedulertestViewset(NewViewBase):
    """计划任务"""
    queryset = FaultInfo.objects.all()
    serializer_class = FaultBaseSerializers
    permission_classes = [AllowAny]


    # 每日 复盘会/整改措施 推送开关
    @action(methods=['get', 'post'], detail=False, url_path='swtich')
    def switch_notice(self, request, *args, **kwargs):
        try:
            data = request.data

            # 机器人关闭定时任务
            if data['statu'] == 'OFF':
                url = "http://139.159.232.199:88/robot/jobs/remind/pause/"
                requests.get(url=url)
            # 机器人开启定时任务
            elif data['statu'] == 'ON':
                url = "http://139.159.232.199:88/robot/jobs/remind/resume/"
                requests.get(url=url)
            else:
                return Response(return_code(1009, data='statu参数错误'))
            return Response(return_code(0, data=data))
        except Exception as e:
            return Response(return_code(-1, data=e))

    # 每日 复盘会/整改措施 数据接口
    @action(methods=['get', 'post'], detail=False, url_path='noticemsg')
    def notice_msg(self, request, *args, **kwargs):
        try:
            # data = request.data
            now = datetime.strptime(datetime.now().strftime('%Y-%m-%d %H:%M'),'%Y-%m-%d %H:%M')
            # 未复盘故障报告信息
            print("~" * 100)
            set_log.debug("here is noticemsg")
            fault_all = FaultSerializers(FaultInfo.objects.filter(currentProgress='未复盘', reportType='故障', stage='已发布'), many=True).data
            fault_count = FaultInfo.objects.all().count()
            set_log.debug(f"[all fault count] {fault_count}")
            fault = []
            noreplaytime_fault = []
            # set_log.debug(f"fault get : {fault_all}, {type(fault_all)}")
            if fault_all != []:
                for i in fault_all:
                    set_log.debug(f"[one fault ] {i}")
                    url = f'{itmp_conf["itmp_url"]}/fault/detail/{i["fid"]}'
                    if i['reportType'] != "故障":
                        url = f'{itmp_conf["itmp_url"]}/problem/detail/{i["fid"]}'
                    one_data = {'title': i['faultDescribe'], 'driver': i['driver'],
                                'url': url}
                    if i['replayTime']:
                        one_data['days_remaining'] = (datetime.strptime(i['replayTime'], '%Y-%m-%d %H:%M') - now).days
                        if one_data['days_remaining'] < 0:
                            one_data['typee'] = '超时'
                            one_data['color'] = 'red'
                            one_data['days_remaining'] = -one_data['days_remaining']
                            one_data['estimateTime'] = i['estimateTime']
                        else:
                            one_data['typee'] = '剩余'
                            one_data['color'] = 'green'
                            one_data['estimateTime'] = i['estimateTime']
                        fault.append(one_data)
                    else:
                        one_data['estimateTime'] = '未知'
                        one_data['days_remaining'] = '未知'
                        one_data['typee'] = '超时'
                        one_data['color'] = 'red'
                        noreplaytime_fault.append(one_data)
                        one_data['replayTime'] = ' '
                        one_data['days_remaining'] = ' '
                        one_data['typee'] = ' '
                        one_data['color'] = 'red'
                        fault.append(one_data)
                    set_log.debug(f"[one_fault_data] {one_data}")
                # set_log.debug(f"estimateTime:{fault[0]['estimateTime']}, {type(fault[0]['estimateTime'])}")
                # set_log.debug(f"fault: {fault}")

            now = datetime.strptime(datetime.now().strftime('%Y-%m-%d'),'%Y-%m-%d')
            rectify_all = RectifySerializers(RectifyInfo.objects.filter(rate='进行中', stage='已发布'), many=True).data
            rectify_count = RectifyInfo.objects.all().count()
            set_log.debug(f"[all rectify count] {rectify_count}")
            rectify = []
            noesttime_rectify = []
            timeout_count = RectifyInfo.objects.filter(rate='已完成', isDelay='已延期', stage='已发布').count()
            if rectify_all != []:
                for i in rectify_all:
                    set_log.debug(f"[one rectify ] {i}")
                    one_data = {'title': i['rectifyDescribe'], 'driver': i['driver'],
                                'url': f'{itmp_conf["itmp_url"]}/improvement/detail/{i["iid"]}'}
                    if i['estimateTime']:
                        one_data['days_remaining'] = (datetime.strptime(i['estimateTime'], '%Y-%m-%d') - now).days
                        if one_data['days_remaining'] < 0:
                            # timeout_count += 1
                            one_data['typee'] = '超时'
                            one_data['color'] = 'red'
                            # one_data['days_remaining'] = -one_data['days_remaining']
                            one_data['estimateTime'] = i['estimateTime']
                        else:
                            one_data['typee'] = '剩余'
                            one_data['color'] = 'green'
                            one_data['estimateTime'] = i['estimateTime']
                        rectify.append(one_data)
                    else:
                        one_data['estimateTime'] = '未知'
                        one_data['days_remaining'] = '未知'
                        one_data['typee'] = ''
                        one_data['color'] = 'red'
                        noesttime_rectify.append(one_data)
                    set_log.debug(f"[one rectify data]: {one_data}")
                # set_log.debug(f"estimateTime:{rectify[0]['estimateTime']}, {type(rectify[0]['estimateTime'])}")
            data = {'fault': fault, 'fault_count': fault_count, 'rectify': rectify, 'rectify_count': rectify_count,
                    'timeout_count': timeout_count, 'noreplaytime_fault': noreplaytime_fault,
                    'noesttime_rectify': noesttime_rectify}
            set_log.debug(f"[return_data] {data}")
            return Response(return_code(0, data=data))
        except Exception as e:
            set_log.error(f"[noticemsg falsed] {e}")
            return Response(return_code(-1, data=str(e)))


    # 检核结果 数据接口
    @action(methods=['get', 'post'], detail=False, url_path='checkresultmsg')
    def checkresult_msg(self, request, *args, **kwargs):
        try:
            set_log.info(f"~"*100)
            set_log.info(f"[START checkresultmsg]")
            # data = request.data
            now = datetime.strptime(datetime.now().strftime('%Y-%m-%d'), '%Y-%m-%d')
            # 
            print("*" * 100)
            set_log.debug("here is checkresult checkresultmsg")
            checkresult_all = CheckResultBaseSerializers(CheckResult.objects.filter(rectify__rate__icontains='已完成', stage='已发布'), many=True).data
            checkresult = []
            if checkresult_all != []:
                for i in checkresult_all:
                    set_log.debug(f"[one checkresult ] {i}")
                    fault_url = f'{itmp_conf["itmp_url"]}/fault/detail/{i["fid"]}'
                    if i['reportType'] != "故障":
                        fault_url = f'{itmp_conf["itmp_url"]}/problem/detail/{i["fid"]}'
                    fault = i['faultDescribe']    
                    rectify = i['rectifyDescribe']
                    rectify_url = f'{itmp_conf["itmp_url"]}/improvement/detail/{i["iid"]}'
                    checklist = i['checklist']['task']
                    approver = i['approver']
                    approverResult = i['approverResult']
                    one_data = {
                        'fault': fault,
                        'fault_url': fault_url,
                        'rectify': rectify,
                        'rectify_url': rectify_url,
                        'checklist': checklist,
                        'approver': approver,
                        'approverResult': approverResult
                    }
                    checkresult.append(one_data)
                    set_log.debug(f"[one checkresult data]: {one_data}")
            data = {'checkresult': checkresult}
            set_log.debug(f"[return_data] {data}")
            return Response(return_code(0, data=data))
        except Exception as e:
            return Response(return_code(-1, data=e))

          
    # 整改措施 数据接口
    @action(methods=['get', 'post'], detail=False, url_path='rectifymsg')
    def rectify_msg(self, request, *args, **kwargs):
        try:
            set_log.info(f"~"*100)
            set_log.info(f"[START rectifymsg]")
            # data = request.data
            now = datetime.strptime(datetime.now().strftime('%Y-%m-%d'), '%Y-%m-%d')
            # 
            print("*" * 100)
            set_log.debug("here is rectify rectifymsg")
            rectify_all = RectifySerializers(RectifyInfo.objects.filter(rate__in=['进行中', '未补充'], stage='已发布'), many=True).data
            rectify_count = RectifyInfo.objects.all().count()
            set_log.debug(f"[all rectify count] {rectify_count}")
            rectify = []
            noesttime_rectify = []
            if rectify_all != []:
                for i in rectify_all:
                    set_log.debug(f"[one rectify ] {i}")
                    fault_url = f'{itmp_conf["itmp_url"]}/fault/detail/{i["fid"]}'
                    if i['reportType'] != "故障":
                        fault_url = f'{itmp_conf["itmp_url"]}/problem/detail/{i["fid"]}'
                    one_data = {
                        'faultDescribe': i['faultDescribe'],
                        'fid': fault_url,
                        'title': i['rectifyDescribe'],
                        'driver': i['driver'] if i['driver'] != "" else "未知",
                        'url': f'{itmp_conf["itmp_url"]}/improvement/detail/{i["iid"]}',
                        'reportType': i['reportType']}
                    if i['estimateTime']:
                        one_data['days_remaining'] = (datetime.strptime(i['estimateTime'], '%Y-%m-%d') - now).days
                        if one_data['days_remaining'] < 0:
                            # timeout_count += 1
                            one_data['typee'] = '超时'
                            one_data['color'] = 'red'
                            # one_data['days_remaining'] = -one_data['days_remaining']
                            one_data['estimateTime'] = i['estimateTime']
                        else:
                            one_data['typee'] = '剩余'
                            one_data['color'] = 'green'
                            one_data['estimateTime'] = i['estimateTime']
                        rectify.append(one_data)
                    else:
                        one_data['estimateTime'] = '未知'
                        one_data['days_remaining'] = '未知'
                        one_data['typee'] = ''
                        one_data['color'] = 'red'
                        noesttime_rectify.append(one_data)
                    # user_email
                    # one_data['user_email'] = UserInfo.objects.get(name=one_data['driver']).email
                    set_log.debug(f"[one rectify data]: {one_data}")
                # set_log.debug(f"estimateTime:{rectify[0]['estimateTime']}, {type(rectify[0]['estimateTime'])}")
                # set_log.debug(f"rectify: {rectify}")
            data = {'rectify': rectify, 'noesttime_rectify': noesttime_rectify}
            set_log.debug(f"[return_data] {data}")
            return Response(return_code(0, data=data))
        except Exception as e:
            return Response(return_code(-1, data=e))

    # 复盘会 数据接口
    @action(methods=['get', 'post'], detail=False, url_path='faultmsg')
    def fault_msg(self, request, *args, **kwargs):
        try:
            set_log.info(f"~"*100)
            set_log.info(f"[START faultmsg]")
            # data = request.data
            now = datetime.strptime(datetime.now().strftime('%Y-%m-%d %H:%M'), '%Y-%m-%d %H:%M')
            # 未复盘故障报告信息
            print("*" * 100)
            set_log.debug("here is faultmsg")
            fault_all = FaultSerializers(FaultInfo.objects.filter(currentProgress='未复盘', stage='已发布', reportType='故障'), many=True).data
            # fault_count = FaultInfo.objects.all().count()
            set_log.debug(f"[未复盘故障] {fault_all}")
            fault = []
            noreplaytime_fault = []
            # set_log.debug(f"fault get : {fault_all}, {type(fault_all)}")
            if fault_all != []:
                for i in fault_all:
                    set_log.debug(f"[one fault fields] {i}")
                    set_log.debug(f"[replayTime] {type(i['replayTime'])}")
                    one_data = {'title': i['faultDescribe'], 'driver': i['driver'],
                                'url': f'{itmp_conf["itmp_url"]}/fault/detail/{i["fid"]}'}
                    if i['replayTime']:
                        one_data['days_remaining'] = (datetime.strptime(i['replayTime'], '%Y-%m-%d %H:%M') - now).days
                        if one_data['days_remaining'] < 0:
                            one_data['typee'] = '超时'
                            one_data['color'] = 'red'
                            one_data['replayTime'] = i['replayTime']
                            one_data['days_remaining'] = -one_data['days_remaining']
                        else:
                            one_data['typee'] = '剩余'
                            one_data['color'] = 'green'
                            one_data['replayTime'] = i['replayTime']
                        fault.append(one_data)
                    else:
                        one_data['replayTime'] = '未补充'
                        one_data['days_remaining'] = '未补充'
                        one_data['typee'] = '未补充'
                        one_data['color'] = 'red'
                        noreplaytime_fault.append(one_data)
                        one_data['replayTime'] = ' '
                        one_data['days_remaining'] = ' '
                        one_data['typee'] = ' '
                        one_data['color'] = 'red'
                        fault.append(one_data)
                    set_log.debug(f"[one_fault_data] {one_data}")
                # set_log.debug(f"estimateTime:{fault[0]['estimateTime']}, {type(fault[0]['estimateTime'])}")
                # set_log.debug(f"fault: {fault}")

            data = {'fault': fault, 'noreplaytime_fault': noreplaytime_fault}
            set_log.debug(f"[return_data] {data}")
            return Response(return_code(0, data=data))
        except Exception as e:
            return Response(return_code(-1, data=e))

    # 获取用户邮箱
    @action(methods=['get', 'post'], detail=False, url_path='email')
    def get_email(self, request, *args, **kwargs):
        try:
            data = dict(request.data)['user_list']
            set_log.debug(f"[data] {data} {type(data)}")
            email_list = []
            for i in data:
                email_dict = {'user_name': i}
                set_log.debug(f"[user_name] {i}")
                user = PersonsInitInfo.objects.filter(user_name=i)
                set_log.debug(f"[user] {user} {len(user)}")
                if len(user) == 0:
                    email_dict['user_email'] = ""
                    email_dict['user_id'] = ""
                    email_list.append(email_dict)
                    continue
                set_log.debug(f"[user_email] {user[0].email} [user_id] {user[0].user_id}")
                email_dict['user_email'] = user[0].email
                email_dict['user_id'] = user[0].user_id
                email_list.append(email_dict)
                set_log.debug(f"[email_dict] {email_dict}")
            return Response(return_code(0, data=email_list))
        except Exception as e:
            return Response(return_code(-999, data=e))

    # 延期整改措施数据
    @action(methods=['get', 'post'], detail=False, url_path='probelmRectifyDelay')
    def probelmRectifyDelay(self, request, *args, **kwargs):
        try:
            set_log.info(f"~"*100)
            set_log.info(f"[START PROBLEM RECTIFY DELAY JUDGE]")
            # data = request.data
            rectify = RectifyInfo.objects.filter(stage="已发布", rate__in=["进行中", "未补充"])
            set_log.info(f"[rectify count] {rectify.count()}")
            for i in rectify:
                try:
                    set_log.debug(f"[对比是否延期]")
                    now = datetime.now()
                    set_log.debug(f"[当天日期] {now}")
                    completeTime = i.completeTime if i.completeTime else now
                    set_log.debug(f"[完成时间] {i.completeTime}")
                    estimateTime = i.estimateTime
                    set_log.debug(f"[预计完成时间] {i.estimateTime}")


                    if not estimateTime:
                        set_log.info(f"[无法判断是否延期] {i.pk} {i.rectifyDescribe}")
                        continue
                    else:
                        if completeTime <= estimateTime:
                            set_log.info(f"[未延期] {i.pk} {i.rectifyDescribe}")
                            i.isDelay = "未延期"
                            i.save()
                        else:
                            set_log.info(f"[已延期] {i.pk} {i.rectifyDescribe}")
                            i.isDelay = "已延期"
                            i.save()
                except Exception as e:
                    set_log.error(f"[probelmRectifyDelay FALSE] {e}")
                    set_log.info(f"[probelmRectifyDelay FALSE] {i.pk} {i.rectifyDescribe}")
                    continue
            set_log.info(f"[probelmRectifyDelay SUCCESS]")
            return Response(return_code(0, data="probelmRectifyDelay SUCCESS"))
        except Exception as e:
            return Response(return_code(-999, data=e))

    # 测试
    @action(methods=['get', 'post'], detail=False, url_path='test')
    def test(self, request, *args, **kwargs):
        try:
            data = request.data
            return Response(return_code(0, data=data))
        except Exception as e:
            return Response(return_code(-999, data=e))

# DI平台获取
class MttrView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            set_log.info('*'*100)
            set_log.debug('----- GET -----')
            set_log.debug(f"{request.headers}")
            request_ip = request.headers["X-Forwarded-For"].split(",")[0]
            set_log.debug(f"[request_ip] {request_ip}")
            set_log.debug(f"[white_ip_list] {itmp_conf['DI_white_ip_list']}")
            set_log.debug(f"[hostname] {request.headers['hostname']}")
            if request.headers['hostname'] not in itmp_conf['DI_white_ip_list']:
                return Response({"detail": "身份认证信息未提供。"})
            year = request.GET.get('year', default=False)
            month = request.GET.get('month', default=False)
            month = str(int(month))
            if year & month:
                # 缺少参数
                return Response(return_code(1002))
            isOperation = request.GET.get('isOperation', default='非运营类')
            set_log.debug(f"[isOperation] {isOperation}")
            date = f"{year}年{month}月"
            set_log.debug(f"[front data] year: {year}  month: {month} date: {date}")
            # isOperation
            search = {'faultMonth': date, 'stage': '已发布', 'isOperation': isOperation}
            data = self.get_data(search)
            if data == 0:
                return Response(return_code(0, data={'total': 0, 'count_p2': 0, 'MTTR': 0}))
            return Response(return_code(0, data=data))
        except Exception as e:
            set_log.error(f'{e}')
            return Response(return_code(-999, data=e))

    def post(self, request):
        try:
            set_log.info('*'*100)
            set_log.debug('----- POST -----')
            set_log.debug(f"{request.headers}")
            request_ip = request.headers["X-Forwarded-For"].split(",")[0]
            set_log.debug(f"[request_ip] {request_ip}")
            set_log.debug(f"[white_ip_list] {itmp_conf['DI_white_ip_list']}")
            set_log.debug(f"[hostname] {request.headers['hostname']}")
            if request.headers['hostname'] not in itmp_conf['DI_white_ip_list']:
                return Response({"detail": "身份认证信息未提供。"})
            data = request.data
            set_log.debug(f"[front data] {type(data)} {data}")
            year = data.get('year', False)
            month = data.get('month', False)
            set_log.debug(f"[year] {year} [month] {month}")
            if not year or not month:
                # 缺少参数
                return Response(return_code(1002))
            month = str(int(month))
            date = f"{year}年{month}月"
            set_log.debug(f"[date] {date}")
            search = {'faultMonth': date, 'stage': '已发布'}
            isOperation = data.get('isOperation', False)
            set_log.debug(f"[isOperation] {isOperation}")
            if isOperation:
                search['isOperation'] = isOperation
            isEffectOutside = data.get('isEffectOutside', False)
            set_log.debug(f"[isEffectOutside] {isEffectOutside}")
            if isEffectOutside:
                search['isEffectOutside'] = isEffectOutside
            set_log.debug(f"[search] {search}")
            data = self.get_data(search)
            if data == 0:
                return Response(return_code(0, data={'total': 0, 'count_p2': 0, 'MTTR': 0}))
            return Response(return_code(0, data=data))
        except Exception as e:
            set_log.error(f'{e}')
            return Response(return_code(-999, data=e))

    def get_data(self, search):
        try:
            fault = FaultInfo.objects.filter(**search).values('duration', 'level', 'isOperation', 'fid', 'isEffectOutside')
            set_log.debug(f"[fault] {type(fault)} {fault}")
            total = len(fault)
            set_log.debug(f"[total] {total}")
            if total == 0:
                return 0
            duration_all = 0
            count_p2 = 0
            for i in fault:
                set_log.debug(f"[one_data] {type(i)} {i}")
                # set_log.debug(f"[duration] {type(i['duration'])} {i['duration']}")
                duration_all += i["duration"]
                if i["level"] == 'P1' or i["level"] == 'P2':
                    count_p2 += 1
            mttr = round(duration_all / total, 1)
            data = {'total': total, 'count_p2': count_p2, 'MTTR': mttr}
            return data
        except Exception as e:
            return e

# 天相查询接口
class TelemetryView(APIView):
    permission_classes = [AllowAny]


    def post(self, request):
        try:
            set_log.info('*'*100)
            set_log.info('----- TelemetryView -----')

            set_log.debug('----- POST -----')
            set_log.debug(f"{request.headers}")
            request_ip = request.headers["X-Forwarded-For"].split(",")[0]
            set_log.debug(f"[request_ip] {request_ip}")
            set_log.debug(f"[white_token_list] {itmp_conf['Telemetry_white_token_list']}")
            set_log.debug(f"[token] {request.headers['token']}")
            if request.headers['token'] not in itmp_conf['Telemetry_white_token_list']:
                set_log.debug(f"[token] 身份认证信息未提供。")
                return Response({"detail": "身份认证信息未提供。"})
            data = request.data
            set_log.debug(f"[front data] {type(data)} {data}")
            # if data.get('page', False) and data.get('size', False):
            query = data.get('query', False)
            if query:
                fault = self.get_data(query)
            else:
                fault = FaultInfo.objects.all()
            if isinstance(fault, int):
                set_log.error(f"[error coed] {fault}")
                return Response(return_code(fault))
            page = int(data.get('page', 1))
            size = int(data.get('size', 10))
            totalSize = len(fault)
            if totalSize == 1 and size == 1:
                totalPage = 1
            else:
                totalPage = totalSize//size + 1
            fault = FaultSerializers(fault[page*size-size:page*size], many=True).data
            set_log.debug(f"[fault] {type(fault)} {fault}")
            for i in fault:
                rectify = RectifySerializers(RectifyInfo.objects.filter(fid=i['fid']), many=True).data
                set_log.debug(f"[one rectify] {type(rectify)} {rectify}")
                i['improveAction'] = rectify
            set_log.debug(f"return data {type(fault)} {fault}")
            return Response(return_code(0, data=fault, totalSize=totalSize, totalPage=totalPage))
        except Exception as e:
            set_log.error(f'{e}')
            return Response(return_code(-999, data=e))

    # def get_data(self, search):
    def get_data(self, parameter):
        set_log.info("-" * 40)
        set_log.debug("here is select")
        set_log.debug(f"查询参数：{parameter}")
        try:
            search = dict()
            # fid搜索
            if parameter.get('fid', False):
                search['fid__in'] = parameter['fid']
            # 故障状态 已发布/未发布
            if parameter.get('stage', False):
                search['stage__in'] = parameter['stage']  
            # 年-月
            if parameter.get('date', False):
                search['faultMonth__in'] = parameter['date']
            year = parameter.get('year', False)
            month = parameter.get('month', False)
            if year or month:
                set_log.debug(f"[year] {type(year)} {year}")
                set_log.debug(f"[month] {type(month)} {month}")
                date = []
                if year and month:
                    for i in year:
                        for j in month:
                            date.append(f"{i}年{j}月")
                    search['faultMonth__in'] = date
                elif not year:
                    date = [f"{j}年{i}月" for i in month for j in ['2021', '2022', '2023']]
                    search['faultMonth__in'] = date
                elif not month:
                    date = [f"{i}年" for i in year]
                    search['faultYear__in'] = date

                set_log.debug(f"[date] {date}")

            # 故障等级
            if parameter.get('level', False):
                search['level__in'] = parameter['level']
            # 根因
            if parameter.get('cause', False):
                search['causeClassify__in'] = parameter['cause']
            # 当前进度
            if parameter.get('current', False):
                search['currentProgress__in'] = parameter['current']
            # driver
            if parameter.get('driver', False):
                search['driver__in'] = parameter['driver']
            # 是否影响对外业务
            if parameter.get('isEffectOutside', False):
                search['isEffectOutside__in'] = parameter['isEffectOutside']
            # 对营收的影响
            if parameter.get('directEffectRevenue', False):
                search['directEffectRevenue__in'] = parameter['directEffectRevenue']
            # 根因定位角色
            # 参数为[]空时，可查询为空的数据
            if parameter.get('causeLocator', False):
                search['causeLocator__in'] = parameter['causeLocator']
            # 是否首发
            if parameter.get('isFirstLaunch', False):
                search['isFirstLaunch__in'] = parameter['isFirstLaunch']
            # 是否有监控
            if parameter.get('hasWatch', False):
                search['hasWatch__in'] = parameter['hasWatch']
            # 是否有告警
            if parameter.get('hasAlert', False):
                search['hasAlert__in'] = parameter['hasAlert']
            # 变更/非变更
            if parameter.get('isChange', False):
                search['isChange__in'] = parameter['isChange']
            # 基础设施
            if parameter.get('infrastructure', False):
                search['infrastructure__in'] = parameter['infrastructure']
            # 影响产品
            if parameter.get('isEffectProduce', False):
                temp = '|'.join(parameter['isEffectProduce'])
                search['isEffectProduce__iregex'] = temp
                temp = parameter['isEffectProduce']
                # search['isEffectProduce__icontains'] = f"'{temp[0]}'"
                # # ["TT"] -> 'TT, xx'
                # for i in temp[1:]:
                #     search['isEffectProduce__icontains'] += f", '{i}'"
                set_log.debug(f"[影响的应用] : {search['isEffectProduce__iregex']}")
            # 对营收的影响  非经济损失/直接/间接
            if parameter.get('directEffectRevenue', False):
                search['directEffectRevenue__in'] = parameter['directEffectRevenue']
            # 是否运营类
            if parameter.get('isOperation', False):
                search['isOperation__in'] = parameter['isOperation']
            # 恢复预案
            if parameter.get('recoveryPlan', False):
                search['recoveryPlan__in'] = parameter['recoveryPlan']
            # creator
            # if parameter['creator']:
            #     search['creator__in'] = parameter['creator']
            # creatorID
            if parameter.get('creatorID', False):
                search['creatorID__in'] = parameter['creatorID']
            # reportType 类型
            if parameter.get('reportType', False):
                # search['reportType'] = parameter['reportType']
                search['reportType__in'] = parameter['reportType']
            # 模糊搜索
            if parameter.get('search', False):
                search['faultDescribe__icontains'] = parameter['search']
                # search_data = serializers.serialize('json', FaultInfo.objects.filter(Q(faultDescribe__icontains=search) | Q(causeDescribe__icontains=search)))
            set_log.debug(f"查询参数：{search}")

            # 无参数
            if len(search) == 0:
                fault = FaultInfo.objects.all()
                return fault
            order = parameter.get('order', '-')
            order = '-' if order == 'desc' or order == '-' else ''
            sortKey = parameter.get('sortKey', 'fid')
            fault = FaultInfo.objects.filter(**search).order_by(f'{order}{sortKey}')

            # 错误查询结果查看
            if len(fault) == 0:
                set_log.error(f"查询结果为空：{fault}")
                return 1001
            return fault
        except Exception as e:
            set_log.error(f"error : {e}")
            return -1

# 天相SLA创建故障报告接口
class TelemetryCreateView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        driver = {"liuzhimin": "刘智敏", "dongfuxin": "董富欣", "huangyuxiong": "黄誉雄", "lichaowen":"黎超文", "zhengjiayu":"郑佳裕", "yangxiangyuan":"杨相远", "lujunjie1":"卢俊杰", "chenzheng": "陈正","yaoweizhong": "姚伟忠"}
        try:
            set_log.info('*'*100)
            set_log.info('----- TelemetryCreateView -----')
            set_log.debug('----- POST -----')
            # 认证
            set_log.debug(f"{request.headers}")
            request_ip = request.headers["X-Forwarded-For"].split(",")[0]
            set_log.debug(f"[request_ip] {request_ip}")
            set_log.debug(f"[white_token_list] {itmp_conf['Telemetry_white_token_list']}")
            set_log.debug(f"[token] {request.headers['token']}")
            if request.headers['token'] not in itmp_conf['Telemetry_white_token_list']:
                return Response({"detail": "身份认证信息未提供。"})
            
            # 获取参数
            frontend_data = request.data
            set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
            # 生成新fid
            last_fid = FaultInfo.objects.all().order_by('-id')[0].fid
            set_log.debug(f"[last FID] {type(last_fid)} {last_fid}")
            today = time.strftime('%Y%m%d', time.localtime(time.time()))
            set_log.debug(f"[日期比较]  {last_fid[:8]} {today}")
            if last_fid[:8] == today:
                set_log.debug(f"SAME DAY")
                fid = str(int(last_fid) + 1)
            else:
                set_log.debug(f"[NEW DAY]")
                fid = f"{today}0001"
            set_log.debug(f"[NEW FID] {fid}")
            frontend_data['fid'] = fid

            # 天相平台参数
            frontend_data['creator'] = '天相'
            frontend_data['creatorID'] = "TP0001"
            frontend_data['stage'] = '已发布'
            frontend_data['currentProgress'] = '未归档'
            frontend_data['reportType'] = frontend_data.get('reportType', 'SLA问题')
            frontend_data['driver'] = driver.get(frontend_data['driver'], frontend_data['driver'])


            # data = {"fid": fid, 'creator': '天相平台', 'creatorID': "TP0001", 'stage': '未发布'}
            set_log.info(f"[录入数据]  {frontend_data}")
            try:
                new_fault = FaultInfo(**frontend_data)
                new_fault.save()
            except Exception as e:
                set_log.error(f"[CREATE FALSE] {e}")
                return Response(return_code(1004, data=e))
            else:
                set_log.info(f"[CREATE SUCCESS] {new_fault}")
            # 创建成功后发送卡片给driver
            return Response(return_code(0, data=frontend_data))
        except Exception as e:
            set_log.error(f"[CREATE FALSE] {e}")
            return Response(return_code(-1, data=e))

# 天相更新接口
class TelemetryUpdateView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            set_log.info('*'*100)
            set_log.info('----- TelemetryUpdateView -----')

            set_log.debug('----- POST -----')
            set_log.debug(f"{request.headers}")
            request_ip = request.headers["X-Forwarded-For"].split(",")[0]
            set_log.debug(f"[request_ip] {request_ip}")
            set_log.debug(f"[white_token_list] {itmp_conf['Telemetry_white_token_list']}")
            set_log.debug(f"[token] {request.headers['token']}")
            if request.headers['token'] not in itmp_conf['Telemetry_white_token_list']:
                return Response({"detail": "身份认证信息未提供。"})
            frontend_data = request.data
            set_log.debug(f"[UPDATE front data] {frontend_data}")
            if frontend_data.get('fid', False):
                fid = frontend_data.pop('fid')
            else:
                return Response(return_code(1004, data='无fid'))
            # frontend_data['driver'] = frontend_data.pop('Driver')
            res = FaultInfo.objects.filter(fid=fid).update(**frontend_data)
            set_log.debug(f"[UPDATE res] {res}")
            set_log.info(f"[Update SUCCESS]")
            # data = TelemetryProbelmSerializers(FaultInfo.objects.get(fid=fid)).data
            return Response(return_code(0, data="更新成功"))
        except Exception as e:
            set_log.error(f"[UPDATE FALSE] {e}")
            return Response(return_code(-1, data=e))

