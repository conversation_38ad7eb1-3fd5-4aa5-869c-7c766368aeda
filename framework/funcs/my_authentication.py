import json
import time

import jwt
from rest_framework import authentication
from rest_framework import exceptions
from rest_framework.response import Response

from common.utils.utils import return_code
from common.utils.log import Logger

from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers

from rest_framework_jwt.utils import jwt_decode_handler


set_log = Logger.get_logger(__name__, 0)

class MyTokenAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        set_log.info(f"+"*100)
        set_log.info(f"[MyTokenAuthentication] 开始Token验证")
        # token = request.META.get('X-Auth-Token')
        # set_log.debug(f"[token] {token}")
        try:
            operator = request.data.get('data', {'operator': False}).get('operator', False)
            set_log.debug(f"[frontend data] {request.data}")
            if operator:
                return ({"user_name": "jarvis"}, None)
            token = request.headers.get("Authorization")
            if token[6] == ' ':
                token = token[7:]
            set_log.debug(f"[token] {token}")
            if not token:
                set_log.info(f"[无token信息]")
                raise exceptions.NotAuthenticated

            username = jwt_decode_handler(token)
            set_log.debug(f"[username] {username}")
            now = int(time.time())
            if username['exp'] - now <= 0:
                # raise exceptions.AuthenticationFailed(return_code(2003))
                raise jwt.ExpiredSignature
            user = MyUserInfo.objects.filter(email=username['email'], not_send="False")[0]
            # user = User.objects.get(pk=jwt_decode_handler(token)['user_id'])
            set_log.debug(f"[user] {user}")
            if user.is_active:
                return (user, None)
            else:
                raise exceptions.AuthenticationFailed(return_code(2004))
        except jwt.ExpiredSignature:
            set_log.info('token 过期')
            raise exceptions.AuthenticationFailed(return_code(401, data='token 过期', msg='token 过期', status='error'))

            # return Response(return_code(401, data='token 过期', msg='token 过期', status='error'))
        except jwt.DecodeError:
            set_log.info(f"签名错误")
            raise exceptions.AuthenticationFailed(return_code(2004))
        except jwt.InvalidTokenError:
            set_log.info(f"无效的token")
            raise exceptions.AuthenticationFailed(return_code(2004))
        except exceptions.NotAuthenticated:
            set_log.info(f"EMPTY TOKEN")
            raise exceptions.NotAuthenticated(return_code(2003))
        except Exception as e:
            set_log.debug(f"[认证模块异常] {e}")
            set_log.info(f"无效的token")

            raise exceptions.AuthenticationFailed(return_code(2004))

        # return (user, None)

# def MyAuthenticationFailed(APIException):
#     # status_code = status.HTTP_401_UNAUTHORIZED
#     default_detail = _('Incorrect authentication credentials.')
#     default_code = 'authentication_failed'

def MyTokenAuthentication_by_hand(request):
    set_log.info(f"+"*100)
    set_log.info(f"[MyTokenAuthentication] 开始Token验证")
    # token = request.META.get('X-Auth-Token')
    # set_log.debug(f"[token] {token}")
    try:
        operator = request.data.get('data', {'operator': False}).get('operator', False)
        set_log.debug(f"[frontend data] {request.data}")
        if operator:
            return ({"user_name": "jarvis"}, 20000)
        token = request.headers.get("Authorization")
        if token[6] == ' ':
            token = token[7:]
        set_log.debug(f"[token] {token}")
        if not token:
            set_log.info(f"[无token信息]")
            # 用户未登录
            return ("", 2002)

        username = jwt_decode_handler(token)
        set_log.debug(f"[username] {username}")
        now = int(time.time())
        if username['exp'] - now <= 0:
            return ("", 2003)
        user = MyUserInfo.objects.filter(email=username['email'], not_send="False")[0]
        set_log.debug(f"[user] {user}")
        if user.is_active:
            return (user, 20000)
        else:
            # token无效
            return 2004
    except jwt.ExpiredSignature:
        set_log.info('token 过期')
        return ("", 401)
        # return Response(return_code(401, data='token 过期', msg='token 过期', status='error'))
    except jwt.DecodeError:
        set_log.info(f"签名错误")
        return ("", 2004)
    except jwt.InvalidTokenError:
        set_log.info(f"无效的token")
        return ("", 2004)
    except exceptions.NotAuthenticated:
        set_log.info(f"EMPTY TOKEN")
        return ("", 2003)
    except Exception as e:
        set_log.debug(f"[认证模块异常] {e}")
        set_log.info(f"异常的token")

        return ("", 2004)

def get_user(request):
    try:
        token = request.headers.get("Authorization")
        if token[6] == ' ':
            token = token[7:]
        set_log.debug(f"[frontend token] {token}")
        try:
            # username = data.get('username')
            username = jwt_decode_handler(token)
            set_log.debug(f"[username] {username}")
            now = int(time.time())
            if username['exp'] - 60*60*24 - now <= 30:
                # raise exceptions.AuthenticationFailed(return_code(2003))
                raise jwt.ExpiredSignature
        except jwt.ExpiredSignatureError as e:
            # ExpiredSignatureError token过期
            set_log.info(f"{e}")
            return return_code(401, data="token过期", msg='请求成功', status='ok')

        user = MyUserInfoSerializers(MyUserInfo.objects.filter(email=username['email'])[0], not_send="False").data
        set_log.info(f"[user] {user}")

        return user
    except Exception as e:
        set_log.error(f'获取用户信息失败 {e}')
        return {'user_name': '未知', 'TT_id': '未知'}