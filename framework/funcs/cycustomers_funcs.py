import copy
import json
import time

import requests
from django.db.models import Q
from rest_framework_jwt.utils import jwt_decode_handler


from ..models import *
from ..serializers import *
from common.utils.log import Logger
from common.utils.config import itmp_conf

from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers

# 异步请求
import asyncio
import threading
import aiohttp

set_log = Logger.get_logger(__name__, 0)
ITMP_URL = itmp_conf["itmp_url"]
ITMP_ROBOT_URL = itmp_conf["itmp_robot_url"]

# sel
def my_selectTicket(request):
    try:
        set_log.info("-" * 40)
        set_log.info("Here is my_selectTicket")
        # 多选参数
        json_field = ['businessOwner', 'devOwner', 'attachments', 'appVersion', 'mobileType']
        one_field = ['ticketID', 'problemDescription', 'userTtid', 'stage', 'apps', 'osType']
        # time_field = []
        ticketType = 'allTicket'
        query = {'ticketType': 'allTicket'}
        set_log.debug(f"[request method] {request.method}")
        page = 1
        limit = 10
        if request.method == 'GET':
            set_log.debug(f"[request method] --- GET ---")
            set_log.debug(f"frontend data: {request.GET}")
            page = int(request.GET.get('page', 1))
            limit = int(request.GET.get('limit', 10))
            ticketType = request.GET.get('ticketType', 'allTicket')
            query = request.GET.get('query', {'ticketType': 'allTicket'})

        elif request.method == 'POST':
            set_log.debug(f"[request method] --- POST ---")
            frontend_data = request.data['data']
            set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")

            page = int(frontend_data.get('page', 1))
            limit = int(frontend_data.get('limit', 10))


            query = frontend_data.get('query', {'ticketType': 'allTicket'})
            # set_log.debug(f"query:{query} {type(query)}")
            if 'ticketType' in query.keys():
                ticketType = query.pop('ticketType')

        # 分页参数
        set_log.debug(f"分页参数：page - {page}, limit - {limit}")
        set_log.debug(f"[GET ticketType] {ticketType}")

        # 获取当前用户
        user = get_user(request)



        search = dict()
        search_by_hand = Q()
        search_json_icontains = []

        # 查询全部工单
        if ticketType == 'allTicket':
            set_log.info(f"[ticketType] allTicket")
            pass
        # 查询我创建的工单
        elif ticketType == 'myCreateTicket':
            set_log.info(f"[ticketType] myCreateTicket")

            
            # 大客户组临时配置 后续优化
            # 超人组
            # if user['user_name'] in ["黄国臻", "钟成东"]:
            #     search['feedbackPerson__in'] = ["黄国臻", "钟成东"]
            # , "钟成东", "李婵", "杨译丞" // 人员变动除去
            # 库奇组
            if user['user_name'] in ["薛宏晶", "黄国臻", "陈海志"]:
                search['feedbackPerson__in'] = ["薛宏晶", "黄国臻", "陈海志"]
            # 咸蛋组
            elif user['user_name'] in ["宋玥", "黄宾宾", "吴果鸿"]:
                search['feedbackPerson__in'] = ["宋玥", "黄宾宾", "吴果鸿"]
            # 测试组
            elif user['user_name'] in ["文磊", "江梓帆", "admin"]:
                search['feedbackPerson__in'] = ["文磊", "江梓帆", "admin"]
            else:
                search['feedbackPerson'] = user['user_name']
        # 查询我待办的工单
        elif ticketType == 'myTodoTicket':
            set_log.info(f"[ticketType] myTodoTicket")
            # 大客户组临时配置 后续优化
            # 超人组
            # if user['user_name'] in ["黄国臻", "钟成东"]:
            #     search_json_icontains.append(Q())
            #     for k in ["黄国臻", "钟成东"]:
            #         search_json_icontains[-1].connector = 'OR'
            #         search_json_icontains[-1].children.append((f"owner__icontains", k))
            # 库奇组
            if user['user_name'] in ["薛宏晶", "黄国臻", "陈海志"]:
                search_json_icontains.append(Q())
                for k in ["薛宏晶", "黄国臻", "陈海志"]:
                    search_json_icontains[-1].connector = 'OR'
                    search_json_icontains[-1].children.append((f"owner__icontains", k))
            # 咸蛋组
            elif user['user_name'] in ["宋玥", "黄宾宾", "吴果鸿"]:
                search_json_icontains.append(Q())
                for k in ["宋玥", "黄宾宾", "吴果鸿"]:
                    search_json_icontains[-1].connector = 'OR'
                    search_json_icontains[-1].children.append((f"owner__icontains", k))
            # 测试组
            elif user['user_name'] in ["文磊", "江梓帆", "admin"]:
                search_json_icontains.append(Q())
                for k in ["文磊", "江梓帆", "admin"]:
                    search_json_icontains[-1].connector = 'OR'
                    search_json_icontains[-1].children.append((f"owner__icontains", k))
            else:
                search['owner__icontains'] = user['user_name']
            search['stage__in'] = ['待处理', '处理中', '已处理']
        # 我的已办
        elif ticketType == 'myFinishTicket':
            set_log.info(f"[ticketType] myFinishTicket")
            pass
        else:
            set_log.info(f"[ticketType] selTicket")

        for i, j in query.items():
            set_log.debug(f"[one search] {i} {j} {type(j)}")
            # 精确搜索
            # if
            # search[f'{i}__in'] = j
            if j == []:
                pass
            elif j == '':
                pass
            elif j == None:
                pass
            else:
                set_log.info(f"[Valid parameters] {i} {j} {type(j)}")
                # 模糊搜索
                if i == 'search':
                    if j == '':
                        continue
                    # Q(id__icontains=j) |
                    search_by_hand = Q(stage__icontains=j) | Q(owner__icontains=j) | \
                                     Q(businessOwner__icontains=j) | Q(devOwner__icontains=j) | Q(clientOwner__icontains=j) | \
                                     Q(problemDescription__icontains=j) | Q(userTtid__icontains=j) | Q(attachments__icontains=j) | \
                                     Q(apps__icontains=j) | Q(osType__icontains=j) | Q(mobileType__icontains=j) | \
                                     Q(feedbackPerson__icontains=j) | Q(severityLevel__icontains=j) | \
                                     Q(reason__icontains=j) | Q(respondent__icontains=j) | Q(devProcessor__icontains=j) | \
                                     Q(cause__icontains=j) | Q(changeLogs__icontains=j)


                # json 格式
                # if i in json_field:
                #     search_by_hand.connector = 'OR'
                #     for k in j:
                #         search_by_hand.children.append((f"{i}__icontains", k))
                # 多选模糊匹配参数
                elif i in json_field:
                    # search_by_hand.connector = 'AND'
                    search_json_icontains.append(Q())
                    for k in j:
                        # search_by_hand.connector = 'OR'
                        # search_by_hand.children.append((f"{i}__icontains", k))
                        search_json_icontains[-1].connector = 'OR'
                        search_json_icontains[-1].children.append((f"{i}__icontains", k))
                # 单选
                elif i in one_field:
                    search[f'{i}__icontains'] = j
                # 按功能分类
                elif i == 'functionType':
                    # search_by_hand.connector = 'AND'
                    # for k in j:
                    #     search_by_hand.children.append((f"{i}__icontains", k))
                    if len(j) == 1:
                        search['firstLevelCategory'] = j[0]
                    elif len(j) == 2:
                        search['firstLevelCategory'] = j[0]
                        search['secondLevelCategory'] = j[1]
                    else:
                        search['firstLevelCategory'] = j[0]
                        search['secondLevelCategory'] = j[1]
                        search['thirdLevelCategory'] = j[2]
                elif i == 'enterTime' and j:
                    # [i for i in j]
                    search['enterTime__range'] = j
                else:
                    search[f'{i}__in'] = j
        set_log.debug(f"查询参数：{search} {search_by_hand} {len(search_by_hand)} {search_json_icontains} {len(search_json_icontains)}")

        func_start_time = time.time()

        # 无参数
        # if len(search) == 0 and len(search_by_hand) == 0:
        #     # set_log.debug(f"all")
        #     # func_start_time = time.time()
        #     data = CYCustomersFeedbackInfo.objects.all().order_by('-id')
        #     total = data.count()
        #     data = CustomersFeedbackSerializers(data[page*limit-limit:page*limit], many=True).data

        # elif len(search) > 0 and len(search_by_hand) == 0:
        #     set_log.debug(f"[search] {search}")
        #     data = CYCustomersFeedbackInfo.objects.filter(**search).order_by('-id')
        #     # set_log.debug(f"[search data] {data}")
        #     total = data.count()
        #     data = CustomersFeedbackSerializers(data[page*limit-limit:page*limit], many=True).data
        #     # set_log.debug(f"[search data json] {data}")
        # else:
        #     # search_by_hand.children.append(filter_Q)
        #     set_log.debug(f"[search_by_hand] {search_by_hand}")
        #     data = CYCustomersFeedbackInfo.objects.filter(**search).filter(search_by_hand).order_by('-id')
        #     # set_log.debug(f"[search_by_hand data] {data}")
        #     total = data.count()
        #     data = CustomersFeedbackSerializers(data[page*limit-limit:page*limit], many=True).data
            # set_log.debug(f"[search_by_hand data json] {data}")
        # total = len(alerts)
        # 优化
        data = CYCustomersFeedbackInfo.objects.all()
        if len(search) != 0:
            data = data.filter(**search)
        if len(search_by_hand) != 0:
            data = data.filter(search_by_hand)
        if len(search_json_icontains) != 0:
            for i in search_json_icontains:
                data = data.filter(i)
        data = data.order_by('-enterTime')
        total = data.count()
        set_log.debug(f"[search data] {data}")
        data = CYCustomersFeedbackSerializers(data[page*limit-limit:page*limit], many=True).data
        set_log.debug(f"[search data json len] {len(data)}")



        set_log.info(f"[sel data] SUCCESS")
        func_end_time = time.time()
        set_log.info(f"[查询 耗时] {func_end_time - func_start_time:.2f} seconds")
        # 时间格式转换
        # data = timeserializers(data)
        set_log.debug(f"分页后数据长度：{len(data)}")

        # 查询为空
        if data == []:
            set_log.error(f"查询结果为空：{data}")
            # return data, count, total
        return data, total
    except Exception as e:
        set_log.error(f"error : {e}")
        return -1, 0

# dashboard = get_dashboard(json.loads(fault[0]), json.loads(rectify), mttf_times_count(fault[-1]))
# set_log.info(f"[dashboard] {dashboard}")


def get_dashboard(request):
    dashboard = {"test": "test"}
    return dashboard

async def async_send_report(url, post_data, headers):
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url, data=post_data, headers=headers) as response:
                response_text = await response.text()
                if response.status == 200:
                    set_log.debug(f"[SEND SUCCESS] {response}")
                    set_log.debug(f"[RESPONSE DATA] {response_text}")
                else:
                    set_log.debug(f"[SEND FALSE] {response}")
                    set_log.debug(f"[RESPONSE DATA] {response_text}")
        except Exception as e:
            set_log.debug(f"[创建工单同步卡片异常] {e}")

def my_createTicket(request):
    try:
        set_log.info("-" * 40)
        set_log.info("Here is my_createTicket")
        # cookie = get_cookie(request)
        # creator = UserInfo.objects.filter(userid=cookie['UserID'])[0].name
        # set_log.info(f"creator: {creator}")


        data = request.data['data']
        set_log.debug(f"[frontend data] {data}")


        functionType = data.get('functionType', [])
        set_log.info(f"[问题类型]  {functionType}")
        count = len(functionType)
        if count == 3:
            data['firstLevelCategory'] = functionType[0]
            data['secondLevelCategory'] = functionType[1]
            data['thirdLevelCategory'] = functionType[2]
            function = f"{functionType[0]} - {functionType[1]} - {functionType[2]}"
        elif count == 2:
        # else:
            data['firstLevelCategory'] = functionType[0]
            data['secondLevelCategory'] = functionType[1]
            data['thirdLevelCategory'] = ""
            function = f"{functionType[0]} - {functionType[1]}"
        # elif count == 1:
        #     data['firstLevelCategory'] = functionType[0]
        #     onwers = ResponsiblePersonsSerializers(ResponsiblePersonsInfo.objects.filter(firstLevelCategory=data['firstLevelCategory'])[0]).data
        else:
            return 1009, "选择功能类别不能为空"

        try:
            # 获取相关负责人
            url = "https://yw-cmdb.ttyuyin.com/api/jsonrpc/"
            headers = {"Authorization": "NOLHTYVeHEdM3fIA4MLjzll2s1FWvdbsFfhNKGuGKUSzZYIWckSIdxV3NQ8fyKUc"}
            req_data = {
                "id": 0,
                "jsonrpc": "2.0",
                "method": "ui_get_functional_correspondence",
                "params": {
                    "function_l1": data['firstLevelCategory'],
                    "function_l2": data['secondLevelCategory'],
                    "function_l3": data['thirdLevelCategory'],
                    "page": 1,
                    "limit": 20
                }
            }
            onwers = requests.post(url=url, json=req_data, headers=headers).json()['result']['items'][0]
        except Exception as e:
            set_log.error(f"[get functional_correspondence FALSE] {e}")
            return 10031, "获取功能异常"


        # 根据功能绑定处理人和当前负责人
        # 一级分类 --> 群组
        # 具体分类 --> 运/研/客 负责人
        set_log.info(f"[onwers info] {onwers}")
        data["feishuGroup"] = onwers['feishu_group']
        data["feishuGroupId"] = onwers['feishu_group_id']
        if data["feishuGroupId"] == '':
            data["feishuGroupId"] = 'https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=d32kb2b2-c427-4848-8f2a-1f1b3c058141'
            set_log.warning(f"群组id为空,触发兜底")
        data['businessOwner'] = onwers['business_label_ops_users']
        data['devOwner'] = onwers['develop_users']
        data['clientIOSOwner'] = onwers['ios_person_in_charges']
        data['clientAndroidOwner'] = onwers['android_person_in_charges']
        data['owner'] =[]
        if '客户端' in data['endType']:
            # ["Android", "iOS"]
            if 'Android' in data['osType']:
                data['owner'] +=  data['clientAndroidOwner']
            if 'iOS' in data['osType']:
                data['owner'] +=  data['clientIOSOwner']
        if '服务端' in data['endType']:
            data['owner'] +=  data['devOwner']
        data['owner'] += data['businessOwner']       
        # + data['clientIOSOwner'] + data['clientAndroidOwner']


        # 获取反馈人
        feedbackPerson = get_user(request)
        data['feedbackPerson'] = feedbackPerson['user_name']
        # 获取反馈人open_id
        try:
            if feedbackPerson['user_id'] == "not found":
                registrant = feedbackPerson['user_name']
            else:
                registrant = feedbackPerson['user_id']
        except Exception as e:
            set_log.warning(f"用户不存在,触发兜底")
            registrant = 'ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2'

        data_return = copy.deepcopy(data)

        # 添加创建日志
        changeLogs = [{
            "type": "create",
            "time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "operator": feedbackPerson['user_name'],
            "content": f"创建了工单"
        }]
        data['changeLogs'] = changeLogs



        # 录入时间
        enterTime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        data['enterTime'] = enterTime
        # data['isUploadLogs'] = isUploadLogs
        # 判断是否工作时间录入
        # hasWorkingHours = ''

        # # 紧急程度
        # severityLevel = data.get('severityLevel', '非紧急')

        # json格式转换

        set_log.info(f"[录入数据] {data}")
        ticketinfo = CYCustomersFeedbackInfo(**data, stage="待处理")
        ticketinfo.save()

        # 创建后根据id 创建 ticketID
        ticketID = str(time.time())[:10]
        pk = ticketinfo.pk
        set_log.debug(f"[new pk] {pk}")
        ticketID = ticketID + str(pk).rjust(6, '0')
        set_log.debug(f"[new ticketID] {ticketID}")

        ticketinfo.ticketID = ticketID
        ticketinfo.save()

        set_log.debug(f"[录入格式] {ticketinfo} {ticketinfo.pk}")

        set_log.info(f"[CREATE SUCCESS] 录入成功")

        # 紧急程度
        # severityLevel = data.get('severityLevel', '非紧急')



        # set_log.info(f"[CREATE] SUCCESS")
        # 创建授予相关权限权限

            # url = ITMP_URL + "/circulation/api/send_report/"
        url = ITMP_ROBOT_URL + "/circulation/api/send_report/"
        # url = "https://127.0.0.1:8765/circulation/api/send_report/"
        set_log.debug(f"[itmp-jarvis-url] {url}")
        fault_time = ticketinfo.startTime
        set_log.debug(f"[fault_time] {fault_time}")
        if fault_time == None or fault_time == 'null':
            fault_time = ''
        photo_url = [i['url'] for i in data.get('attachments', [])]
        developer_id_list = []
        for i in data['devOwner']:
            devOwner = MyUserInfo.objects.filter(user_name=i, not_send="False")
            # devOwner = PersonsInitInfo.objects.filter(user_name=i)
            set_log.debug(f"[devOwner] {devOwner} {devOwner.count()}")
            if devOwner.count() != 0:
                developer_id_list.append(devOwner[0].user_id)
        # [i.get('open_id', 'ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2') for i in data['businessOwner']]
        operator_id_list = []

        for i in data['businessOwner']:
            businessOwner = MyUserInfo.objects.filter(user_name=i, not_send="False")
            # businessOwner = PersonsInitInfo.objects.filter(user_name=i)
            if businessOwner.count() != 0:
                operator_id_list.append(businessOwner[0].user_id)
        onwers_id_list = []
        for i in data['owner']:
            owner = MyUserInfo.objects.filter(user_name=i, not_send="False")
            if owner.count() != 0:
                onwers_id_list.append(owner[0].user_id)       
        # 工单链接
        app_type = data.get('apps', [])
        if '唱鸭' in app_type: 
            ticket_url = itmp_conf['itmp_url'] + "/feedback/cy/ticket-detail/" + str(ticketID)
        else:    
            ticket_url = itmp_conf['itmp_url'] + "/feedback/ticket-detail/" + str(ticketID)
        post_data = {
            "job_id": ticketID,
            "describe": data.get('problemDescription', ''),
            "severityLevel": data.get('severityLevel', '单例问题'),
            "tt_id": data.get('userTtid', []),
            # 反馈时间
            "create_time": enterTime,
            # 出现时间
            "fault_time": [fault_time],
            "device": data.get('osType', []),
            "app_type": data.get('apps', []),
            "app_version": data.get('appVersion', []),
            "function": function,
            "owner": onwers_id_list,
            # 登记人open_id
            "registrant": registrant,
            "url": ticket_url,
            # "photo_url": ["https://www.baidu.com"],
            "photo_url": photo_url,
            # Jarvis测试群
            # "chat_id": "oc_7adc3624a795ead71e2528ee01bedb32",
            # ITMP 测试群
            # "chat_id": "oc_f52d5bfd4b7f11b144ab2b995f45a419",
            "feishu_group": onwers['feishu_group'],
            "feishu_group_id": onwers['feishu_group_id'],
            # "developer_id_list": ["ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2"],
            "developer_id_list": developer_id_list,
            # "operator_id_list": ["ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2"]
            "operator_id_list": operator_id_list,
            "stage": "待处理",
            "changeLogs": ticketinfo.changeLogs,
            "isUploadLogs": data.get('isUploadLogs', '未反馈')
        }
        headers = {
            'Content-Type': 'application/json'
        }
        post_data = json.dumps(post_data)
        # set_log.debug(f"[POST URL] {url}")
        set_log.debug(f"[CARD DATA] {post_data}")

        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(async_send_report(url, post_data, headers))
            loop.close()
        # 在新线程中执行异步任务
        thread = threading.Thread(target=run_async_task)
        thread.start()
        return 20000, data_return
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'


def my_uploadPhotp(request):
    from obs import ObsClient, HeadPermission  # 用于连接华为云OBS的库
    set_log.info("-" * 50)
    set_log.debug("Here is my_uploadPhotp")
    # cookie = myget_cookie(request)
    # operator = get_user(request)
    if not request.FILES.get('file', False):
        return 1010, '图片上传失败,未获取到上传图片'
    img = request.FILES.get('file').read()
    set_log.debug(f"frontend files {request.FILES}")
    data = request.data
    set_log.debug(f"[frontend] {request.data}")
    obsClient = ObsClient(
        # access_key_id='PTIF140E2NIGOIXBXWMN',
        # secret_access_key='1i05SIkeIBb7Ms44xulQwC2Du8YryV2IGm9CZKws',
        # server='obs.cn-north-4.myhuaweicloud.com'
        access_key_id=itmp_conf['access_key_id'],  # 你的华为云的ak码
        secret_access_key=itmp_conf['secret_access_key'],  # 你的华为云的sk
        server=itmp_conf['server']  # 你的桶的地址
    )
    # bucketname = 'obs-test-hw-gz-yw-fmp-backend'
    bucketname = itmp_conf['bucketname']
    set_log.debug(f"bucket: {itmp_conf['bucketname'], itmp_conf['server']}")

    # uid = request.FILES.get('file')
    # set_log.debug(f"uid: {uid}")
    # 同名会覆盖
    title = request.FILES.get('file').name
    set_log.debug(f"[img name] {title}")
    uid = data.get('uid', 'error_id')

    # img = base64.b64decode(img)
    set_log.debug(f"img: {type(img)},uid: {uid}")
    uid = uid + "." + title.split(".")[-1]
    # 上传图片
    # if title[-3:] == "mp4":
    #     set_log.info(f"[上传视频中]")
    #     res = obsClient.putFile(bucketname, uid, img)
    # else:
    #     set_log.info(f"[上传图片中]")
    res = obsClient.putContent(bucketname, uid, content=img)


    set_log.debug(f"obs res: {res}")

    if res.status < 300:
        url = res.body.objectUrl
        # 开权限
        obsClient.setObjectAcl(bucketname, uid, aclControl=HeadPermission.PUBLIC_READ)

        return_data = {
            "uid": uid,
            "url": url,
            "name": title,
            "type": title.split(".")[-1]
        }
        set_log.info(f"[UPLOAD SUCCESS] {return_data}")
        return 20000, return_data
    else:
        set_log.error(f"errorCode: {res.errorCode}")
        set_log.error(f"errorMessage: {res.errorMessage}")
        # return Response(return_code(-99, data='调试中', msg='调试中', operator=operator))
        # return Response(return_code(-2, data='图片上传失败', msg='图片上传失败', operator=operator))
        # 触发告警
        # return 1010, data='图片上传失败', msg='图片上传失败', operator=operator)
        return 1010, "图片上传失败"

# 客服编辑
def my_edit(request):
    try:
        set_log.info("-" * 50)
        set_log.info("Here is my_edit")
        # cookie = get_cookie(request)
        # operator = UserInfo.objects.filter(userid=cookie['UserID'])[0].name
        # set_log.info(f"operator: {operator}")


        data = request.data['data']
        set_log.debug(f"[frontend data] {data}")
        operator = get_user(request)
        # data['feedbackPerson'] = operator['user_name']

        data_return = copy.deepcopy(data)
        ticketID = data.pop('ticketID')
        set_log.debug(f"[TICKET ID] {ticketID}")

        ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]


        # 修改的内容
        ticketinfo.problemDescription = data.get('problemDescription', ticketinfo.problemDescription)
        ticketinfo.userTtid = data.get('userTtid', ticketinfo.userTtid)
        # 附件
        ticketinfo.attachments = data.get('attachments', ticketinfo.attachments)
        ticketinfo.isUploadLogs = data.get('isUploadLogs', ticketinfo.isUploadLogs)
        ticketinfo.apps = data.get('apps', ticketinfo.apps)
        ticketinfo.appVersion = data.get('appVersion', ticketinfo.appVersion)
        ticketinfo.osType = data.get('osType', ticketinfo.osType)
        ticketinfo.mobileType = data.get('mobileType', ticketinfo.mobileType)
        ticketinfo.startTime = data.get('startTime', ticketinfo.startTime)
        ticketinfo.result = data.get('result', ticketinfo.result)
        ticketinfo.cause = data.get('cause', ticketinfo.cause)
        ticketinfo.hasClientRelease = data.get('hasClientRelease', ticketinfo.hasClientRelease)
        # 紧急程度修改 
        ticketinfo.severityLevel = data.get('severityLevel', ticketinfo.severityLevel)

        functionType = data.get('functionType', ticketinfo.functionType)
        # set_log.debug(f"[functionType] {functionType} {type(functionType)}")
        count = len(functionType)
        if count == 3:
            ticketinfo.firstLevelCategory = functionType[0]
            ticketinfo.secondLevelCategory = functionType[1]
            ticketinfo.thirdLevelCategory = functionType[2]
            function = f"{functionType[0]} - {functionType[1]} - {functionType[2]}"
            firstLevelCategory = functionType[0]
            secondLevelCategory = functionType[1]
            thirdLevelCategory = functionType[2]

        elif count == 2:
            ticketinfo.firstLevelCategory = functionType[0]
            ticketinfo.secondLevelCategory = functionType[1]
            function = f"{functionType[0]} - {functionType[1]}"
            firstLevelCategory = functionType[0]
            secondLevelCategory = functionType[1]
            thirdLevelCategory = ""

        elif count == 1:
            ticketinfo.firstLevelCategory = functionType[0]
            function = f"{functionType[0]}"
            firstLevelCategory = functionType[0]
            secondLevelCategory = ""
            thirdLevelCategory = ""
        else:
            return 1009, "选择功能类别不能为空"
        ticketinfo.functionType = functionType
        ticketinfo.feishuGroup = data.get('feishuGroup', ticketinfo.feishuGroup)
        ticketinfo.feishuGroupId = data.get('feishuGroupId', ticketinfo.feishuGroupId)
        # ticketinfo.reason = data['reason']
        # 获取相关负责人
        url = "https://yw-cmdb.ttyuyin.com/api/jsonrpc/"
        headers = {"Authorization": "NOLHTYVeHEdM3fIA4MLjzll2s1FWvdbsFfhNKGuGKUSzZYIWckSIdxV3NQ8fyKUc"}
        req_data = {
            "id": 0,
            "jsonrpc": "2.0",
            "method": "ui_get_functional_correspondence",
            "params": {
                "function_l1": firstLevelCategory,
                "function_l2": secondLevelCategory,
                "function_l3": thirdLevelCategory,
                "page": 1,
                "limit": 20
            }
        }
        onwers = requests.post(url=url, json=req_data, headers=headers).json()['result']['items'][0]

        changeLogs = {
            "type": "update",
            "time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "operator": operator['user_name'],
            "content": "修改了工单"
        }
        ticketinfo.changeLogs.append(changeLogs)
        ticketinfo.save()
        data = CYCustomersFeedbackSerializers(ticketinfo).data
        # 工单链接
        ticket_url = itmp_conf['itmp_url'] + "/feedback/ticket-detail/" + str(ticketID)

            # 同步卡片
            # url = ITMP_URL + "/circulation/api/update_card/"
        url = ITMP_ROBOT_URL + "/circulation/api/update_card/"
        # url = "http://127.0.0.1:8765/circulation/api/update_card/"
        fault_time = data['startTime']
        if operator['user_id'] == "not found":
            registrant = operator['user_name']
        else:
            registrant = operator['user_id']
        set_log.debug(f"[fault_time] {fault_time}")
        if fault_time == None or fault_time == 'null':
            fault_time = ''
        photo_url = [i['url'] for i in data.get('attachments', [])]
        # if photo_url != []:
        #     photo_url = [i['url'] for i in photo_url]
        post_data = {
            "job_id": ticketID,
            # 操作人id
            "operate_id": registrant,
            "describe": data.get('problemDescription', ''),
            "severityLevel": data.get('severityLevel', '单例问题'),
            "tt_id": data.get('userTtid', []),
            # 反馈时间
            "create_time": ticketinfo.enterTime.strftime('%Y-%m-%d %H:%M:%S'),
            # 出现时间
            "fault_time": [fault_time],
            "device": data.get('osType', []),
            "app_type": data.get('apps', []),
            "app_version": data.get('appVersion', []),
            "function": function,
            # 对应功能群
            "feishu_group": data['feishuGroup'],
            "feishu_group_id": data['feishuGroupId'],
            # 登记人open_id
            # "registrant": registrant,
            "url": ticket_url,
            # "photo_url": ["https://www.baidu.com"],
            "photo_url": photo_url,
            "stage": ticketinfo.stage,
            "changeLogs": ticketinfo.changeLogs,
            "isUploadLogs": ticketinfo.isUploadLogs
        }

        headers = {
            'Content-Type': 'application/json'
        }
        # set_log.debug(f"[feedback_time type] {type(post_data['feedback_time'])} {post_data['feedback_time']}")
        # set_log.debug(f"[fault_time type] {[type(i) for i in post_data['fault_time']]} {post_data['fault_time']}")

        post_data = json.dumps(post_data)
        # set_log.debug(f"[POST URL] {url}")
        set_log.debug(f"[CARD DATA] {post_data}")
        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(async_send_report(url, post_data, headers))
            loop.close()
        # 在新线程中执行异步任务
        thread = threading.Thread(target=run_async_task)
        thread.start()
        # 创建授予相关权限权限
        return 20000, data_return
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'

# 认领
def my_handleTicket(request):
    try:
        set_log.info("-" * 50)
        set_log.info("Here is my_handleTicket")
        # cookie = get_cookie(request)
        # operator = UserInfo.objects.filter(userid=cookie['UserID'])[0].name
        # set_log.info(f"operator: {operator}")


        data = request.data['data']
        apps = data.get('apps', [])
        set_log.debug(f"[frontend data] {data}")
        data
        if 'stage' in data.keys():
            data.pop('stage')
        # 获取认领人信息
        # if 'respondent' in data.keys():
        #     operator = data['respondent']
        #     set_log.debug(f"[respondent type] {type(operator)} {operator}")
        #     if operator == None:
        #         operator = get_user(request)
        #     elif isinstance(operator, str):
        #         set_log.debug(f"[operation] {MyUserInfo.objects.filter(user_name=operator)}")
        #         set_log.debug(f"[operation] {MyUserInfoSerializers(MyUserInfo.objects.filter(user_name=operator)[0]).data}")
        #         operator = MyUserInfoSerializers(MyUserInfo.objects.filter(user_name=operator)[0]).data
        # else:
        operator = get_user(request)
        set_log.debug(f"[认领人] {operator['user_name']} {operator}")


        data_return = copy.deepcopy(data)
        ticketID = data.pop('ticketID')
        set_log.debug(f"[TICKET ID] {ticketID}")
        ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]
        set_log.debug(f"[ticket_info] {ticketinfo}")
        ticketinfo.stage = "处理中"
        if not ticketinfo.respondent:
            ticketinfo.respondent = operator['user_name']
        # owner = MyUserInfo.objects.filter(user_name=data.get('respondent', False))[0]
        ticketinfo.owner = [operator['user_name']]
        ticketinfo.responseTime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))

        changeLogs = {
            "type": "claim",
            "time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "operator": operator['user_name'],
            "content": "认领了工单"
        }
        ticketinfo.changeLogs.append(changeLogs)
        ticketinfo.save()


        # 同步卡片
        # url = ITMP_URL + "/circulation/api/syn_card/"
        url = ITMP_ROBOT_URL + "/circulation/api/syn_card/"
        apps = data.get('apps', [])
        # url = "http://127.0.0.1:8765/circulation/api/syn_card/"
        post_data = {
            "data": {
                "job_id": ticketID,
                # 处理人id
                "open_id": operator['user_id'],
                "apps" : apps,
                # // 指派：appoint， 抑制：restrain， 认领：claim， 研发确认已解决：resolved， 登记人确认已解决：confirm_resolve, 登记人驳回：not_resolve
                "action": "claim",
                "option": "4"
            }
        }

        headers = {
            'Content-Type': 'application/json'
        }
        post_data = json.dumps(post_data)
        # set_log.debug(f"[POST URL] {url}")
        set_log.debug(f"[CARD DATA] {post_data}")
        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(async_send_report(url, post_data, headers))
            loop.close()
        # 在新线程中执行异步任务
        thread = threading.Thread(target=run_async_task)
        thread.start()
        return 20000, data_return
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'


# 指派
def my_assignTicket(request):
    try:
        set_log.info("-" * 50)
        set_log.info("Here is my_assignTicket")
        # cookie = get_cookie(request)
        # operator = UserInfo.objects.filter(userid=cookie['UserID'])[0].name
        # set_log.info(f"operator: {operator}")


        data = request.data['data']
        apps = data.get('apps', [])
        set_log.debug(f"[frontend data] {data}")
        if 'stage' in data.keys():
            data.pop('stage')
        # 获取指派人信息
        assignor = get_user(request)
        assignee = data['assignee']['option']

        assignor_id = assignor['user_id']
        assignor = assignor['user_name']

        # 获取被指派人id
        assignee_id = assignee['user_id']
        assignee = assignee['user_name']


        data_return = copy.deepcopy(data)
        ticketID = data.pop('ticketID')
        set_log.debug(f"[TICKET ID] {ticketID}")

        ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]
        if ticketinfo.stage == "待处理":
            ticketinfo.responseTime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
            ticketinfo.respondent = assignor

        ticketinfo.owner = [assignee]
        changeLogs = {
            "type": "appoint",
            "time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "assignor": assignor,
            "assignee": assignee,
            "content": "指派了工单"
        }

        ticketinfo.changeLogs.append(changeLogs)
        ticketinfo.stage = "处理中"
        ticketinfo.save()

        # 同步卡片
        # url = ITMP_URL + "/circulation/api/syn_card/"
        url = ITMP_ROBOT_URL + "/circulation/api/syn_card/"
        # url = "http://127.0.0.1:8765/circulation/api/syn_card/"
        post_data = {
            "data": {
                "job_id": ticketID,
                # 处理人id
                "open_id": assignor_id,
                "apps" : apps,
                # // 指派：appoint， 抑制：restrain， 认领：claim， 研发确认已解决：resolved， 登记人确认已解决：confirm_resolve, 登记人驳回：not_resolve
                "action": "appoint",
                "option": assignee_id
            }
        }

        headers = {
            'Content-Type': 'application/json'
        }
        post_data = json.dumps(post_data)
        # set_log.debug(f"[POST URL] {url}")
        set_log.debug(f"[CARD DATA] {post_data}")
        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(async_send_report(url, post_data, headers))
            loop.close()
        # 在新线程中执行异步任务
        thread = threading.Thread(target=run_async_task)
        thread.start()
        set_log.info(f"[AssignTicket]  SUCCESS")

        return 20000, data_return
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'

# 抑制工单
# def my_restrainTicket(request):
#     set_log.info("-" * 50)
#     # cookie = get_cookie(request)
#     set_log.info("Here is my_restrainTicket")
#     data = request.data['data']
#     set_log.debug(f"[frontend data] {data}")
#     operator = get_user(request)
#     ticketID = data['ticketID']
#     set_log.debug(f"get ticketID : {ticketID}")
#     # 抑制告警
#     ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]
#     set_log.debug(f"[ticket_info] {ticketinfo}")
#     hours = data['start_time'] - data['end_time']
#     # 同步卡片
#     url = "http://127.0.0.1:8765/circulation/api/recall/"
#     post_data = {
#         "job_id": ticketID,
#         "open_id": operator['user_id'],
#         "action": "restrain",
#         "option": hours
#     }
#     headers = {
#         'Content-Type': 'application/json'
#     }
#     post_data = json.dumps(post_data)
#     # set_log.debug(f"[POST URL] {url}")
#     set_log.debug(f"[CARD DATA] {post_data}")
#     req = requests.post(url=url, data=post_data, headers=headers)
#     # req = requests.post(url=url, data=post_data, headers=headers)
#     if req.status_code == 200:
#         set_log.debug(f"[SEND SUCCESS] {req} {req.json()}")
#     else:
#         set_log.debug(f"[SEND FALSE] {req} {req.json()}")
#
#     changeLogs = {
#         "type": "restrain",
#         "time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
#         "operator": operator['user_name'],
#         "start_time": data['start_time'],
#         "end_time": data['end_time'],
#         "content": "抑制了工单"
#     }
#     set_log.info(f"{changeLogs}")
#
#     ticketinfo.changeLogs = changeLogs
#     ticketinfo.save()
#     set_log.info(f" 抑制工单id:{ticketID} 成功")
#     return 20000, '删除成功'

# 处理完成工单
def my_completeTicket(request):
    try:
        set_log.info("-" * 50)
        set_log.info("Here is my_completeTicket")
        # cookie = get_cookie(request)
        # operator = UserInfo.objects.filter(userid=cookie['UserID'])[0].name
        # set_log.info(f"operator: {operator}")

        time_now = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        data = request.data['data']
        apps = data.get('apps', [])
        set_log.debug(f"[frontend data] {data}")
        if 'stage' in data.keys():
            data.pop('stage')
        # 获取操作人信息
        operator = get_user(request)
        set_log.debug(f"[处理人] {operator['user_name']} {operator}")
        # data['feedbackPerson'] = operator['user_name']

        data_return = copy.deepcopy(data)
        ticketID = data.pop('ticketID')
        set_log.debug(f"[TICKET ID] {ticketID}")

        # if len(str(pk)) < 3:
        #     ticketinfo = CYCustomersFeedbackInfo.objects.filter(pk=pk)[0]
        # else:
        ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]
        set_log.debug(f"[ticket_info] {ticketinfo}")

        ticketinfo.stage = "已处理"
        ticketinfo.devProcessor = operator['user_name']
        ticketinfo.owner = [ticketinfo.feedbackPerson]
        ticketinfo.endTime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        ticketinfo.result = data.get('result', ticketinfo.result)
        ticketinfo.cause = data.get('cause', ticketinfo.cause)
        ticketinfo.hasClientRelease = data.get('hasClientRelease', ticketinfo.hasClientRelease)

        changeLogs = {
            "type": "resolved",
            "time": time_now,
            "operator": operator['user_name'],
            "content": "处理完成工单"
        }
        ticketinfo.changeLogs.append(changeLogs)
        ticketinfo.save()

        # 同步卡片
        # url = ITMP_URL + "/circulation/api/syn_card/"
        url = ITMP_ROBOT_URL + "/circulation/api/syn_card/"
        # url = "http://127.0.0.1:8765/circulation/api/syn_card/"
        post_data = {
            "data": {
                "job_id": ticketID,
                # 处理人id
                "open_id": operator['user_id'],
                "apps" : apps,
                # // 指派：appoint， 抑制：restrain， 认领：claim， 研发确认已解决：resolved， 登记人确认已解决：confirm_resolve, 登记人驳回：not_resolve
                # // 处理完成时间
                # "solve_time": "2023-10-14 16:15:00",
                "action": "resolved"
            }
        }

        headers = {
            'Content-Type': 'application/json'
        }
        post_data = json.dumps(post_data)
        set_log.debug(f"[CARD DATA] {post_data}")
        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(async_send_report(url, post_data, headers))
            loop.close()
        # 在新线程中执行异步任务
        thread = threading.Thread(target=run_async_task)
        thread.start()
        set_log.info(f"[CompleteTicket]  SUCCESS")
        # 创建授予相关权限权限
        return 20000, data_return
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'

# 驳回工单
def my_rejectTicket(request):
    # elif option == 'not_resolve':

    try:
        set_log.info("-" * 50)
        set_log.info("Here is my_rejectTicket")
        # cookie = get_cookie(request)
        # operator = UserInfo.objects.filter(userid=cookie['UserID'])[0].name
        # set_log.info(f"operator: {operator}")

        time_now = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        data = request.data['data']
        apps = data.get('apps', [])
        set_log.debug(f"[frontend data] {data}")
        data_return = copy.deepcopy(data)
        ticketID = data.pop('ticketID')
        set_log.debug(f"[TICKET ID] {ticketID}")

        ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]
        set_log.debug(f"[ticket_info] {ticketinfo}")

        if 'stage' in data.keys():
            data.pop('stage')

        operator = get_user(request)
        set_log.debug(f"[操作人] {operator['user_name']} {operator}")
        # set_log.debug(f"[changelogs test] {type(ticketinfo.changeLogs)} {ticketinfo.changeLogs}")
        ticketinfo.owner = [ticketinfo.changeLogs[-1]['operator']]

        ticketinfo.stage = "处理中"
        changeLogs = {
            "type": "not_resolve",
            "time": time_now,
            "operator": operator['user_name'],
            "content": "驳回了工单"
        }
        ticketinfo.changeLogs.append(changeLogs)
        ticketinfo.save()


        # 同步卡片
        # url = ITMP_URL + "/circulation/api/syn_card/"
        url = ITMP_ROBOT_URL + "/circulation/api/syn_card/"
        # url = "http://127.0.0.1:8765/circulation/api/syn_card/"
        post_data = {
            "data":{
                "job_id": ticketID,
                # 处理人id
                "open_id": operator['user_id'],
                "apps" : apps,
                # // 指派：appoint， 抑制：restrain， 认领：claim， 研发确认已解决：resolved， 登记人确认已解决：confirm_resolve, 登记人驳回：not_resolve
                "action": "not_resolve"
            }
        }

        headers = {
            'Content-Type': 'application/json'
        }
        post_data = json.dumps(post_data)
        # set_log.debug(f"[POST URL] {url}")
        set_log.debug(f"[CARD DATA] {post_data}")
        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(async_send_report(url, post_data, headers))
            loop.close()
        # 在新线程中执行异步任务
        thread = threading.Thread(target=run_async_task)
        thread.start()
        # CYCustomersFeedbackInfo.objects.filter(pk=pk).update(**data)
        set_log.info(f"[CompleteTicket]  SUCCESS")
        # 创建授予相关权限权限
        return 20000, data_return
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'

# 归档工单
def my_checkTicket(request):
    try:
        set_log.info("-" * 50)
        set_log.info("Here is my_checkTicket")
        # cookie = get_cookie(request)
        # operator = UserInfo.objects.filter(userid=cookie['UserID'])[0].name
        # set_log.info(f"operator: {operator}")

        time_now = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        data = request.data['data']
        apps = data.get('apps', [])
        set_log.debug(f"[frontend data] {data}")
        data_return = copy.deepcopy(data)
        ticketID = data.pop('ticketID')
        set_log.debug(f"[TICKET ID] {ticketID}")

        # if len(str(pk)) < 3:
        #     ticketinfo = CYCustomersFeedbackInfo.objects.filter(pk=pk)[0]
        # else:
        ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]
        set_log.debug(f"[ticket_info] {ticketinfo}")

        if 'stage' in data.keys():
            data.pop('stage')
        # 获取操作人信息
        # if 'feedbackPerson' in data.keys():
        #     operator = data['feedbackPerson']
        #     if operator == None:
        #         # operator = get_user(request)
        #         operator = CustomersFeedbackSerializers(ticketinfo)['feedbackPerson']
        #         operator = MyUserInfoSerializers(MyUserInfo.objects.filter(user_name=operator)[0]).data
        #     elif isinstance(operator, str):
        #         operator = MyUserInfoSerializers(MyUserInfo.objects.filter(user_name=operator)[0]).data
        # else:
        operator = get_user(request)
        set_log.debug(f"[操作人] {operator['user_name']} {operator}")
        # data['feedbackPerson'] = operator['user_name']




        ticketinfo.stage = "已归档"
        changeLogs = {
            "type": "confirm_resolve",
            "time": time_now,
            "operator": operator['user_name'],
            "content": "归档了工单"
        }
        ticketinfo.changeLogs.append(changeLogs)
        ticketinfo.save()

        # 同步卡片
        # url = ITMP_URL + "/circulation/api/syn_card/"
        url = ITMP_ROBOT_URL + "/circulation/api/syn_card/"
        # url = "http://127.0.0.1:8765/circulation/api/syn_card/"
        post_data = {
            "data":{
                "job_id": ticketID,
                # 处理人id
                "open_id": operator['user_id'],
                "apps" : apps,
                # // 指派：appoint， 抑制：restrain， 认领：claim， 研发确认已解决：resolved， 登记人确认已解决：confirm_resolve, 登记人驳回：not_resolve
                "action": "confirm_resolve"
            }
        }

        headers = {
            'Content-Type': 'application/json'
        }
        post_data = json.dumps(post_data)
        # set_log.debug(f"[POST URL] {url}")
        set_log.debug(f"[CARD DATA] {post_data}")
        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(async_send_report(url, post_data, headers))
            loop.close()
        # 在新线程中执行异步任务
        thread = threading.Thread(target=run_async_task)
        thread.start()
        # CYCustomersFeedbackInfo.objects.filter(pk=pk).update(**data)
        set_log.info(f"[CompleteTicket]  SUCCESS")
        # 创建授予相关权限权限
        return 20000, data_return
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'

# 删除
def my_deleteTicket(request):
    set_log.info("-" * 50)
    # cookie = get_cookie(request)
    set_log.info("Here is my_deleteTicket")
    data = request.data['data']
    set_log.debug(f"[frontend data] {data}")

    ticketID = data['ticketID']
    set_log.debug(f"get ticketID : {ticketID}")
    # 删除故障报告
    ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]
    set_log.debug(f"[ticket_info] {ticketinfo}")

    # 同步卡片
    # url = ITMP_URL + "/circulation/api/recall/"
    url = ITMP_ROBOT_URL + "/circulation/api/recall/"
    # url = "http://127.0.0.1:8765/circulation/api/recall/"
    post_data = {
        "job_id": ticketID
    }
    headers = {
        'Content-Type': 'application/json'
    }
    post_data = json.dumps(post_data)
    # set_log.debug(f"[POST URL] {url}")
    set_log.debug(f"[CARD DATA] {post_data}")
    def run_async_task():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(async_send_report(url, post_data, headers))
        loop.close()
    # 在新线程中执行异步任务
    thread = threading.Thread(target=run_async_task)
    thread.start()

    ticketinfo.delete()
    set_log.info(f" 删除工单id:{ticketID} 成功")
    return 20000, '删除成功'


def get_cookie(request):
    set_log.info("-" * 50)
    set_log.info("Here is get frontend cookie")
    cookie = request.META.get("HTTP_COOKIE")
    temp = cookie.split('; ')
    cookie = dict()
    for i in temp:
        i = i.split('=')
        cookie[i[0]] = i[1]
    cookie['Authorization'] = cookie['Authorization'].replace('%20', ' ')
    set_log.debug(f"frontend cookie: {cookie}")
    return cookie


def get_user(request):
    token = request.headers.get("Authorization")
    if token[6] == ' ':
        token = token[7:]
    set_log.debug(f"[frontend token] {token}")
    username = jwt_decode_handler(token)
    set_log.debug(f"[username] {username}")

    user = MyUserInfoSerializers(MyUserInfo.objects.filter(email=username['email'], not_send="False")[0]).data
    set_log.info(f"[user] {user}")

    return user

# 卡片操作同步平台
def my_cardupdate(request):
    try:
        set_log.info("-" * 50)
        set_log.info("Here is my_cardupdate")
        time_now = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))

        data = request.data['data']
        set_log.debug(f"[frontend data] {data}")
        # 获取认领人信息
        operator = data['operator']
        operator = MyUserInfoSerializers(MyUserInfo.objects.filter(user_id=operator, not_send="False")[0]).data

        set_log.debug(f"[操作人] {operator['user_name']} {operator}")


        ticketID = data.pop('ticketID')
        set_log.debug(f"[TICKET ID] {ticketID}")
        try:
            ticketinfo = CYCustomersFeedbackInfo.objects.filter(ticketID=ticketID)[0]
            set_log.debug(f"[ticket_info] {ticketinfo}")
        except IndexError as e:
            set_log.warning(f"{e}")
            set_log.info(f"ticketID 不存在 {ticketID}")
            return 1004, "ticketID 不存在"
        option = data['action']
        # 认领
        if option == 'claim':
            ticketinfo.stage = "处理中"
            ticketinfo.respondent = operator['user_name']
            ticketinfo.owner = [operator['user_name']]
            ticketinfo.responseTime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
            changeLogs = {
                "type": "claim",
                "time": time_now,
                "operator": operator['user_name'],
                "content": "认领了工单"
            }
            set_log.info(f"{changeLogs}")
            ticketinfo.changeLogs.append(changeLogs)
        # 指派
        elif option == 'appoint':
            try:
                owner = MyUserInfoSerializers(MyUserInfo.objects.filter(user_id=data['assignor'], not_send="False")[0]).data
                set_log.debug(f"[被指派人] {owner}")
            except IndexError as e:
                set_log.warning(f"{e}")
                set_log.info(f"被指派人ID 不存在 {data['assignor']}")
                return 1004, "被指派人ID 不存在"
            ticketinfo.owner = [owner['user_name']]
            changeLogs = {
                "type": "appoint",
                "time": time_now,
                # 指派
                "assignor": operator['user_name'],
                # 被指派
                "assignee": owner['user_name'],
                "content": "指派了工单"
            }
            set_log.info(f"{changeLogs}")
            ticketinfo.changeLogs.append(changeLogs)
        # 抑制,只需同步changelog
        elif option == 'restrain':
            # set_log.info("")
            changeLogs = {
                "type": "restrain",
                "time": time_now,
                "operator": operator['user_name'],
                "start_time": data['start_time'],
                "end_time": data['end_time'],
                "content": "抑制了工单"
            }
            set_log.info(f"{changeLogs}")
            ticketinfo.changeLogs.append(changeLogs)
        # 处理完成
        elif option == 'resolved':
            ticketinfo.stage = "已处理"
            ticketinfo.devProcessor = operator['user_name']
            # owner = MyUserInfoSerializers(MyUserInfo.objects.filter(user_name=ticketinfo.feedbackPerson)[0]).data
            ticketinfo.owner = [ticketinfo.feedbackPerson]
            ticketinfo.endTime = time_now
            if data.get('cause', '待补充'):
                ticketinfo.cause = data.get('cause', '待补充')

            changeLogs = {
                "type": "resolved",
                "time": time_now,
                "operator": operator['user_name'],
                "content": "处理完成工单"
            }
            set_log.info(f"{changeLogs}")

            ticketinfo.changeLogs.append(changeLogs)
        # 登记人确认已解决
        elif option == 'confirm_resolve':
            ticketinfo.stage = "已归档"
            changeLogs = {
                "type": "confirm_resolve",
                "time": time_now,
                "operator": operator['user_name'],
                "content": "归档了工单"
            }
            ticketinfo.changeLogs.append(changeLogs)
        # 登记人驳回
        elif option == 'not_resolve':
            ticketinfo.stage = "处理中"
            set_log.debug(f"[] {ticketinfo.changeLogs[-1]}")
            owner = MyUserInfoSerializers(MyUserInfo.objects.filter(user_name=ticketinfo.changeLogs[-1]['operator'], not_send="False")[0]).data
            ticketinfo.owner = [owner['user_name']]
            changeLogs = {
                "type": "not_resolve",
                "time": time_now,
                "operator": operator['user_name'],
                "content": "驳回了工单,请重新核实问题"
            }
            set_log.info(f"{changeLogs}")

            ticketinfo.changeLogs.append(changeLogs)
        ticketinfo.save()


        set_log.info(f"[Card Update]  SUCCESS")

        return 20000, "同步成功"
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'


# 工单手动催促
def my_urge_ticket(request):
    try:
        from common.robot.get_tenant_access_token import get_access_token
        from common.robot.send_message import send_message
        from common.utils.exception import send_exception
        set_log.info("-" * 40)
        set_log.info("Here is urge_ticket")
        user = get_user(request)
        data = request.data['data']
        ticketID = data.get('ticketID', '未知')
        if ticketID == '未知':
            return -4, "催促失败"
        # 频控判断
        urge_info = UrgeInfo.objects.filter(name=user['user_name'], email=user['email'], ticketID=ticketID)
        set_log.debug(f"[urge_info] {urge_info.count()} {urge_info}")
        count = urge_info.count()
        ret_data = {
            "status": "请求成功"
            }
        if count == 1:
            urge_info = urge_info[0]
            set_log.debug(f"[lastUrgeTime] {urge_info.lastUrgeTime}")
            now = datetime.now()
            set_log.debug(f"[now] {now}")
            spend_time = now - urge_info.lastUrgeTime
            availableTime = 3600 - spend_time.seconds
            if spend_time.seconds <= 3600:
                set_log.info(f"[催促频控中 剩 {availableTime} 秒 -> {availableTime//60} 分钟]")
                ret_data["lastUrgeTime"] = UrgeInfoSerializers(urge_info).data['lastUrgeTime']
                ret_data["availableTime"] = f"{availableTime//60}分钟"
                ret_data["status"] = "请求失败"
                return 20000, ret_data
            set_log.info(f"[距上次催促已过 {spend_time.seconds} 秒 -> {availableTime//60} 分钟 可催促]")
        ticket_info = CYCustomersFeedbackInfo.objects.get(ticketID=ticketID)
        set_log.debug(f"[frontend data] {data}")
        access_token = get_access_token()
        msg = {'text': f'【用户反馈工单】[{ticket_info.problemDescription}]({ITMP_URL}/feedback/ticket-detail/{ticketID})，有人催促啦~请及时处理哈~'}
        # 获取工单负责人
        user_list = ticket_info.owner
        user_id_list = [MyUserInfo.objects.get(user_name=i).user_id for i in user_list]
        # 发消息类型催促
        for i in user_id_list:
            if i != "not found":
                send_message_id = send_message(access_token, i, msg)
                # 发送失败
                if not send_message_id.startswith("om_"):
                        code = 1010
                        msg = {
                            "header": {
                                "code": code,
                                "msg": "failed"
                            },
                            "details": f"{send_message_id}"
                        }
                        send_exception(code, f"[SEND_REPORT] Card send error: {send_message_id}")
                    # return Response(return_code(-4, data=msg))
                else:
                    set_log.info(f"[send message to {i} success {send_message_id}]")   
            else:
                set_log.info(f"{user_list} user id miss {i}") 
        # 回复类型催促
        # message_id_list = MsgCard.objects.get(job_id=data['ticketID'])
        # reply_message(access_token, message_id_list[-1], remind_msg)
                
        # 催促成功落盘频控
        if count == 1:
            urge_info.lastUrgeTime = datetime.now()
        else:
            urge_info = UrgeInfo(name=user['user_name'], email=user['email'], ticketID=ticketID, lastUrgeTime=datetime.now())
        urge_info.save()
        set_log.info(f"[urgeTicket SUCCESS]")
        return 20000, ret_data
    except Exception as e:
        set_log.info(f"[urgeTicket FALSE]")
        set_log.error(f"{e}")
        return -1, "催促失败"

def my_resendTicket(request):
    try:
        set_log.info("-" * 40)
        set_log.info("Here is my_resendTicket")
        # cookie = get_cookie(request)
        # creator = UserInfo.objects.filter(userid=cookie['UserID'])[0].name
        # set_log.info(f"creator: {creator}")


        data = request.data['data']
        set_log.debug(f"[frontend data] {data}")
        ticketID = data.get('ticketID', 'error')
        if ticketID == 'error':
            return -1
        ticketinfo = CYCustomersFeedbackSerializers(CYCustomersFeedbackInfo.objects.get(ticketID=ticketID)).data


        # 获取反馈人
        feedbackPerson = MyUserInfo.objects.get(user_name=ticketinfo['feedbackPerson'])
        # feedbackPerson = feedbackPerson
        # 获取反馈人open_id
        try:
            if feedbackPerson.user_id == "not found":
                registrant = feedbackPerson.user_name
            else:
                registrant = feedbackPerson.user_id
        except Exception as e:
            set_log.warning(f"用户不存在,触发兜底")
            registrant = 'ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2'

        try:
            # 联动飞书发送卡片
            url = ITMP_URL + "/circulation/api/send_report/"
            # url = "https://127.0.0.1:8765/circulation/api/send_report/"
            set_log.debug(f"[itmp-jarvis-url] {url}")
            fault_time = ticketinfo.get("startTime")
            set_log.debug(f"[fault_time] {fault_time}")
            if fault_time == None or fault_time == 'null':
                fault_time = ''
            photo_url = [i['url'] for i in ticketinfo.get('attachments', [])]
            developer_id_list = []
            for i in ticketinfo['devOwner']:
                devOwner = MyUserInfo.objects.filter(user_name=i, not_send="False")
                set_log.debug(f"[devOwner] {devOwner} {devOwner.count()}")
                if devOwner.count() != 0:
                    developer_id_list.append(devOwner[0].user_id)
            # [i.get('open_id', 'ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2') for i in data['businessOwner']]
            operator_id_list = []
            for i in ticketinfo['businessOwner']:
                businessOwner = MyUserInfo.objects.filter(user_name=i, not_send="False")
                if businessOwner.count() != 0:
                    operator_id_list.append(businessOwner[0].user_id)
            # 工单链接
            ticket_url = itmp_conf['itmp_url'] + "/feedback/ticket-detail/" + str(ticketID)
            post_data = {
                "job_id": ticketID,
                "describe": ticketinfo.get('problemDescription', ''),
                "severityLevel": ticketinfo.get('severityLevel', '单例问题'),
                "tt_id": ticketinfo.get('userTtid', []),
                # 反馈时间
                "create_time": ticketinfo.get("enterTime"),
                # 出现时间
                "fault_time": [fault_time],
                "device": ticketinfo.get('osType', []),
                "app_type": ticketinfo.get('apps', []),
                "app_version": ticketinfo.get('appVersion', []),
                "function": ticketinfo.get("functionType"),
                # 登记人open_id
                "registrant": registrant,
                "url": ticket_url,
                # "photo_url": ["https://www.baidu.com"],
                "photo_url": photo_url,
                # Jarvis测试群
                # "chat_id": "oc_7adc3624a795ead71e2528ee01bedb32",
                # ITMP 测试群
                # "chat_id": "oc_f52d5bfd4b7f11b144ab2b995f45a419",
                "feishu_group": ticketinfo.get('feishuGroup'),
                "feishu_group_id": ticketinfo.get('feishuGroupId'),
                # "developer_id_list": ["ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2"],
                "developer_id_list": developer_id_list,
                # "operator_id_list": ["ou_6a7a6ff77c0bfb90d2d42ce3489bb3d2"]
                "operator_id_list": operator_id_list,
                "stage": "待处理",
                "changeLogs": ticketinfo.get("changeLogs")
            }
            headers = {
                'Content-Type': 'application/json'
            }
            set_log.debug(f"[CARD DATA] {post_data}")
            post_data = json.dumps(post_data)
            # set_log.debug(f"[POST URL] {url}")
            # set_log.debug(f"[CARD DATA] {post_data}")
            req = requests.post(url=url, data=post_data, headers=headers)
            # req = requests.post(url=url, data=post_data, headers=headers)
            if req.status_code == 200:
                set_log.debug(f"[SEND SUCCESS] {req} {req.json()}")
            else:
                set_log.debug(f"[SEND FALSE] {req} {req.json()}")
        except Exception as e:
            set_log.debug(f"[重推工单同步卡片异常] {e}")
        return 20000, "重推成功"
    except Exception as e:
        set_log.error(f"error:{e}")
        return -1, 'error'
