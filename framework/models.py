from datetime import datetime

from django.db import models
import django.utils.timezone as timezone


# Create your models here.

# TT客户、运营问题反馈表
class CustomersFeedbackInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    # 唯一id 时间戳 + id(补六位)
    ticketID = models.CharField(db_column='ticketID', max_length=100, blank=True, null=True, default="")
    # 当前状态
    stage = models.CharField(db_column='stage', blank=True, null=True, max_length=100, default="")
    # 当前处理人
    owner = models.JSONField(db_column='owner', blank=True, null=True, default=[])
    # 运维负责人
    businessOwner = models.JSONField(db_column='businessOwner', blank=True, null=True, default=[])
    # 研发负责人
    devOwner = models.JSONField(db_column='devOwner', blank=True, null=True, default=[])
    # 客户端负责人
    # clientOwner = models.JSONField(db_column='clientOwner', blank=True, null=True)
    # IOS客户端负责人
    clientIOSOwner = models.JSONField(db_column='clientIOSOwner', blank=True, null=True, default=[])
    # Android客户端负责人
    clientAndroidOwner = models.JSONField(db_column='clientAndroidOwner', blank=True, null=True, default=[])
    # 飞书群信息
    feishuGroup = models.CharField(db_column='feishuGroup', blank=True, null=True, max_length=1000, default="")
    feishuGroupId = models.CharField(db_column='feishuGroupId', blank=True, null=True, max_length=1000, default="")


    # 问题内容描述
    problemDescription = models.TextField(db_column='problemDescription', blank=True, null=True, default="")
    # TTID
    userTtid = models.JSONField(db_column='userTtid', blank=True, null=True, default=[])
    # 附件
    attachments = models.JSONField(db_column='attachments', blank=True, null=True, default=[])
    # 影响软件
    apps = models.JSONField(db_column='apps', blank=True, null=True, default=[])
    # 版本
    appVersion = models.JSONField(db_column='appVersion', blank=True, null=True, default=[])
    # 系统类型
    osType = models.JSONField(db_column='osType', blank=True, null=True, default=[])
    # 手机具体类型
    mobileType = models.JSONField(db_column='mobileType', blank=True, null=True, default=[])
    # 是否有发端内反馈
    hasFeedback = models.CharField(db_column='hasFeedback', blank=True, null=True, max_length=100, default="")
    # 问题时间 时间戳
    startTime = models.DateTimeField(db_column='startTime', blank=True, null=True)
    # 录入时间
    enterTime = models.DateTimeField(db_column='enterTime', blank=True, default=datetime.now())
    # 问题反馈人
    feedbackPerson = models.CharField(db_column='feedbackPerson', blank=True, null=True, max_length=100, default="")
    # 是否工作时间反馈
    hasWorkingHours = models.CharField(db_column='hasWorkingHours', blank=True, null=True, max_length=100, default="")
    # 紧急程度
    severityLevel = models.CharField(db_column='severityLevel', max_length=100, blank=True, null=True, default="")
    # 问题分类 ['一级', '二级', '三级']
    functionType = models.JSONField(db_column='functionType', blank=True, null=True, default=[])
    # 一级问题分类
    firstLevelCategory = models.CharField(db_column='firstLevelCategory', blank=True, null=True, max_length=100, default="")
    # 二级问题分类
    secondLevelCategory = models.CharField(db_column='secondLevelCategory', blank=True, null=True, max_length=100, default="")
    # 三级问题分类
    thirdLevelCategory = models.CharField(db_column='thirdLevelCategory', blank=True, null=True, max_length=100, default="")
    # 选择其他的原因
    reason = models.CharField(db_column='reason', blank=True, null=True, max_length=1000, default="")


    # 响应人
    respondent = models.CharField(db_column='respondent', blank=True, null=True, max_length=100, default="")
    # 响应时间 时间戳
    responseTime = models.DateTimeField(db_column='responseTime', blank=True, null=True)

    # 研发处理人
    devProcessor = models.CharField(db_column='devProcessor', blank=True, null=True, max_length=100, default="")
    # 问题原因
    cause = models.TextField(db_column='cause', blank=True, null=True, max_length=1000, default="")
    # 处理结果
    result = models.TextField(db_column='result', blank=True, null=True, max_length=1000, default="")
    # 解决时间 时间戳
    endTime = models.DateTimeField(db_column='endTime', blank=True, null=True)
    # 是否需要发版解决
    hasClientRelease = models.CharField(db_column='hasClientRelease', blank=True, null=True, max_length=100, default="")
    # 客户端或服务端
    endType = models.JSONField(db_column='endType', blank=True, null=True, max_length=100, default="")
    # 日志
    changeLogs = models.JSONField(db_column='changeLogs', blank=True, null=True, default=[])
    # 用户是否端内反馈
    isUploadLogs =  models.CharField(db_column='isUploadLogs', max_length=100, blank=True, null=True, default="")

    objects = models.Manager()

    class Meta:
        app_label = "framework"
        db_table = 'customers_feedback'

# TT功能负责人表
class ResponsiblePersonsInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    # 业务线
    biz = models.CharField(db_column='biz', blank=True, null=True, max_length=100)
    # 一级问题分类
    firstLevelCategory = models.CharField(db_column='firstLevelCategory', blank=True, null=True, max_length=100)
    # 二级问题分类
    secondLevelCategory = models.CharField(db_column='secondLevelCategory', blank=True, null=True, max_length=100)
    # 三级问题分类
    thirdLevelCategory = models.CharField(db_column='thirdLevelCategory', blank=True, null=True, max_length=100)
    # 对应飞书群信息
    groupInfo = models.JSONField(db_column='groupInfo', blank=True, null=True)
    # 研发负责人
    devOwner = models.JSONField(db_column='devOwner', blank=True, null=True)
    # 运维负责人
    sreOwner = models.JSONField(db_column='sreOwner', blank=True, null=True)
    # IOS客户端负责人
    clientIOSOwner = models.JSONField(db_column='clientIOSOwner', blank=True, null=True)
    # Android客户端负责人
    clientAndroidOwner = models.JSONField(db_column='clientAndroidOwner', blank=True, null=True)
    # 产品负责人
    productOwner = models.JSONField(db_column='productOwner', blank=True, null=True)
    # 运营负责人
    operationsOwner = models.JSONField(db_column='operationsOwner', blank=True, null=True)
    # 玩法文档
    playMethodDoc = models.CharField(db_column='playMethodDoc', blank=True, null=True, max_length=500)
    # 路径指引
    pathGuide = models.JSONField(db_column='pathGuide', blank=True, null=True)
    objects = models.Manager()

    class Meta:
        app_label = "framework"
        db_table = 'responsible_persons'

# 负责人信息表
class PersonsInitInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    user_name = models.CharField(max_length=255, blank=True, null=True)
    user_id = models.CharField(max_length=255, blank=True, null=True)
    email = models.CharField(max_length=255, unique=True)

    objects = models.Manager()

    class Meta:
        app_label = "framework"
        db_table = 'persons_init'

# 催促频控表
class UrgeInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    email = models.CharField(max_length=255)
    ticketID = models.CharField(db_column='ticketID', max_length=100, blank=True, null=True, default="")
    lastUrgeTime = models.DateTimeField(db_column='endTime', blank=True, null=True, default=datetime.now())


    objects = models.Manager()

    class Meta:
        app_label = "framework"
        db_table = 'urge_info'

# X项目反馈表
class XCustomersFeedbackInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    # 唯一id 时间戳 + id(补六位)
    ticketID = models.CharField(db_column='ticketID', max_length=100, blank=True, null=True, default="")
    # 当前状态
    stage = models.CharField(db_column='stage', blank=True, null=True, max_length=100, default="")
    # 当前处理人
    owner = models.JSONField(db_column='owner', blank=True, null=True, default=[])
    # 业务负责人
    businessOwner = models.JSONField(db_column='businessOwner', blank=True, null=True, default=[])
    # 研发负责人
    devOwner = models.JSONField(db_column='devOwner', blank=True, null=True, default=[])
    # IOS客户端负责人
    clientIOSOwner = models.JSONField(db_column='clientIOSOwner', blank=True, null=True, default=[])
    # Android客户端负责人
    clientAndroidOwner = models.JSONField(db_column='clientAndroidOwner', blank=True, null=True, default=[])
    # 飞书群信息
    feishuGroup = models.CharField(db_column='feishuGroup', blank=True, null=True, max_length=1000, default="")
    feishuGroupId = models.CharField(db_column='feishuGroupId', blank=True, null=True, max_length=1000, default="")
    # 效果记录
    category = models.CharField(db_column='category', blank=True, null=True, max_length=1000, default="")

    # 问题内容描述
    problemDescription = models.TextField(db_column='problemDescription', blank=True, null=True, default="")
    # ID
    ids = models.JSONField(db_column='ids', blank=True, null=True, default=[])
    # 模型版本
    modelType = models.JSONField(db_column='modelType', blank=True, null=True, default=[])
    # 附件
    attachments = models.JSONField(db_column='attachments', blank=True, null=True, default=[])
    # ID类型
    idsType = models.JSONField(db_column='idsType', blank=True, null=True, default=[])
    # 训练方式
    trainType = models.JSONField(db_column='trainType', blank=True, null=True, default=[])
    # 问题时间 时间戳
    startTime = models.DateTimeField(db_column='startTime', blank=True, null=True)
    # 录入时间
    enterTime = models.DateTimeField(db_column='enterTime', blank=True, default=datetime.now())
    # 问题反馈人
    feedbackPerson = models.CharField(db_column='feedbackPerson', blank=True, null=True, max_length=100, default="")
    # 是否工作时间反馈
    hasWorkingHours = models.CharField(db_column='hasWorkingHours', blank=True, null=True, max_length=100, default="")
    # 紧急程度
    severityLevel = models.CharField(db_column='severityLevel', max_length=100, blank=True, null=True, default="")
    # 问题分类 ['一级', '二级', '三级']
    functionType = models.JSONField(db_column='functionType', blank=True, null=True, default=[])
    # 一级问题分类
    firstLevelCategory = models.CharField(db_column='firstLevelCategory', blank=True, null=True, max_length=100, default="")
    # 二级问题分类
    secondLevelCategory = models.CharField(db_column='secondLevelCategory', blank=True, null=True, max_length=100, default="")
    # 三级问题分类
    thirdLevelCategory = models.CharField(db_column='thirdLevelCategory', blank=True, null=True, max_length=100, default="")
    # 选择其他的原因
    reason = models.CharField(db_column='reason', blank=True, null=True, max_length=1000, default="")


    # 响应人
    respondent = models.CharField(db_column='respondent', blank=True, null=True, max_length=100, default="")
    # 响应时间 时间戳
    responseTime = models.DateTimeField(db_column='responseTime', blank=True, null=True)

    # 研发处理人
    devProcessor = models.CharField(db_column='devProcessor', blank=True, null=True, max_length=100, default="")
    # 问题原因
    cause = models.TextField(db_column='cause', blank=True, null=True, max_length=1000, default="")
    # 处理结果
    result = models.TextField(db_column='result', blank=True, null=True, max_length=1000, default="")
    # 解决时间 时间戳
    endTime = models.DateTimeField(db_column='endTime', blank=True, null=True)
    # 日志
    changeLogs = models.JSONField(db_column='changeLogs', blank=True, null=True, default=[])


    objects = models.Manager()

    class Meta:
        app_label = "framework"
        db_table = 'xcustomers_feedback'

# 唱鸭客户、运营问题反馈表
class CYCustomersFeedbackInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    # 唯一id 时间戳 + id(补六位)
    ticketID = models.CharField(db_column='ticketID', max_length=100, blank=True, null=True, default="")
    # 当前状态
    stage = models.CharField(db_column='stage', blank=True, null=True, max_length=100, default="")
    # 当前处理人
    owner = models.JSONField(db_column='owner', blank=True, null=True, default=[])
    # 运维负责人
    businessOwner = models.JSONField(db_column='businessOwner', blank=True, null=True, default=[])
    # 研发负责人
    devOwner = models.JSONField(db_column='devOwner', blank=True, null=True, default=[])
    # 客户端负责人
    # clientOwner = models.JSONField(db_column='clientOwner', blank=True, null=True)
    # IOS客户端负责人
    clientIOSOwner = models.JSONField(db_column='clientIOSOwner', blank=True, null=True, default=[])
    # Android客户端负责人
    clientAndroidOwner = models.JSONField(db_column='clientAndroidOwner', blank=True, null=True, default=[])
    # 飞书群信息
    feishuGroup = models.CharField(db_column='feishuGroup', blank=True, null=True, max_length=1000, default="")
    feishuGroupId = models.CharField(db_column='feishuGroupId', blank=True, null=True, max_length=1000, default="")


    # 问题内容描述
    problemDescription = models.TextField(db_column='problemDescription', blank=True, null=True, default="")
    # TTID
    userTtid = models.JSONField(db_column='userTtid', blank=True, null=True, default=[])
    # 附件
    attachments = models.JSONField(db_column='attachments', blank=True, null=True, default=[])
    # 影响软件
    apps = models.JSONField(db_column='apps', blank=True, null=True, default=[])
    # 版本
    appVersion = models.JSONField(db_column='appVersion', blank=True, null=True, default=[])
    # 系统类型
    osType = models.JSONField(db_column='osType', blank=True, null=True, default=[])
    # 手机具体类型
    mobileType = models.JSONField(db_column='mobileType', blank=True, null=True, default=[])
    # 是否有发端内反馈
    hasFeedback = models.CharField(db_column='hasFeedback', blank=True, null=True, max_length=100, default="")
    # 问题时间 时间戳
    startTime = models.DateTimeField(db_column='startTime', blank=True, null=True)
    # 录入时间
    enterTime = models.DateTimeField(db_column='enterTime', blank=True, default=datetime.now())
    # 问题反馈人
    feedbackPerson = models.CharField(db_column='feedbackPerson', blank=True, null=True, max_length=100, default="")
    # 是否工作时间反馈
    hasWorkingHours = models.CharField(db_column='hasWorkingHours', blank=True, null=True, max_length=100, default="")
    # 紧急程度
    severityLevel = models.CharField(db_column='severityLevel', max_length=100, blank=True, null=True, default="")
    # 问题分类 ['一级', '二级', '三级']
    functionType = models.JSONField(db_column='functionType', blank=True, null=True, default=[])
    # 一级问题分类
    firstLevelCategory = models.CharField(db_column='firstLevelCategory', blank=True, null=True, max_length=100, default="")
    # 二级问题分类
    secondLevelCategory = models.CharField(db_column='secondLevelCategory', blank=True, null=True, max_length=100, default="")
    # 三级问题分类
    thirdLevelCategory = models.CharField(db_column='thirdLevelCategory', blank=True, null=True, max_length=100, default="")
    # 选择其他的原因
    reason = models.CharField(db_column='reason', blank=True, null=True, max_length=1000, default="")


    # 响应人
    respondent = models.CharField(db_column='respondent', blank=True, null=True, max_length=100, default="")
    # 响应时间 时间戳
    responseTime = models.DateTimeField(db_column='responseTime', blank=True, null=True)

    # 研发处理人
    devProcessor = models.CharField(db_column='devProcessor', blank=True, null=True, max_length=100, default="")
    # 问题原因
    cause = models.TextField(db_column='cause', blank=True, null=True, max_length=1000, default="")
    # 处理结果
    result = models.TextField(db_column='result', blank=True, null=True, max_length=1000, default="")
    # 解决时间 时间戳
    endTime = models.DateTimeField(db_column='endTime', blank=True, null=True)
    # 是否需要发版解决
    hasClientRelease = models.CharField(db_column='hasClientRelease', blank=True, null=True, max_length=100, default="")
    # 客户端或服务端
    endType = models.JSONField(db_column='endType', blank=True, null=True, max_length=100, default="")
    # 日志
    changeLogs = models.JSONField(db_column='changeLogs', blank=True, null=True, default=[])
    # 用户是否端内反馈
    isUploadLogs =  models.CharField(db_column='isUploadLogs', max_length=100, blank=True, null=True, default="")

    objects = models.Manager()

    class Meta:
        app_label = "framework"
        db_table = 'cycustomers_feedback'        