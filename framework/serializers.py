from rest_framework import serializers
from .models import *


class CustomersFeedbackSerializers(serializers.ModelSerializer):
    """客户、运营问题反馈序列化"""
    startTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    enterTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    responseTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    endTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    # hasFeedback = serializers.SerializerMethodField()
    # feedbackPerson = serializers.SerializerMethodField()
    # hasWorkingHours = serializers.SerializerMethodField()
    # severityLevel = serializers.SerializerMethodField()
    # firstLevelCategory = serializers.SerializerMethodField()
    # secondLevelCategory = serializers.SerializerMethodField()
    # thirdLevelCategory = serializers.SerializerMethodField()
    # reason = serializers.SerializerMethodField()

    # ticketID = serializers.CharField(label="id")

    class Meta:
        model = CustomersFeedbackInfo
        # fields = '__all__'
        field = '__all__'
        exclude = ["id"]
        # fields = (
        #     'id', 'ticketID', 'stage', 'owner', 'businessOwner', 'devOwner', 'clientOwner', 'problemDescription', 'userTtid', 'attachments',
        #     'apps', 'appVersion', 'osType', 'mobileType', 'hasFeedback', 'startTime', 'feedbackPerson', 'hasWorkingHours',
        #     'severityLevel', 'firstLevelCategory', 'secondLevelCategory', 'thirdLevelCategory', 'reason', 'respondent', 'responseTime',
        #     'devProcessor', 'cause', 'endTime', 'hasClientRelease', 'changeLogs')

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for i,j in data.items():
            if not j and j != []:
                data[i] = ""
        return data

class CYCustomersFeedbackSerializers(serializers.ModelSerializer):
    """客户、运营问题反馈序列化"""
    startTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    enterTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    responseTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    endTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    # hasFeedback = serializers.SerializerMethodField()
    # feedbackPerson = serializers.SerializerMethodField()
    # hasWorkingHours = serializers.SerializerMethodField()
    # severityLevel = serializers.SerializerMethodField()
    # firstLevelCategory = serializers.SerializerMethodField()
    # secondLevelCategory = serializers.SerializerMethodField()
    # thirdLevelCategory = serializers.SerializerMethodField()
    # reason = serializers.SerializerMethodField()

    # ticketID = serializers.CharField(label="id")

    class Meta:
        model = CYCustomersFeedbackInfo
        # fields = '__all__'
        field = '__all__'
        exclude = ["id"]
        # fields = (
        #     'id', 'ticketID', 'stage', 'owner', 'businessOwner', 'devOwner', 'clientOwner', 'problemDescription', 'userTtid', 'attachments',
        #     'apps', 'appVersion', 'osType', 'mobileType', 'hasFeedback', 'startTime', 'feedbackPerson', 'hasWorkingHours',
        #     'severityLevel', 'firstLevelCategory', 'secondLevelCategory', 'thirdLevelCategory', 'reason', 'respondent', 'responseTime',
        #     'devProcessor', 'cause', 'endTime', 'hasClientRelease', 'changeLogs')

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for i,j in data.items():
            if not j and j != []:
                data[i] = ""
        return data

class xCustomersFeedbackSerializers(serializers.ModelSerializer):
    """客户、运营问题反馈序列化"""
    startTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    enterTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    responseTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    endTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    # hasFeedback = serializers.SerializerMethodField()
    # feedbackPerson = serializers.SerializerMethodField()
    # hasWorkingHours = serializers.SerializerMethodField()
    # severityLevel = serializers.SerializerMethodField()
    # firstLevelCategory = serializers.SerializerMethodField()
    # secondLevelCategory = serializers.SerializerMethodField()
    # thirdLevelCategory = serializers.SerializerMethodField()
    # reason = serializers.SerializerMethodField()

    # ticketID = serializers.CharField(label="id")

    class Meta:
        model = XCustomersFeedbackInfo
        # fields = '__all__'
        field = '__all__'
        exclude = ["id"]
        # fields = (
        #     'id', 'ticketID', 'stage', 'owner', 'businessOwner', 'devOwner', 'clientOwner', 'problemDescription', 'userTtid', 'attachments',
        #     'apps', 'appVersion', 'osType', 'mobileType', 'hasFeedback', 'startTime', 'feedbackPerson', 'hasWorkingHours',
        #     'severityLevel', 'firstLevelCategory', 'secondLevelCategory', 'thirdLevelCategory', 'reason', 'respondent', 'responseTime',
        #     'devProcessor', 'cause', 'endTime', 'hasClientRelease', 'changeLogs')

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for i,j in data.items():
            if not j and j != []:
                data[i] = ""
        return data        


class ResponsiblePersonsSerializers(serializers.ModelSerializer):
    """功能负责人序列化"""

    class Meta:
        model = ResponsiblePersonsInfo
        fields = '__all__'
        # fields = ('id', 'firstLevelCategory', 'secondLevelCategory', 'thirdLevelCategory', 'devOwner', 'clientOwner',
        #           'OperationsOwner', 'productOwner', 'playMethodDoc', 'pathGuide')

class PersonsInitSerializers(serializers.ModelSerializer):
    """功能负责人序列化"""

    class Meta:
        model = PersonsInitInfo
        fields = '__all__'


class UrgeInfoSerializers(serializers.ModelSerializer):
    """功能负责人序列化"""
    lastUrgeTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    class Meta:
        model = UrgeInfo
        fields = '__all__'
