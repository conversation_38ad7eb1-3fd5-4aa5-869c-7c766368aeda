from django.shortcuts import render
import json
from datetime import datetime
import requests
from django.contrib.auth import authenticate
from django.db.models import Q
from django.shortcuts import render
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny
from rest_framework.authentication import authenticate
# from rest_framework.decorators import api_view, authentication_classes, permission_classes, action
from .serializers import *
from .models import *
from rest_framework.response import Response
from rest_framework.views import APIView
from django.core.paginator import Paginator
from django.core import serializers
# from api_auth.models import UserInfo
from common.utils.utils import NewViewBase, return_code
from common.utils.log import Logger
# from common.utils import config
from .funcs import customers_funcs, head_funcs, my_authentication, xcustomers_funcs, cycustomers_funcs
# from . import alerts_tools
from apscheduler.schedulers.background import BackgroundScheduler
# Create your views here.
import asyncio
from asgiref.sync import async_to_sync


set_log = Logger.get_logger(__name__, 0)


class CustomersFeedbackViewset(NewViewBase):
    """信息增删改查"""
    queryset = CustomersFeedbackInfo.objects.all()
    serializer_class = CustomersFeedbackSerializers
    permission_classes = [AllowAny]
    # authentication_classes = [my_authentication.MyTokenAuthentication]
    # authentication_classes = []
    # 自定义查询全消息
    # @action(methods=['get', 'post'], detail=False, url_path='all')
    # def sel_all(self, request, *args, **kwargs):
    #     try:
    #         set_log.info("*" * 100)
    #         set_log.info("Here is api/CustomersFeedback/all")
    #         data = CustomersFeedbackSerializers(CustomersFeedbackInfo.objects.all(), many=True).data
    #         set_log.info(data, type(data))
    #         return Response(return_code(0, data=data))
    #     except Exception as e:
    #         set_log.error(f"[error] {e}")
    #         return Response(return_code(-1, data=e))

    # 自定义条件查询

    @action(methods=['get', 'post'], detail=False, url_path='selectTicket')
    # @authentication_classes([])  # 未认证请求也可以访问此 API
    # @permission_classes([])  # 只有权限满足条件的用户才能访问此 API
    def selectTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/selectTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # user = my_authentication.get_user(request)
            # if 'code' in user.keys():
            #     return Response(user)
            data, total = customers_funcs.my_selectTicket(request)
            # dashboard = customers_funcs.get_dashboard(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"CustomersFeedback异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data={'list': data, 'total': total}, status='ok'))
            # return Response(return_code(0, total=total, data=data, dataall=dataall, count=len(data)))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 创建
    @action(methods=['get', 'post'], detail=False, url_path='createTicket')
    def createTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/createTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_createTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 上传图片
    @action(methods=['get', 'post'], detail=False, url_path='upload')
    def uploadPhotp(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/upload")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_uploadPhotp(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 删除图片
    # @action(methods=['get', 'post'], detail=False, url_path='upload')
    # def uploadPhotp(self, request, *args, **kwargs):
    #     try:
    #         set_log.info("*" * 100)
    #         set_log.info("Here is api/CustomersFeedback/deleteTicket")
    #         code, data = customers_funcs.my_deleteTicket(request)
    #         return Response(return_code(code, data=data, status='ok'))
    #     except Exception as e:
    #         set_log.error(f"[error] {e}")
    #         return Response(return_code(-1, data=e, status='error'))

    # 修改用户反馈消息 状态不变
    @action(methods=['get', 'post'], detail=False, url_path='editTicket')
    def editTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/editTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_edit(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 认领
    # 待处理 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='handleTicket')
    def handleTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/handleTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_handleTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 指派
    # 待处理 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='assignTicket')
    def assignTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/assignTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_assignTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 处理完成
    # 处理中 -> 已处理
    @action(methods=['get', 'post'], detail=False, url_path='completeTicket')
    def completeTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/completeTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_completeTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 反馈人驳回
    # 处理中 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='rejectTicket')
    def rejectTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/rejectTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_rejectTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 反馈人确认
    # 处理中 -> 归档
    @action(methods=['get', 'post'], detail=False, url_path='checkTicket')
    def checkTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/checkTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_checkTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 真删除
    @action(methods=['get', 'post'], detail=False, url_path='deleteTicket')
    def deleteTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/deleteTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_deleteTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 消息卡片同步数据库接口
    @action(methods=['get', 'post'], detail=False, url_path='cardupdate')
    def cardupdate(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/cardupdate")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = customers_funcs.my_cardupdate(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 消息卡片催促
    @action(methods=['get', 'post'], detail=False, url_path='urgeTicket')
    def urgeTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/feedback/urgeTicket")
            # user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            # if code != 20000:
            #     return Response(return_code(code))
            code, data = customers_funcs.my_urge_ticket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))
        
    # 消息卡片重推
    @action(methods=['get', 'post'], detail=False, url_path='resendTicket')
    def cardupdate(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/feedback/resendTicket")
            # user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            # if code != 20000:
            #     return Response(return_code(code))
            code, data = customers_funcs.my_resendTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))
    # # dashboard
    # @action(methods=['get', 'post'], detail=False, url_path='dashboard')
    # def dashboard_sel(self, request, *args, **kwargs):
    #     data = request.data
    #     set_log.info(f"get data :{data}")
    #     fault = json.loads(
    #         serializers.serialize('json', FaultInfo.objects.filter(faultYear='2023年', isOperation='非运营类')))
    #     temp = {"fid__in": [i['fields']['fid'] for i in fault]}
    #     set_log.info(f"[fid] {temp}")
    #     rectify = json.loads(
    #         serializers.serialize('json', RectifyInfo.objects.filter(**temp).exclude(rate__in=['长期整改项', '经评估暂时不做'])))
    #     dashboard = get_dashboard(fault, rectify,
    #                               mttf_times_count([(2023, 5), (2023, 6), (2023, 1), (2023, 2), (2023, 3), (2023, 4)]))
    #     set_log.info(f"[count] {len(fault)}")
    #     return Response(return_code(0, data=dashboard))


class ResponsiblePersonsViewset(NewViewBase):
    """负责人信息增删改查"""
    queryset = ResponsiblePersonsInfo.objects.all()
    serializer_class = ResponsiblePersonsSerializers
    permission_classes = [AllowAny]
    # authentication_classes = []

    # 自定义条件查询
    @action(methods=['get', 'post'], detail=False, url_path='selectHead')
    def selectHead(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/ResponsiblePersons/selectHead")
            data, total = head_funcs.my_selectHead(request)
            # dashboard = head_funcs.get_dashboard(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"ResponsiblePersons异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data={'list': data, 'total': total}, status='ok'))
            # return Response(return_code(0, total=total, data=data, dataall=dataall, count=len(data)))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # XP业务细分树状图
    @action(methods=['get', 'post'], detail=False, url_path='categoryTree/X')
    def xcategoryTree(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/ResponsiblePersons/categoryTree/X")
            data = head_funcs.get_category_tree("XP")
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"categoryTree异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # CY业务细分树状图
    @action(methods=['get', 'post'], detail=False, url_path='categoryTree/CY')
    def cycategoryTree(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/ResponsiblePersons/categoryTree/CY")
            data = head_funcs.get_category_tree("CY")
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"categoryTree异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))
    # TT业务细分树状图
    @action(methods=['get', 'post'], detail=False, url_path='categoryTree')
    def categoryTree(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/ResponsiblePersons/categoryTree")
            data = head_funcs.get_category_tree("TT")
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"categoryTree异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))




    # 业务细分树状图
    @action(methods=['get', 'post'], detail=False, url_path='categoryTreeTest')
    def categoryTreeTest(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/ResponsiblePersons/categoryTreeTest")
            data = head_funcs.get_category_treeTest(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"categoryTreeTest异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    #
    @action(methods=['get', 'post'], detail=False, url_path='getOpenID')
    def getOpenID(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/ResponsiblePersons/getOpenID")
            token = request.headers.get("Authorization")
            if token != "admin123":
                set_log.debug(f"[token] {token}")
                return Response(return_code(2004))
            data = head_funcs.get_OpenID(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"getOpenID异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))




class CustomersFeedbackXViewset(NewViewBase):
    """信息增删改查"""
    queryset = CustomersFeedbackInfo.objects.all()
    serializer_class = CustomersFeedbackSerializers
    permission_classes = [AllowAny]

    # 创建
    @action(methods=['get', 'post'], detail=False, url_path='createTicket')
    def createTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/createTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_createTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    @action(methods=['get', 'post'], detail=False, url_path='selectTicket')
    # @authentication_classes([])  # 未认证请求也可以访问此 API
    # @permission_classes([])  # 只有权限满足条件的用户才能访问此 API
    def selectTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/selectTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # user = my_authentication.get_user(request)
            # if 'code' in user.keys():
            #     return Response(user)
            data, total = xcustomers_funcs.my_selectTicket(request)
            # dashboard = customers_funcs.get_dashboard(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"CustomersFeedback异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data={'list': data, 'total': total}, status='ok'))
            # return Response(return_code(0, total=total, data=data, dataall=dataall, count=len(data)))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))



    # 上传图片
    @action(methods=['get', 'post'], detail=False, url_path='upload')
    def uploadPhotp(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/upload")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_uploadPhotp(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 修改用户反馈消息 状态不变
    @action(methods=['get', 'post'], detail=False, url_path='editTicket')
    def editTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/editTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_edit(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 认领
    # 待处理 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='handleTicket')
    def handleTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/handleTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_handleTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 指派
    # 待处理 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='assignTicket')
    def assignTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/assignTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_assignTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 处理完成
    # 处理中 -> 已处理
    @action(methods=['get', 'post'], detail=False, url_path='completeTicket')
    def completeTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/completeTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_completeTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 反馈人驳回
    # 处理中 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='rejectTicket')
    def rejectTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/rejectTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_rejectTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 反馈人确认
    # 处理中 -> 归档
    @action(methods=['get', 'post'], detail=False, url_path='checkTicket')
    def checkTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/checkTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_checkTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 真删除
    @action(methods=['get', 'post'], detail=False, url_path='deleteTicket')
    def deleteTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/deleteTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_deleteTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 消息卡片同步数据库接口
    @action(methods=['get', 'post'], detail=False, url_path='cardupdate')
    def cardupdate(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/cardupdate")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = xcustomers_funcs.my_cardupdate(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 消息卡片催促
    @action(methods=['get', 'post'], detail=False, url_path='urgeTicket')
    def urgeTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/feedback/urgeTicket")
            # user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            # if code != 20000:
            #     return Response(return_code(code))
            code, data = xcustomers_funcs.my_urge_ticket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))
        
    # 消息卡片重推
    @action(methods=['get', 'post'], detail=False, url_path='resendTicket')
    def cardupdate(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/feedback/resendTicket")
            # user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            # if code != 20000:
            #     return Response(return_code(code))
            code, data = xcustomers_funcs.my_resendTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


class CustomersFeedbackCYViewset(NewViewBase):
    """信息增删改查"""
    queryset = CustomersFeedbackInfo.objects.all()
    serializer_class = CustomersFeedbackSerializers
    permission_classes = [AllowAny]

    # 创建
    @action(methods=['get', 'post'], detail=False, url_path='createTicket')
    def createTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/createTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_createTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    @action(methods=['get', 'post'], detail=False, url_path='selectTicket')
    # @authentication_classes([])  # 未认证请求也可以访问此 API
    # @permission_classes([])  # 只有权限满足条件的用户才能访问此 API
    def selectTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/selectTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            # user = my_authentication.get_user(request)
            # if 'code' in user.keys():
            #     return Response(user)
            data, total = cycustomers_funcs.my_selectTicket(request)
            # dashboard = customers_funcs.get_dashboard(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"CustomersFeedback异常状态码：{data}")
                return Response(return_code(data, data=[], status='error'))
            return Response(return_code(20000, data={'list': data, 'total': total}, status='ok'))
            # return Response(return_code(0, total=total, data=data, dataall=dataall, count=len(data)))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))



    # 上传图片
    @action(methods=['get', 'post'], detail=False, url_path='upload')
    def uploadPhotp(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/upload")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_uploadPhotp(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 修改用户反馈消息 状态不变
    @action(methods=['get', 'post'], detail=False, url_path='editTicket')
    def editTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/editTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_edit(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 认领
    # 待处理 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='handleTicket')
    def handleTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/handleTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_handleTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 指派
    # 待处理 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='assignTicket')
    def assignTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/assignTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_assignTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 处理完成
    # 处理中 -> 已处理
    @action(methods=['get', 'post'], detail=False, url_path='completeTicket')
    def completeTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/completeTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_completeTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 反馈人驳回
    # 处理中 -> 处理中
    @action(methods=['get', 'post'], detail=False, url_path='rejectTicket')
    def rejectTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/rejectTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_rejectTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 反馈人确认
    # 处理中 -> 归档
    @action(methods=['get', 'post'], detail=False, url_path='checkTicket')
    def checkTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/checkTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_checkTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

    # 真删除
    @action(methods=['get', 'post'], detail=False, url_path='deleteTicket')
    def deleteTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/deleteTicket")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_deleteTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 消息卡片同步数据库接口
    @action(methods=['get', 'post'], detail=False, url_path='cardupdate')
    def cardupdate(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/CustomersFeedback/cardupdate")
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            code, data = cycustomers_funcs.my_cardupdate(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 消息卡片催促
    @action(methods=['get', 'post'], detail=False, url_path='urgeTicket')
    def urgeTicket(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/feedback/urgeTicket")
            # user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            # if code != 20000:
            #     return Response(return_code(code))
            code, data = cycustomers_funcs.my_urge_ticket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))
        
    # 消息卡片重推
    @action(methods=['get', 'post'], detail=False, url_path='resendTicket')
    def cardupdate(self, request, *args, **kwargs):
        try:
            set_log.info("*" * 100)
            set_log.info("Here is api/feedback/resendTicket")
            # user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            # if code != 20000:
            #     return Response(return_code(code))
            code, data = cycustomers_funcs.my_resendTicket(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))

