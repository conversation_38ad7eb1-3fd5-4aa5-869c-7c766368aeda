import mysql.connector
import hashlib
import os
import django

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'itmp.settings')
django.setup()

from fault.models import RectifyInfo


# +-----------------+---------------+------+-----+---------+----------------+
# | Field           | Type          | Null | Key | Default | Extra          |
# +-----------------+---------------+------+-----+---------+----------------+
# | id              | int(11)       | NO   | PRI | NULL    | auto_increment |
# | faultDescribe   | varchar(256)  | YES  |     | NULL    |                |
# | rectifyDescribe | varchar(256)  | YES  |     | NULL    |                |
# | type            | varchar(256)  | YES  |     | NULL    |                |
# | fault_time      | varchar(256)  | YES  |     | NULL    |                |
# | start_time      | datetime(6)   | YES  |     | NULL    |                |
# | estimate_time   | datetime(6)   | YES  |     | NULL    |                |
# | consuming       | varchar(256)  | YES  |     | NULL    |                |
# | rate            | varchar(256)  | YES  |     | NULL    |                |
# | complete_time   | datetime(6)   | YES  |     | NULL    |                |
# | isDelay         | varchar(256)  | YES  |     | NULL    |                |
# | driver          | varchar(256)  | YES  |     | NULL    |                |
# | department      | varchar(256)  | YES  |     | NULL    |                |
# | fid_id          | varchar(256)  | NO   | MUL | NULL    |                |
# | stage           | varchar(256)  | YES  |     | NULL    |                |
# | delayDate       | datetime(6)   | YES  |     | NULL    |                |
# | fileUrl         | varchar(2048) | YES  |     | NULL    |                |
# +-----------------+---------------+------+-----+---------+----------------+
# 连接到数据库 A
db_a = mysql.connector.connect(
  host="**************",
  user="fmp",
  password="fmp123456#",
  database="faults_sys"
)

# +------------------+--------------+------+-----+---------+----------------+
# | Field            | Type         | Null | Key | Default | Extra          |
# +------------------+--------------+------+-----+---------+----------------+
# | id               | int(11)      | NO   | PRI | NULL    | auto_increment |
# | iid              | varchar(256) | YES  |     | NULL    |                | 1
# | fid              | varchar(256) | YES  |     | NULL    |                | 
# | faultDescribe    | varchar(256) | YES  |     | NULL    |                |
# | rectifyDescribe  | varchar(256) | YES  |     | NULL    |                |
# | type             | varchar(256) | YES  |     | NULL    |                |
# | faultTime        | varchar(256) | YES  |     | NULL    |                |
# | startTime        | datetime(6)  | YES  |     | NULL    |                |
# | estimateTime     | datetime(6)  | YES  |     | NULL    |                |
# | consuming        | varchar(256) | YES  |     | NULL    |                |
# | rate             | varchar(256) | YES  |     | NULL    |                |
# | completeTime     | datetime(6)  | YES  |     | NULL    |                |
# | isDelay          | varchar(256) | YES  |     | NULL    |                |
# | driver           | varchar(256) | YES  |     | NULL    |                |
# | department       | varchar(256) | YES  |     | NULL    |                |
# | stage            | varchar(256) | YES  |     | NULL    |                |
# | fileUrl          | json         | YES  |     | NULL    |                |
# | delayDate        | datetime(6)  | YES  |     | NULL    |                |
# | reportType       | varchar(256) | YES  |     | NULL    |                | 1
# | delayTime        | datetime(6)  | YES  |     | NULL    |                | 1
# | historyDelayTime | json         | YES  |     | NULL    |                | 1
# +------------------+--------------+------+-----+---------+----------------+

# 连接到数据库 B
db_b = mysql.connector.connect(
  host="**************",
  user="itmp_rw",
  password="itmp123456#",
  database="itmp_sys"
)



# oldtonew= {
#     'id': 'id', 
#     'faultDescribe': 'faultDescribe', 
#     'rectifyDescribe': 'rectifyDescribe', 
#     'type': 'type', 
#     'fault_time': 'faultTime', 
#     'start_time': 'startTime', 
#     'estimate_time': 'estimateTime', 
#     'consuming': 'consuming', 
#     'rate': 'rate', 
#     'complete_time': 'completeTime', 
#     'isDelay': 'isDelay', 
#     'driver': 'driver', 
#     'department': 'department', 
#     'fid_id': 'fid', 
#     'stage': 'stage', 
#     'delayDate': 'delayDate', 
#     'fileUrl': 'fileUrl'
#     }

q = [
"1、uploadSpecifyPhotoType接口文档加注释提醒，新增业务场景接入OBS时,需申请新的业务桶不可直接调用接口复用已有桶，并在业务开发组内部宣导。\n2、接口文档备注tipoffs桶文件生命周期15天\n文档地址：https://q9jvw0u5f5.feishu.cn/docx/Kq0RdhUTXozDJxxCsjHcHDxdnMm",
"CDN95计费带宽环比分析",
"CDN侧单位时间内对单IP下载次数限制",
"X项目业务梳理",
"chatgpt",
"cicd-public.ttyuyin.com和cicd.ttyuyin.com证书更新，代码仓库排除掉相关证书的监听配置",
"cvm-prod-tc-bj-cicd-csi-01",
"registry.npmjs.org的服务的配置中增加",
"registry.ttyuyin.com",
"registry.ttyuyin.com证书替换",
"xds下发时延指标需要跟业务方沟通进行下调",
"增加CDN带宽阈值告警通知",
"联合IT制定并落地流程（长期）",
"调整SLO指标和重跑数据",
"确认commission-server熔断配置",
"针对istiod进行扩容",
"定位sidecar两集群大小不一问题",
"增加token过期监控",
"针对token刷新原任务为一天一次，修改为30分钟一次",
"调整xds下发时间",
"扩容磁盘",
"限制登录IP访问云商控制台（只能用公司IP+VPN访问）",
"云商明确SLA质量要求",
"漏部服务检查",
"终端管控工具+流量审计",
"测试用例补全,",
"涉及过期域名（",
"增加数量的告警",
"已修改查询频次",
"重启时执行再次Mount和Export命令",
"数据平台组修复SDK重复上报问题(已修复),IOS客户端发布新版本6.47.0后验证",
"自动化部署任务task状态未改变也未触发部署BUG修复",
"修复更新备份脚本",
"培训宣贯安全意识",
"增加自动同步功能",
"与业务沟通修复方案",
"网络变更按比例切流",
"变更管控流程改进同步",
"提单修改指标重跑数据",
"服务注册重试机制优化",
"测试方案的完整性验证",
"终端安全管控方案落地",
"云商账号权限最小化原则",
"增加分钟级调度探测任务",
"应用标签标准化方案评估",
"微软云所有用户密码修改",
"监控指标梳理；增加监控",
"盘点当前存在多服务混用redis的服务/盘点生产环境使用灰度环境redis",
"制定账号权限泄漏的止损预案",
"发版调优【跟桶分布有关系】",
"关键基础组件关键基础组件的Pod数量不允许随意调整",
"修改账户密码开启手机验证登录",
"加强对变更人员的变更流程意识",
"离线调度资源使用水位异常告警",
"覆盖云商账号异常登录监控告警",
"基础设施变更的测试环境如何搭建",
"召回场景跨团队功能上线前沟通机制",
"覆盖一次创建大量资源无感知监控告警",
"按照对应服务影响范围发布标准变更流程,通知到相应的研发人员",
"需关注的监控独立到一个群，接入电话告警",
"需要关注的重要告警转到运维监控告警平台",
"演练方案的预期标准明确，对齐故障演练标准",
"完善调度服务监控，补充关键指标采集推送到prometheus",
"增加二次确认及预期效果提示，由产品评估需求",
"工单降级商务谈判（未产生费用成本不需要谈判）",
"运营告警分等级电话通知，可抑制，可配置阈值等",
"优化以下三种情况导致的耗时上报不正确的问题。\n1.",
"基础设施的故障演练的评审机制，变更机制标准对齐",
"加强对变更人员的变更流程意识，严格按照变更流程进行",
"后续重要变更需求增加二次确认及预期效果提示，由产品评估需求",
"与微软代理建立顺畅的技术支持通道（待评估）（需要服务费暂停）",
"准实时任务重点保障方案，增加基线告警支持，加强基线任务的保障，基线异常发现时间左移",
"按照对应服务影响范围采用不同等级的变更流程（影响范围大的的需要走重大变更流程）需要根据不同的变更影响范围通知到相应的研发人员",
]
cursor_a = db_a.cursor()
# 查询数据
p = []
for i in q:
    query = f"SELECT * FROM api_v2_rectifyinfo WHERE rectifyDescribe = '{i}' "
    p.append(query)
print(p) 
for j in p:   
    cursor_a.execute(j)
    result_set = cursor_a.fetchall()
    print(result_set)
    old = ['id', 'faultDescribe', 'rectifyDescribe', 'type', 'fault_time', 'start_time', 'estimate_time', 'consuming', 'rate', 'complete_time', 'isDelay', 'driver', 'department', 'fid_id', 'stage', 'delayDate', 'fileUrl']
    new = ['id', 'faultDescribe', 'rectifyDescribe', 'type', 'faultTime', 'startTime', 'estimateTime', 'consuming', 'rate', 'completeTime', 'isDelay', 'driver', 'department', 'fid', 'stage', 'delayDate', 'fileUrl']
    

    # 转化成成list
    data = []
    for row in result_set:
        item1 = dict(zip(new, row))
        item1.pop('id')
        item1['reportType'] = '故障'
        data.append(item1)

    # 写入到数据库 B

    print(data)
    for item in data:
        record = RectifyInfo(**item)
        record.save()
        record.iid = hashlib.md5(str(record.pk).encode("utf8")).hexdigest()  
        record.save()
