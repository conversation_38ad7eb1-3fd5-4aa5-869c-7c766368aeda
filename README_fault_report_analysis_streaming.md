# 故障报告分析流式接口实现

## 概述

本次实现为 `/api/dify/workflows/run/fault_report_analysis` 接口添加了流式响应支持，参考了 Dify 的流式 API 设计模式。

## 实现的功能

### 1. 流式响应支持

- 直接转发 Dify 工作流的 Server-Sent Events (SSE) 数据
- 不对响应状态码进行判断，完全透明转发
- 支持客户端主动停止分析

### 2. 双模式支持

- **流式模式 (streaming)**: 实时返回分析过程
- **阻塞模式 (blocking)**: 等待完整结果后返回

### 3. 配置化管理

- 统一的 Dify 工作流配置管理
- 支持不同工作流的独立 token 配置
- 可配置的代理和超时设置

## 文件结构

```
├── llm/
│   ├── views.py                    # 新增流式视图函数
│   ├── urls.py                     # 新增路由配置
│   └── config.py                   # 新增配置文件
├── docs/
│   └── fault_report_analysis_streaming_api.md  # API 文档
├── static/
│   └── fault_analysis_streaming_demo.html      # 前端演示页面
├── test_streaming_api.py           # 测试脚本
└── README_fault_report_analysis_streaming.md   # 本文件
```

## 主要修改

### 1. 新增视图函数 (`llm/views.py`)

```python
@csrf_exempt
def fault_report_analysis_proxy_view(request, path=""):
    """故障报告分析代理视图，支持流式响应"""
    # 支持流式和阻塞两种模式
    # 使用 StreamingHttpResponse 实现流式响应
    # 处理 Server-Sent Events 格式
```

### 2. 新增路由配置 (`llm/urls.py`)

```python
path('dify/workflows/run/fault_report_analysis/', fault_report_analysis_proxy_view, ...)
```

### 3. 新增配置管理 (`llm/config.py`)

```python
DIFY_CONFIG = {
    'workflows': {
        'fault_report_analysis': {
            'token': 'app-fault-report-analysis-token',
            'description': '故障报告分析工作流，支持流式响应'
        }
    }
}
```

## 使用方法

### 1. 配置 Token

在 `llm/config.py` 中配置实际的 Dify token：

```python
'fault_report_analysis': {
    'token': 'your-actual-dify-token',  # 替换为实际的 token
    'description': '故障报告分析工作流，支持流式响应'
}
```

### 2. 流式调用示例

```javascript
const response = await fetch('/api/dify/workflows/run/fault_report_analysis/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    Authorization: 'Bearer your-token',
  },
  body: JSON.stringify({
    inputs: {
      fault_description: '数据库连接超时导致服务不可用',
      fault_level: 'P2',
    },
    response_mode: 'streaming',
    user: 'current_user',
  }),
})

const reader = response.body.getReader()
// 处理流式数据...
```

### 3. 阻塞式调用示例

```python
import requests

response = requests.post('/api/dify/workflows/run/fault_report_analysis/', json={
    'inputs': {
        'fault_description': '数据库连接超时导致服务不可用',
        'fault_level': 'P2'
    },
    'response_mode': 'blocking',
    'user': 'current_user'
})

result = response.json()
```

## 测试

### 1. 使用测试脚本

```bash
python test_streaming_api.py
```

### 2. 使用前端演示页面

打开 `static/fault_analysis_streaming_demo.html` 在浏览器中测试。

### 3. 手动测试

```bash
curl -X POST http://localhost:8000/api/dify/workflows/run/fault_report_analysis/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "inputs": {
      "fault_description": "数据库连接超时导致服务不可用"
    },
    "response_mode": "streaming",
    "user": "test_user"
  }'
```

## 技术特点

### 1. 流式响应处理

- 使用 `StreamingHttpResponse` 实现流式响应
- 直接转发 Dify 的 Server-Sent Events 格式数据
- 不判断响应状态码，完全透明代理

### 2. 错误处理

- 完善的异常捕获和错误信息返回
- 支持客户端主动中断连接
- 超时和网络错误的优雅处理

### 3. 配置化设计

- 统一的配置管理
- 支持多工作流配置
- 易于扩展和维护

## 注意事项

1. **Token 配置**: 需要在生产环境中配置实际的 Dify token
2. **代理设置**: 根据实际网络环境调整代理配置
3. **超时设置**: 建议设置合适的超时时间（推荐 5 分钟）
4. **缓冲控制**: 在 nginx 等代理服务器中禁用缓冲以确保流式响应正常工作

## 后续优化建议

1. **监控和日志**: 添加详细的监控和日志记录
2. **限流控制**: 添加请求频率限制
3. **缓存机制**: 对相似请求添加缓存机制
4. **性能优化**: 优化流式数据处理性能
5. **安全加固**: 添加更多的安全验证和防护措施

## 兼容性

- Django 3.1+
- Python 3.7+
- 支持现代浏览器的 Server-Sent Events
- 兼容现有的认证和权限系统
