from sqlite3 import IntegrityError
import mysql.connector
import hashlib
import os
import django

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'itmp.settings')
django.setup()

from fault.models import FaultInfo
from fault.models import PlaybackInfo
from fault.models import RectifyInfo

#OLD api_v2_faultinfo  api_v2_playbackinfo
#NEW fault_info playback_info


# 连接到数据库 A
db_a = mysql.connector.connect(
  host="**************",
  user="fmp",
  password="fmp123456#",
  database="faults_sys"
)



# 连接到数据库 B
db_b = mysql.connector.connect(
  host="**************",
  user="itmp_rw",
  password="itmp123456#",
  database="itmp_sys"
)

# 查询数据B
# query = "SELECT * FROM fault_info WHERE startTime >= '2024-03-06'"
# cursor_b = db_b.cursor()
# cursor_b.execute(query)
# result_set_b = cursor_b.fetchall()
# print(result_set_b)

# 查询数据A
query = "SELECT * FROM api_v2_faultinfo WHERE fid = '202403270001'"
cursor_a = db_a.cursor()
cursor_a.execute(query)
result_set = cursor_a.fetchall()
print(result_set)

# 
old = ['id', 'fid', 'fault_describe', 'meeting_minutes', 'current_progress', 'fault_year', 'fault_month', 'fault_quarter', 'start_time', 'discovery_time', 'discovery_consuming', 'response_time', 'response_consuming', 'loacte_time', 'loacte_consuming', 'recover_time', 'recover_consuming', 'duration', 'playback', 'cause_classify', 'is_change', 'infrastructure', 'other_reason', 'cause_describe', 'service_classification', 'is_effect_produce', 'direct_effect_revenue', 'is_grade', 'level', 'effect_content', 'is_effect_outside', 'has_watch', 'has_alert', 'is_firstlaunch', 'firstlaunch_source', 'feedback_source', 'cause_locator', 'causelocate_tool', 'improve_action', 'handler', 'driver', 'recorder', 'simple_report_time', 'simple_report_delay', 'preparation_time', 'replay_time', 'replay_delay', 'finish_file_time', 'finish_file_overtime', 'recovery_plan', 'creator', 'architecture_diagram', 'stage', 'troubleshooting', 'creator_id', 'isoperation', 'problem', 'budgetSLA', 'declineSLA', 'groupLink', 'hasSLA', 'isEffectSLA', 'participants', 'effectSLO', 'isDevLocate', 'isOpsLocate', 'reportType', 'isReplay', 'slaDetail']
new = ['id', 'fid', 'faultDescribe', 'meetingMinutes', 'currentProgress', 'faultYear', 'faultMonth', 'faultQuarter', 'startTime', 'discoveryTime', 'discoveryConsuming', 'responseTime', 'responseConsuming', 'loacteTime', 'loacteConsuming', 'recoverTime', 'recoverConsuming', 'duration', 'playback', 'causeClassify', 'isChange', 'infrastructure', 'otherReason', 'causeDescribe', 'serviceClassification', 'isEffectProduce', 'directEffectRevenue', 'isGrade', 'level', 'effectContent', 'isEffectOutside', 'hasWatch', 'hasAlert', 'isFirstLaunch', 'firstLaunchSource', 'feedbackSource', 'causeLocator', 'isDevLocate', 'isOpsLocate', 'causeLocateTool', 'improveAction', 'handler', 'driver', 'recorder', 'simpleReportTime', 'simpleReportDelay', 'preparationTime', 'replayTime', 'replayDelay', 'finishFileTime', 'finishFileDelay', 'recoveryPlan', 'creator', 'creatorID', 'stage', 'reportType', 'architectureDiagram', 'troubleshooting', 'isOperation', 'problem', 'groupLink', 'hasSLA', 'isEffectSLA', 'declineSLA', 'slaDetail', 'effectSLO', 'budgetSLA', 'isReplay', 'participants', 'improvementFinishTime']

item={
  'id': 'id', 
  'fid': 'fid', 
  'fault_describe': 'faultDescribe', 
  'meeting_minutes': 'meetingMinutes', 
  'current_progress': 'currentProgress', 
  'fault_year': 'faultYear', 
  'fault_month': 'faultMonth', 
  'fault_quarter': 'faultQuarter', 
  'start_time': 'startTime', 
  'discovery_time': 'discoveryTime', 
  'discovery_consuming': 'discoveryConsuming', 
  'response_time': 'responseTime', 
  'response_consuming': 'responseConsuming', 
  'loacte_time': 'loacteTime', 
  'loacte_consuming': 'loacteConsuming', 
  'recover_time': 'recoverTime', 
  'recover_consuming': 'recoverConsuming', 
  'duration': 'duration', 
  'playback': 'playback', 
  'cause_classify': 'causeClassify', 
  'is_change': 'isChange', 
  'infrastructure': 'infrastructure', 
  'other_reason': 'otherReason', 
  'cause_describe': 'causeDescribe', 
  'service_classification': 'serviceClassification', 
  'is_effect_produce': 'isEffectProduce', 
  'direct_effect_revenue': 'directEffectRevenue', 
  'is_grade': 'isGrade', 
  'level': 'level', 
  'effect_content': 'effectContent', 
  'is_effect_outside': 'isEffectOutside', 
  'has_watch': 'hasWatch', 
  'has_alert': 'hasAlert', 
  'is_firstlaunch': 'isFirstLaunch', 
  'firstlaunch_source': 'firstLaunchSource', 
  'feedback_source': 'feedbackSource', 
  'cause_locator': 'causeLocator', 
  'causelocate_tool': 'causeLocateTool',
  'improve_action': 'improveAction',
  'handler': 'handler', 
  'driver': 'driver', 
  'recorder': 'recorder', 
  'simple_report_time': 'simpleReportTime', 
  'simple_report_delay': 'simpleReportDelay', 
  'preparation_time': 'preparationTime', 
  'replay_time': 'replayTime', 
  'replay_delay': 'replayDelay', 
  'finish_file_time': 'finishFileTime', 
  'finish_file_overtime': 'finishFileDelay', 
  'recovery_plan': 'recoveryPlan', 
  'creator': 'creator',
  'architecture_diagram': 'architectureDiagram', 
  'stage': 'stage', 
  'troubleshooting': 'troubleshooting',
  'creator_id': 'creatorID', 
  'isoperation': 'isOperation',
  'problem': 'problem', 
  'budgetSLA': 'budgetSLA', 
  'declineSLA': 'declineSLA',
  'groupLink': 'groupLink', 
  'hasSLA': 'hasSLA', 
  'isEffectSLA': 'isEffectSLA', 
  'participants': 'participants', 
  'effectSLO': 'effectSLO', 
  'isDevLocate': 'isDevLocate', 
  'isOpsLocate': 'isOpsLocate', 
  'reportType': 'reportType', 
  'isReplay': 'isReplay', 
  'slaDetail': 'slaDetail',
  '0': 'improvementFinishTime'}

new_list = [item.get(key, key) for key in old]


# 转化成成list
data = []
for row in result_set:
    item1 = dict(zip(new_list, row))
    item1.pop('id')
    item1['reportType'] = '故障'
    data.append(item1)
print(data)    
for i in data:
  i['participants'] = eval(i['participants'])
  i['isEffectProduce'] = eval(i['isEffectProduce'])
  item_to_update = FaultInfo.objects.filter(fid=i['fid']).first()
  if item_to_update:
    # 执行模糊查询
    last_fid = FaultInfo.objects.filter(fid__contains=i['fid'][:8]).order_by('-id').first().fid
    item_to_update.fid  = str(int(last_fid) + 1)
    item_to_update.save()
    # 修改重
    if PlaybackInfo.objects.filter(fid=i['fid']).first():
        PlaybackInfo.objects.filter(fid=i['fid']).update(fid=item_to_update.fid)
    if RectifyInfo.objects.filter(fid=i['fid']).first():
        RectifyInfo.objects.filter(fid=i['fid']).update(fid=item_to_update.fid)    





# item_to_update = FaultInfo.objects.filter(fid='202403050006').first()
# if item_to_update:
#     item_to_update.fid = '202403050026'  # 将fid字段修改为'yy'
#     item_to_update.save()  # 保存修改
# item_to_update = FaultInfo.objects.filter(fid='202403020001').first()
# if item_to_update:
#     item_to_update.fid = '202403020028'  # 将fid字段修改为'yy'
#     item_to_update.save()  # 保存修改    
# item_to_update = FaultInfo.objects.filter(fid='202403120001').first()
# if item_to_update:
#     item_to_update.fid = '202403120021'  # 将fid字段修改为'yy'
#     item_to_update.save()  # 保存修改       
# item_to_update = FaultInfo.objects.filter(fid='202403050001').first()
# if item_to_update:
#     item_to_update.fid = '202403050005'  # 将fid字段修改为'yy'
#     item_to_update.save()  # 保存修改
# item_to_update = FaultInfo.objects.filter(fid='202403150001').first()
# if item_to_update:
#     item_to_update.fid = '202403150031'  # 将fid字段修改为'yy'
#     item_to_update.save()  # 保存修改
# 写入到数据库 B


try:
  for item in data:
      record = FaultInfo(**item)
      record.save()
except IntegrityError:
  print(IntegrityError)
