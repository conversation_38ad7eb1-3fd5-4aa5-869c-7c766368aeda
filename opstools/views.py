
from django.db.models import Q

# Create your views here.
from rest_framework.response import Response
from rest_framework.decorators import api_view
import subprocess
from apscheduler.events import EVENT_JOB_ERROR, EVENT_JOB_EXECUTED
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from datetime import datetime, timedelta
from framework.models import PersonsInitInfo
from user_auth.models import MyUserInfo
from common.utils.utils import NewViewBase, return_code, get_user
from common.utils.log import Logger
from common.utils.config import itmp_conf
from opstools.serializers import rtmpSerializers
import hashlib

set_log = Logger.get_logger(__name__, 3)

itmp_url = itmp_conf["itmp_url"]
itmp_rtmp_host = itmp_conf["itmp_rtmp_host"]

# Mysql配置
MYSQL_USER = itmp_conf["databases_user"]
MYSQL_PASSWORD = itmp_conf["databases_pswd"]
MYSQL_HOST = itmp_conf["databases_host"]
MYSQL_PORT = itmp_conf["databases_port"]
MYSQL_DATABASE = itmp_conf["databases_name"]

# 定时任务数据库配置
mydb = {
    'host': MYSQL_HOST,
    'user': MYSQL_USER,
    'password': MYSQL_PASSWORD,
    'database': MYSQL_DATABASE,
    'port': MYSQL_PORT
}

# 定时任务存储配置
job_stores = {
    "default": {
        'type': 'sqlalchemy',
        'url': 'mysql+mysqlconnector://{}:{}@{}:{}/{}'.format(MYSQL_USER, MYSQL_PASSWORD, MYSQL_HOST, MYSQL_PORT, MYSQL_DATABASE),
        'tablename': 'rtmp_jobs',
        'engine_options': {'pool_pre_ping': True, 'pool_recycle': 10800}  # 每七小时检查一次连接
    }
}


def event_listener(event):
    # 任务监听器
    if event.exception:
        set_log.error("[EVENT_LISTENER] {}JOB Execution FAILED!!! Because {}".format(event.job_id, event.exception))
    else:
        set_log.info("[EVENT_LISTENER] {}JOB Execution SUCCEEDED".format(event.job_id))


scheduler = BackgroundScheduler(jobstores=job_stores, timezone='Asia/Shanghai')
scheduler.add_listener(event_listener, EVENT_JOB_ERROR | EVENT_JOB_EXECUTED)

# 启动定时任务框架
scheduler.start()

# Create your views here.
# 开启ffmpeg 转 flv

def ffmpegstart(rtmp_url, container_name):
    set_log.info("**** ffmpegstart ****")
    docker_command = f'''docker run --rm -d --name {container_name} registry.cn-hangzhou.aliyuncs.com/ossrs/srs:encoder   ffmpeg -stream_loop -1 -re -i "{rtmp_url}" -c copy     -f flv rtmp://{itmp_rtmp_host}/live/{container_name}'''
    set_log.info("docker_command: " + docker_command)
    completed_process = subprocess.run(docker_command, shell=True)

    if completed_process.returncode == 0:
        set_log.info("docker 命令执行成功")
        return 0
    else:
        set_log.info("docker 命令执行失败")
        return -1

# 关闭ffmpeg 转 flv
def ffmpegstop(container_name):
    set_log.info("**** ffmpegstop ****")
    docker_command = f'''docker stop {container_name}'''
    set_log.info("docker_command: " + docker_command)
    completed_process = subprocess.run(docker_command, shell=True)
    if completed_process.returncode == 0:
        set_log.info("docker 命令执行成功")
        return 0
    else:
        set_log.error("docker 命令执行失败")
        return -1

def getffmpeg(container_name):
    set_log.info("**** getffmpeg ****")
    docker_command = f'''docker ps  --filter "name={container_name}" |grep {container_name} '''
    set_log.info("docker_command: " + docker_command)
    completed_process = subprocess.run(docker_command, shell=True)
    if completed_process.returncode == 0:
        set_log.info("docker 查找成功")
        return 0
    else:
        set_log.error("docker 查找失败")
        return -1

      
# 推送rtmp转flv
@api_view(['POST'])  
def pushrtmp(request):
    try:
        set_log.info("*" * 100)
        set_log.debug("Here is pushrtmp")
        frontend_data = request.data['data']
        rtmp_url = frontend_data['query']['url']
        hash_value = hashlib.md5(rtmp_url.encode()).hexdigest()
        container_name = str(hash_value)
        # 判断推流是否已启动
        stat_code = getffmpeg(container_name)
        data={'url': f'{itmp_url}/live/'+ str(container_name)+'.flv', 'id': str(container_name)}
        if stat_code == 0:
            # 推流已启动
            set_log.info("PushRtmpViewset.ffmpegstart")
            return Response(return_code(20000, data=data, interface="opstools/pushrtmp"))
        # 计算当前时间加上24小时后的时间，后执行删除
        trigger_time = datetime.now() + timedelta(hours=24)
        scheduler.add_job(func=ffmpegstop, trigger='date', run_date=trigger_time, args=[container_name])    
        stat_code = ffmpegstart(rtmp_url,container_name)
        if stat_code == 0:
            return Response(return_code(20000, data=data, interface="opstools/pushrtmp"))
        else:
            return Response(return_code(-1, "推流失败", interface="opstools/pushrtmp"))
    except Exception as e:
        set_log.info(f"[opstools/pushrtmp FALSE]")
        set_log.error(f"{e}")
        return Response(return_code(-1, e, interface="opstools/pushrtmp"))