from django.shortcuts import render
from .serializers import *
from .models import *
from .funcs import problem_funcs, my_authentication
from rest_framework.permissions import AllowAny
from rest_framework.decorators import action
from rest_framework.response import Response

from common.utils.utils import NewViewBase, return_code, get_user
from common.utils.log import Logger

# Create your views here.
set_log = Logger.get_logger(__name__, 2)


class ProblemViewset(NewViewBase):
    """问题信息增删改查"""
    queryset = ProblemInfo.objects.all()
    serializer_class = ProblemSerializers
    permission_classes = [AllowAny]

    # 查询全问题
    @action(methods=['get', 'post'], detail=False, url_path='test')
    def sel_all(self, request, *args, **kwargs):
        try:
            print("*" * 100)
            set_log.debug("Here is problem/test")
            problem = ProblemSerializers(ProblemInfo.objects.all(), many=True).data
            set_log.debug(f"[return] {type(problem)}")
            set_log.info(f"[problem/test SUCCESS]")
            return Response(return_code(0, data=problem, count=len(problem)))
        except Exception as e:
            set_log.error(f"[ERROR] {e}")
            return Response(return_code(-1, data=e))

    # 自定义查询接口
    @action(methods=['get', 'post'], detail=False, url_path='selectProblem')
    def selectProblem(self, request, *args, **kwargs):
        try:
            print("*" * 100)
            set_log.debug("Here is problem/selectProblem")

            code, problem = problem_funcs.my_selectProblem(request)

            if code == 20000 or code == 0:
                set_log.info(f"[selectProblem SUCCESS]")
            else:
                set_log.info(f"[selectProblem FALSE]")
            return Response(return_code(code, data=problem, count=len(problem)))
        except Exception as e:
            set_log.error(f"[ERROR] {e}")
            return Response(return_code(-1, data=e))

    # dashboard
    @action(methods=['get', 'post'], detail=False, url_path='getDashboardData')
    def dashboard_sel(self, request, *args, **kwargs):
        operator = get_user(request)['user_name']
        try:
            set_log.info("*" * 100)
            set_log.debug("Here is api/problem/dashboard")
            # 鉴权
            user,code = my_authentication.MyTokenAuthentication_by_hand(request)
            if code != 20000:
                return Response(return_code(code))
            data = problem_funcs.get_dashboard(request)
            # 拦截错误码
            if isinstance(data, int):
                set_log.error(f"异常状态码：{data}")
                set_log.info(f"[dashboard FALSE]")
                return Response(return_code(data, data=[], status='error', operator=operator))
            set_log.info(f"[dashboard SUCCESS]")
            return Response(return_code(20000, data=data, status='ok'))
        except Exception as e:
            set_log.info(f"[dashboard FALSE]")
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=str(e), operator=operator))
        
    # 创建接口
    @action(methods=['get', 'post'], detail=False, url_path='createProblem')
    def createProblem(self, request, *args, **kwargs):
        try:
            print("*" * 100)
            set_log.debug("Here is api/problem/createProblem")

            code, data = problem_funcs.my_createProblem(request)

            if code == 20000 or code == 0:
                set_log.info(f"[createProblem SUCCESS]")
            else:
                set_log.info(f"[createProblem FALSE]")
            return Response(return_code(code, data=data))
        except Exception as e:
            set_log.error(f"[ERROR] {e}")
            return Response(return_code(-1, data=e))

    # 上传图片
    @action(methods=['get', 'post'], detail=False, url_path='upload')
    def uploadPhotp(self, request, *args, **kwargs):
        try:
            print("*" * 100)
            set_log.info("Here is api/problem/upload")
            code, data = problem_funcs.my_uploadPhotp(request)
            return Response(return_code(code, data=data, status='ok'))
        except Exception as e:
            set_log.error(f"[error] {e}")
            return Response(return_code(-1, data=e, status='error'))


    # 编辑接口
    @action(methods=['get', 'post'], detail=False, url_path='editProblem')
    def editProblem(self, request, *args, **kwargs):
        try:
            print("*" * 100)
            set_log.debug("Here is problem/editProblem")

            code, problem = problem_funcs.my_editProblem(request)

            if code == 20000 or code == 0:
                set_log.info(f"[editProblem SUCCESS]")
            else:
                set_log.info(f"[editProblem FALSE]")
            return Response(return_code(code, data=problem))
        except Exception as e:
            set_log.error(f"[ERROR] {e}")
            return Response(return_code(-1, data=e))

    # 伪删除接口
    @action(methods=['get', 'post'], detail=False, url_path='deleteProblem')
    def deleteProblem(self, request, *args, **kwargs):
        try:
            print("*" * 100)
            set_log.debug("Here is problem/deleteProblem")

            code, data = problem_funcs.my_deleteProblem(request)

            if code == 20000 or code == 0:
                set_log.info(f"[deleteProblem SUCCESS]")
            else:
                set_log.info(f"[deleteProblem FALSE]")
            return Response(return_code(code, data=data))
        except Exception as e:
            set_log.error(f"[ERROR] {e}")
            return Response(return_code(-1, data=e))

    # 真删除接口
    @action(methods=['get', 'post'], detail=False, url_path='realdeleteProblem')
    def realdeleteProblem(self, request, *args, **kwargs):
        try:
            print("*" * 100)
            set_log.debug("Here is problem/realdeleteProblem")

            code, data = problem_funcs.my_realdeleteProblem(request)


            if code == 20000 or code == 0:
                set_log.info(f"[realdeleteProblem SUCCESS]")
            else:
                set_log.info(f"[realdeleteProblem FALSE]")
            return Response(return_code(code, data=data))
        except Exception as e:
            set_log.error(f"[ERROR] {e}")
            return Response(return_code(-1, data=e))

    # 升级为故障接口
    @action(methods=['get', 'post'], detail=False, url_path='toFault')
    def toFault(self, request, *args, **kwargs):
        try:
            print("*" * 100)
            set_log.debug("Here is problem/toFault")

            code, data = problem_funcs.my_toFault(request)


            set_log.info(f"[problem/toFault SUCCESS]")
            return Response(return_code(code, data=data))
        except Exception as e:
            set_log.error(f"[ERROR] {e}")
            return Response(return_code(-1, data=e))







