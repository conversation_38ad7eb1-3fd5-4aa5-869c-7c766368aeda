import copy
import json
import time

import requests
from django.db.models import Q
from rest_framework_jwt.utils import jwt_decode_handler


from ..models import *
from ..serializers import *
from common.utils.log import Logger
from common.utils.config import itmp_conf
from common.utils.utils import get_user

from user_auth.models import MyUserInfo
from user_auth.serializers import MyUserInfoSerializers
from fault.models import FaultInfo
from fault.serializers import FaultSerializers

set_log = Logger.get_logger(__name__, 2)


def my_selectProblem(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_selectProblem")
        set_log.debug(f"[request method] {request.method}")
        if request.method == 'GET':
            set_log.debug(f"[request method] --- GET ---")
            frontend_data = request.GET

        elif request.method == 'POST':
            set_log.debug(f"[request method] --- POST ---")
            frontend_data = request.data['data']
        else:
            return -1, '方法不允许'
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        page = int(frontend_data.get('page', 1))
        limit = int(frontend_data.get('limit', 10))
        query = frontend_data.get('query', {})
        if query.get('reportType', False):
            reportType = query.pop('reportType')

        # 分页参数
        set_log.debug(f"分页参数：page - {page}, limit - {limit}")

        # 获取当前用户
        # user = get_user(request)

        search = dict()
        search_by_hand = Q()
        # 可多选参数
        json_field = ["fid", "causeClassify", "isEffectOutside", "creator", "creatorID", "stage", "hasSLA", "slaDetail", "effectSLO", "isReplay"]

        for i, j in query.items():
            set_log.debug(f"[one search] {i} {j} {type(j)}")
            # 精确搜索
            if j == []:
                pass
            elif j == '':
                pass
            else:
                set_log.info(f"[Valid parameters]")
                # 模糊搜索
                if i == 'search':
                    if j == '':
                        continue
                    # Q(id__icontains=j) |
                    search_by_hand = Q(fid__icontains=j) | Q(problemDescribe__icontains=j) | \
                                     Q(startTime__icontains=j) | Q(recoverTime__icontains=j) | Q(causeClassify__icontains=j) | \
                                     Q(causeDescribe__icontains=j) | Q(effectContent__icontains=j) | Q(creator__icontains=j) | \
                                     Q(stage__icontains=j) | Q(slaDetail__icontains=j) | Q(effectSLO__icontains=j)
                elif i == 'problemDescribe':
                    search['problemDescribe__icontains'] = j

                # 多选参数
                elif i in json_field:
                    search_by_hand.connector = 'OR'
                    for k in j:
                        search_by_hand.children.append((f"{i}__icontains", k))
                # 时间查询
                elif i == 'enterTime':
                    # [i for i in j]
                    search['enterTime__range'] = j
                else:
                    search[f'{i}__in'] = j
        set_log.debug(f"查询参数：{search} {search_by_hand} {len(search_by_hand)}")

        func_start_time = time.time()

        # 无参数
        if len(search) == 0 and len(search_by_hand) == 0:
            # set_log.debug(f"all")
            # func_start_time = time.time()
            data = ProblemInfo.objects.all().order_by('-id')
            total = data.count()
            data = ProblemSerializers(data[page*limit-limit:page*limit], many=True).data

        elif len(search) > 0 and len(search_by_hand) == 0:
            set_log.debug(f"[search] {search}")
            data = ProblemInfo.objects.filter(**search).order_by('-id')
            set_log.debug(f"[search data] {data}")
            total = data.count()
            data = ProblemSerializers(data[page*limit-limit:page*limit], many=True).data
            set_log.debug(f"[search data json] {data}")
        else:
            # search_by_hand.children.append(filter_Q)
            set_log.debug(f"[search_by_hand] {search_by_hand}")
            data = ProblemInfo.objects.filter(**search).filter(search_by_hand).order_by('-id')
            set_log.debug(f"[search_by_hand data] {data}")
            total = data.count()
            data = ProblemSerializers(data[page*limit-limit:page*limit], many=True).data
            set_log.debug(f"[search_by_hand data json] {data}")
        # total = len(alerts)

        set_log.info(f"[sel data] SUCCESS")
        func_end_time = time.time()
        set_log.info(f"[查询 耗时] {func_end_time - func_start_time:.2f} seconds")
        # 时间格式转换
        set_log.debug(f"分页后数据长度：{len(data)}")

        # 查询为空
        if data == []:
            set_log.error(f"查询结果为空：{data}")
            return 20000, []
        return 20000, data




        # problem = ProblemInfo.objects.filter(**query)
        # set_log.debug(f"[problem] {type(problem)} {problem}")
        # total_size = len(problem)
        # if limit == 1 and total_size == 1:
        #     total_page = 1
        # else:
        #     total_page = total_size//limit
        # # 分页
        # problem = ProblemSerializers(problem[], many=True).data





    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

# dashboard data 
def get_dashboard(request):
    try:
        print("-" * 40)
        set_log.info("Here is get_dashboard")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        query = frontend_data.get('query', {})
        search = {'stage': '已发布', 'currentProgress__in': ['已归档', '未归档']}
        json_field = ["reportType"]
        one_field = ["driver", "isReplay", "isEffectOutside", "causeClassify", "isDevLocate", "isOpsLocate"]

        search_json_icontains = []

        for i, j in query.items():
            set_log.debug(f"[one search] {i} {j} {type(j)}")
            # 
            if j == []:
                pass
            elif j == '':
                pass
                # 多选模糊匹配参数
            elif i in json_field:
                # search_by_hand.connector = 'AND'
                search_json_icontains.append(Q())
                for k in j:
                    # search_by_hand.connector = 'OR'
                    # search_by_hand.children.append((f"{i}__icontains", k))
                    search_json_icontains[-1].connector = 'OR'
                    search_json_icontains[-1].children.append((f"{i}__icontains", k))
            # 时间查询
            elif i == 'finishRateTime' and j:
                # [i for i in j]
                search['improvementFinishTime__range'] = j
            # 单选        
            elif i in one_field:
                search[f'{i}__icontains'] = j
        set_log.debug(f"查询参数：{search} {search_json_icontains}")
        data = FaultInfo.objects.all()
        if len(search) != 0:
            data = data.filter(**search)
        if len(search_json_icontains) != 0:
            for i in search_json_icontains:
                data = data.filter(i)
        data = data.order_by('-startTime')
        unFinishCount= data.filter(currentProgress='未归档').count()
        finishCount= data.filter(currentProgress='已归档').count()
        total = data.count()
        set_log.debug(f"[search data] {data}")
        data = FaultSerializers(data, many=True).data
        set_log.debug(f"[search data json len] {len(data)}")
        opslocate = 0
        if total == 0:
            opslocateRate = 0
        else:    
            for i in data:
                if i['isOpsLocate'] == '是':
                # if i['causeLocator'] == '运维定位':
                    opslocate += 1
            set_log.debug(f"[opslocate] {opslocate} {total}")        
            opslocateRate = round(opslocate / total * 100, 1)
        # 开始计算dashboard
        dashboard = {
            # 问题未归档数
            'unFinishCount': unFinishCount,
            # 问题已归档数
            'finishCount': finishCount,
            # 问题总数
            'finishRateCount': total,
            # 问题闭环率
            'finishRate': round(finishCount/total*100, 1) if total != 0 else 0,
            # 运维定位率
            'opslocateRate': opslocateRate,
        }
        set_log.info(f"[dashboard success] {dashboard}")
        return dashboard
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_createProblem(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_createProblem")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        # 生成新fid
        last_fid = ProblemInfo.objects.all().order_by('-id')[0].fid
        set_log.debug(f"[last FID] {type(last_fid)} {last_fid}")
        today = time.strftime('%Y%m%d', time.localtime(time.time()))
        set_log.debug(f"[日期比较]  {last_fid[:8]} {today}")
        if last_fid[:8] == today:
            set_log.debug(f"SAME DAY")
            fid = str(int(last_fid) + 1)
        else:
            set_log.debug(f"[NEW DAY]")
            fid = f"{today}0001"
        set_log.debug(f"[NEW FID] {fid}")
        frontend_data['fid'] = fid
        # 创建人
        user = get_user(request)
        frontend_data['creator'] = user['user_name']
        frontend_data['creatorID'] = user['TT_id']
        # 类型
        frontend_data['reportType'] = '问题'
        frontend_data['stage'] = '已发布'

        try:
            problem = ProblemInfo(**frontend_data)
            problem.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            set_log.info(f"[CREATE PROBLEM FALSE]")
            return 1003, e
        set_log.info(f"[CREATE PROBLEM SUCCESS]")
        return 20000, frontend_data
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_uploadPhotp(request):
    from obs import ObsClient, HeadPermission  # 用于连接华为云OBS的库
    print("-" * 50)
    set_log.debug("Here is my_uploadPhotp")
    # cookie = myget_cookie(request)
    # operator = get_user(request)
    if not request.FILES.get('file', False):
        return 1010, '图片上传失败,未获取到上传图片'
    img = request.FILES.get('file').read()
    set_log.debug(f"frontend files {request.FILES}")
    data = request.data
    set_log.debug(f"[frontend] {request.data}")
    obsClient = ObsClient(
        # access_key_id='PTIF140E2NIGOIXBXWMN',
        # secret_access_key='1i05SIkeIBb7Ms44xulQwC2Du8YryV2IGm9CZKws',
        # server='obs.cn-north-4.myhuaweicloud.com'
        access_key_id=itmp_conf['access_key_id'],  # 你的华为云的ak码
        secret_access_key=itmp_conf['secret_access_key'],  # 你的华为云的sk
        server=itmp_conf['server']  # 你的桶的地址
    )
    # bucketname = 'obs-test-hw-gz-yw-fmp-backend'
    bucketname = itmp_conf['bucketname']
    set_log.debug(f"bucket: {itmp_conf['bucketname'], itmp_conf['server']}")

    # uid = request.FILES.get('file')
    # set_log.debug(f"uid: {uid}")
    # 同名会覆盖
    title = request.FILES.get('file').name
    set_log.debug(f"[img name] {title}")
    uid = data.get('uid', 'error_id')

    # img = base64.b64decode(img)
    set_log.debug(f"img: {type(img)},uid: {uid}")
    # 上传图片
    res = obsClient.putContent(bucketname, uid, content=img)
    set_log.debug(f"obs res: {res}")

    if res.status < 300:
        url = res.body.objectUrl
        # 开权限
        obsClient.setObjectAcl(bucketname, uid, aclControl=HeadPermission.PUBLIC_READ)

        return_data = {
            "uid": uid,
            "url": url,
        }
        return 20000, return_data
    else:
        set_log.error(f"errorCode: {res.errorCode}")
        set_log.error(f"errorMessage: {res.errorMessage}")
        # return Response(return_code(-99, data='调试中', msg='调试中', operator=operator))
        # return Response(return_code(-2, data='图片上传失败', msg='图片上传失败', operator=operator))
        # 触发告警
        # return 1010, data='图片上传失败', msg='图片上传失败', operator=operator)
        return 1010, "图片上传失败"

def my_editProblem(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_editProblem")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("fid", False):
                return 1009, "缺少fid"
            ProblemInfo.objects.filter(fid=frontend_data['fid']).update(**frontend_data)
        except Exception as e:
            set_log.error(f"error: {e}")
            set_log.info(f"[EDIT PROBLEM FALSE]")
            return 1003, e
        set_log.info(f"[EDIT PROBLEM SUCCESS]")
        return 20000, frontend_data

    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_deleteProblem(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_deleteProblem")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("fid", False):
                return 1009, "缺少fid"
            problem = ProblemInfo.objects.get(fid=frontend_data['fid'])
            problem.stage = "已删除"
            problem.save()
        except Exception as e:
            set_log.error(f"error: {e}")
            set_log.info(f"[DELETE PROBLEM FALSE]")
            return 1003, e
        set_log.info(f"[DELETE PROBLEM SUCCESS]")
        return 20000, frontend_data

    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

def my_realdeleteProblem(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_realdeleteProblem")
        frontend_data = request.data['data']
        set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
        try:
            if not frontend_data.get("fid", False):
                return 1009, "缺少fid"
            problem = ProblemInfo.objects.get(fid=frontend_data['fid'])
            problem.delete()
        except Exception as e:
            set_log.error(f"error: {e}")
            set_log.info(f"[DELETE PROBLEM FALSE]")
            return 1003, e
        set_log.info(f"[DELETE PROBLEM SUCCESS]")
        return 20000, frontend_data

    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)

# 升级为故障
def my_toFault(request):
    try:
        print("-" * 40)
        set_log.info("Here is my_toFault")
    except Exception as e:
        set_log.error(f"error: {e}")
        return -1, str(e)




