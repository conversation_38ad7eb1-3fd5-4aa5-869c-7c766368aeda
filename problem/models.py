from django.db import models


# Create your models here.

class ProblemInfo(models.Model):

    id = models.AutoField(primary_key=True)
    # blank=True  允许表单为空  null=True 允许数据库值为空
    # fid = models.IntegerField(null=True)
    fid = models.CharField(max_length=256, db_column="fid", blank=True, null=True, verbose_name="fid", unique=True)
    # 故障描述
    problemDescribe = models.CharField(max_length=256, blank=True, null=True, db_column="fault_describe",
                                     verbose_name="故障描述")
    # 故障开始时间
    startTime = models.DateTimeField(blank=True, null=True, db_column="start_time", verbose_name="故障开始时间")

    # 恢复时间
    recoverTime = models.DateTimeField(blank=True, null=True, db_column="recover_time", verbose_name="恢复时间")
    # 持续时长
    duration = models.IntegerField(blank=True, null=True, db_column="duration", verbose_name="持续时长")

    # 根因归类
    causeClassify = models.CharField(max_length=256, blank=True, null=True, db_column="cause_classify",
                                     verbose_name="根因归类")

    # 根因描述
    causeDescribe = models.TextField(blank=True, null=True, db_column="cause_describe", verbose_name="根因描述")

    # 影响内容
    effectContent = models.TextField(blank=True, null=True, db_column="effect_content", verbose_name="影响内容")
    # 是否影响对外业务
    isEffectOutside = models.CharField(max_length=256, blank=True, null=True, db_column="is_effect_outside",
                                       verbose_name="是否影响对外业务")

    # 创建者
    creator = models.CharField(max_length=256, blank=True, null=True, db_column="creator", verbose_name="创建者")
    creatorID = models.CharField(max_length=256, blank=True, null=True, db_column="creator_id", verbose_name="创建者ID")
    # 状态
    stage = models.CharField(max_length=256, blank=True, null=True, db_column="stage", verbose_name="状态")

    # 报告类型 故障报告/需复盘问题/问题
    reportType = models.CharField(max_length=256, blank=True, null=True, db_column="reportType", verbose_name="报告类型")


    # 是否有SLA
    hasSLA = models.CharField(max_length=256, blank=True, null=True, db_column="hasSLA", verbose_name="是否有SLA协议")


    # 关联sla
    slaDetail = models.JSONField(blank=True, null=True, db_column="slaDetail", verbose_name="关联sla", default=[])

    # 关联SLO
    effectSLO = models.JSONField(blank=True, null=True, db_column="effectSLO", verbose_name="关联SLO", default=[])

    # 是否需要复盘
    isReplay = models.CharField(max_length=256, blank=True, null=True, db_column="isReplay", verbose_name="是否需要复盘")


    objects = models.Manager()

    class Meta:
        app_label = "problem"
        db_table = 'problems'

    def __str__(self):
        return self.fid