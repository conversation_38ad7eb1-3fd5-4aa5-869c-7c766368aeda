# 故障报告分析流式 API 文档

## 概述

故障报告分析 API 支持流式响应，可以实时返回分析结果，提供更好的用户体验。该接口基于 Dify 工作流，支持两种响应模式：

- **流式响应 (streaming)**: 实时返回分析过程和结果
- **阻塞式响应 (blocking)**: 等待完整分析完成后返回结果

## 接口信息

- **URL**: `/api/dify/workflows/run/fault_report_analysis/`
- **方法**: `POST`
- **认证**: 需要有效的认证 token

## 请求格式

### 请求头

```http
Content-Type: application/json
Authorization: Bearer <your-token>
```

### 请求体

```json
{
  "inputs": {
    "fault_description": "故障描述",
    "fault_time": "故障发生时间",
    "fault_level": "故障等级",
    "affected_services": ["受影响的服务列表"]
  },
  "response_mode": "streaming", // "streaming" 或 "blocking"
  "user": "用户标识"
}
```

#### 参数说明

| 参数                     | 类型   | 必填 | 说明                        |
| ------------------------ | ------ | ---- | --------------------------- |
| inputs.fault_description | string | 是   | 故障描述信息                |
| inputs.fault_time        | string | 否   | 故障发生时间                |
| inputs.fault_level       | string | 否   | 故障等级 (P1-P5)            |
| inputs.affected_services | array  | 否   | 受影响的服务列表            |
| response_mode            | string | 否   | 响应模式，默认为 "blocking" |
| user                     | string | 是   | 用户标识                    |

## 响应格式

### 流式响应 (response_mode: "streaming")

流式响应使用 Server-Sent Events (SSE) 格式，Content-Type 为 `text/event-stream`。

#### 响应头

```http
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive
X-Accel-Buffering: no
```

#### 响应体格式

流式响应直接转发 Dify 工作流的 SSE 数据格式：

```
event: message
data: {"text": "分析开始..."}

event: message
data: {"text": "正在分析故障原因..."}

event: message
data: {"analysis": {"root_cause": "数据库连接池耗尽"}}

event: workflow_finished
data: {"status": "completed"}
```

#### 事件类型

事件类型由 Dify 工作流定义，常见的包括：

| 事件类型          | 说明             |
| ----------------- | ---------------- |
| message           | 分析过程中的消息 |
| workflow_started  | 工作流开始       |
| workflow_finished | 工作流完成       |
| node_started      | 节点开始执行     |
| node_finished     | 节点执行完成     |
| error             | 错误信息         |

### 阻塞式响应 (response_mode: "blocking")

```json
{
  "code": 20000,
  "data": {
    "analysis_result": {
      "root_cause": "数据库连接池耗尽",
      "impact_assessment": "影响用户登录和订单处理",
      "recommendations": ["增加数据库连接池大小", "优化数据库查询性能", "添加连接池监控"]
    }
  },
  "msg": "请求成功",
  "status": "ok"
}
```

## 使用示例

### JavaScript 流式调用示例

```javascript
async function analyzefault(faultData) {
  const response = await fetch('/api/dify/workflows/run/fault_report_analysis/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer your-token',
    },
    body: JSON.stringify({
      inputs: faultData,
      response_mode: 'streaming',
      user: 'current_user',
    }),
  })

  const reader = response.body.getReader()
  const decoder = new TextDecoder()

  while (true) {
    const { done, value } = await reader.read()
    if (done) break

    const chunk = decoder.decode(value)
    const lines = chunk.split('\n')

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        try {
          const data = JSON.parse(line.slice(6))
          console.log('Received:', data)

          // 处理不同类型的消息
          if (data.event === 'message') {
            updateUI(data.data)
          } else if (data.event === 'message_end') {
            console.log('Analysis completed')
          }
        } catch (e) {
          console.error('Parse error:', e)
        }
      }
    }
  }
}
```

### Python 流式调用示例

```python
import requests
import json

def analyze_fault_streaming(fault_data):
    url = '/api/dify/workflows/run/fault_report_analysis/'

    data = {
        'inputs': fault_data,
        'response_mode': 'streaming',
        'user': 'current_user'
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-token'
    }

    response = requests.post(url, json=data, headers=headers, stream=True)

    for line in response.iter_lines(decode_unicode=True):
        if line and line.startswith('data: '):
            try:
                data = json.loads(line[6:])
                print(f"Received: {data}")

                if data.get('event') == 'message_end':
                    print("Analysis completed")
                    break
            except json.JSONDecodeError:
                print(f"Parse error: {line}")
```

## 错误处理

### 错误响应格式

```json
{
  "code": -1,
  "data": "",
  "msg": "错误信息",
  "status": "error"
}
```

### 常见错误码

| 错误码 | 说明           |
| ------ | -------------- |
| 2002   | 认证失败       |
| 400    | 请求参数错误   |
| 405    | 请求方法不允许 |
| 500    | 服务器内部错误 |

## 配置说明

在 `llm/config.py` 中配置故障报告分析工作流：

```python
'fault_report_analysis': {
    'token': 'your-actual-dify-token',
    'description': '故障报告分析工作流，支持流式响应'
}
```

## 注意事项

1. 流式响应需要客户端支持 Server-Sent Events
2. 建议设置合适的超时时间（推荐 5 分钟）
3. 在生产环境中需要配置实际的 Dify token
4. 流式响应可能会被某些代理服务器缓冲，建议配置 `X-Accel-Buffering: no`
