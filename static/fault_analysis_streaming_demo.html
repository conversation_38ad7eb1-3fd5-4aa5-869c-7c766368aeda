<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>故障报告分析流式接口演示</title>
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        background: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
      }

      input,
      textarea,
      select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        box-sizing: border-box;
      }

      textarea {
        height: 100px;
        resize: vertical;
      }

      .button-group {
        display: flex;
        gap: 10px;
        margin: 20px 0;
      }

      button {
        padding: 12px 24px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        transition: background-color 0.3s;
      }

      .btn-primary {
        background-color: #007bff;
        color: white;
      }

      .btn-primary:hover {
        background-color: #0056b3;
      }

      .btn-secondary {
        background-color: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background-color: #545b62;
      }

      .btn-danger {
        background-color: #dc3545;
        color: white;
      }

      .btn-danger:hover {
        background-color: #c82333;
      }

      .result-container {
        margin-top: 30px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f8f9fa;
      }

      .result-header {
        background-color: #e9ecef;
        padding: 10px 15px;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
      }

      .result-content {
        padding: 15px;
        max-height: 400px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.4;
      }

      .message {
        margin-bottom: 10px;
        padding: 8px;
        border-radius: 4px;
      }

      .message.info {
        background-color: #d1ecf1;
        border-left: 4px solid #17a2b8;
      }

      .message.success {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
      }

      .message.error {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
      }

      .timestamp {
        color: #6c757d;
        font-size: 11px;
      }

      .status {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
      }

      .status.connecting {
        background-color: #fff3cd;
        color: #856404;
      }

      .status.streaming {
        background-color: #d1ecf1;
        color: #0c5460;
      }

      .status.completed {
        background-color: #d4edda;
        color: #155724;
      }

      .status.error {
        background-color: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>故障报告分析流式接口演示</h1>

      <form id="faultForm">
        <div class="form-group">
          <label for="faultDescription">故障描述 *</label>
          <textarea id="faultDescription" placeholder="请详细描述故障现象..." required>
数据库连接超时导致用户服务不可用，影响用户登录和订单处理功能</textarea
          >
        </div>

        <div class="form-group">
          <label for="faultTime">故障发生时间</label>
          <input type="datetime-local" id="faultTime" />
        </div>

        <div class="form-group">
          <label for="faultLevel">故障等级</label>
          <select id="faultLevel">
            <option value="">请选择</option>
            <option value="P1">P1 - 严重</option>
            <option value="P2" selected>P2 - 高</option>
            <option value="P3">P3 - 中</option>
            <option value="P4">P4 - 低</option>
            <option value="P5">P5 - 信息</option>
          </select>
        </div>

        <div class="form-group">
          <label for="affectedServices">受影响的服务（用逗号分隔）</label>
          <input
            type="text"
            id="affectedServices"
            placeholder="例如：用户服务,订单服务,支付服务"
            value="用户服务,订单服务"
          />
        </div>

        <div class="form-group">
          <label for="responseMode">响应模式</label>
          <select id="responseMode">
            <option value="streaming" selected>流式响应</option>
            <option value="blocking">阻塞式响应</option>
          </select>
        </div>

        <div class="button-group">
          <button type="submit" class="btn-primary">开始分析</button>
          <button type="button" id="stopBtn" class="btn-danger" disabled>停止分析</button>
          <button type="button" id="clearBtn" class="btn-secondary">清空结果</button>
        </div>
      </form>

      <div class="result-container">
        <div class="result-header">
          分析结果
          <span id="status" class="status">就绪</span>
        </div>
        <div class="result-content" id="resultContent">
          <div class="message info">
            <div class="timestamp">[系统] 准备就绪，请填写故障信息并点击"开始分析"</div>
          </div>
        </div>
      </div>
    </div>

    <script>
      let currentController = null

      document.getElementById('faultForm').addEventListener('submit', async function (e) {
        e.preventDefault()

        const faultDescription = document.getElementById('faultDescription').value
        const faultTime = document.getElementById('faultTime').value
        const faultLevel = document.getElementById('faultLevel').value
        const affectedServices = document
          .getElementById('affectedServices')
          .value.split(',')
          .map((s) => s.trim())
          .filter((s) => s)
        const responseMode = document.getElementById('responseMode').value

        if (!faultDescription) {
          alert('请填写故障描述')
          return
        }

        const data = {
          inputs: {
            fault_description: faultDescription,
            fault_time: faultTime,
            fault_level: faultLevel,
            affected_services: affectedServices,
          },
          response_mode: responseMode,
          user: 'demo_user',
        }

        if (responseMode === 'streaming') {
          await startStreamingAnalysis(data)
        } else {
          await startBlockingAnalysis(data)
        }
      })

      document.getElementById('stopBtn').addEventListener('click', function () {
        if (currentController) {
          currentController.abort()
          addMessage('系统', '分析已被用户停止', 'error')
          setStatus('error', '已停止')
          resetButtons()
        }
      })

      document.getElementById('clearBtn').addEventListener('click', function () {
        document.getElementById('resultContent').innerHTML =
          '<div class="message info"><div class="timestamp">[系统] 结果已清空</div></div>'
        setStatus('', '就绪')
      })

      async function startStreamingAnalysis(data) {
        setStatus('connecting', '连接中...')
        setButtons(true)

        currentController = new AbortController()

        try {
          const response = await fetch('/api/dify/workflows/run/fault_report_analysis/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer demo-token',
            },
            body: JSON.stringify(data),
            signal: currentController.signal,
          })

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }

          setStatus('streaming', '分析中...')
          addMessage('系统', '开始流式分析...', 'info')

          const reader = response.body.getReader()
          const decoder = new TextDecoder()

          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value)
            const lines = chunk.split('\n')

            let currentEvent = 'message' // 默认事件类型

            for (const line of lines) {
              if (line.startsWith('event: ')) {
                // 解析事件类型
                currentEvent = line.slice(7).trim()
              } else if (line.startsWith('data: ')) {
                try {
                  const eventData = JSON.parse(line.slice(6))
                  handleStreamingData(currentEvent, eventData)
                } catch (e) {
                  console.error('Parse error:', e)
                  addMessage('系统', `解析错误: ${line}`, 'error')
                }
              }
            }
          }

          setStatus('completed', '分析完成')
        } catch (error) {
          if (error.name === 'AbortError') {
            addMessage('系统', '分析被用户取消', 'error')
          } else {
            addMessage('系统', `错误: ${error.message}`, 'error')
            setStatus('error', '分析失败')
          }
        } finally {
          resetButtons()
          currentController = null
        }
      }

      async function startBlockingAnalysis(data) {
        setStatus('connecting', '分析中...')
        setButtons(true)

        try {
          const response = await fetch('/api/dify/workflows/run/fault_report_analysis/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer demo-token',
            },
            body: JSON.stringify(data),
          })

          const result = await response.json()

          if (result.code === 20000) {
            addMessage('系统', '分析完成', 'success')
            addMessage('结果', JSON.stringify(result.data, null, 2), 'info')
            setStatus('completed', '分析完成')
          } else {
            addMessage('系统', `分析失败: ${result.msg}`, 'error')
            setStatus('error', '分析失败')
          }
        } catch (error) {
          addMessage('系统', `错误: ${error.message}`, 'error')
          setStatus('error', '分析失败')
        } finally {
          resetButtons()
        }
      }

      function handleStreamingData(eventType, data) {
        // 处理 Dify 工作流的各种事件类型
        switch (eventType) {
          case 'message':
            if (data.text) {
              addMessage('AI', data.text, 'info')
            } else if (data.analysis) {
              addMessage('分析结果', JSON.stringify(data.analysis, null, 2), 'success')
            } else {
              addMessage('数据', JSON.stringify(data, null, 2), 'info')
            }
            break

          case 'workflow_started':
            addMessage('系统', '工作流开始执行', 'info')
            setStatus('streaming', '分析中...')
            break

          case 'workflow_finished':
            addMessage('系统', '工作流执行完成', 'success')
            setStatus('completed', '分析完成')
            break

          case 'node_started':
            if (data.node_name) {
              addMessage('系统', `节点开始: ${data.node_name}`, 'info')
            }
            break

          case 'node_finished':
            if (data.node_name) {
              addMessage('系统', `节点完成: ${data.node_name}`, 'info')
            }
            break

          case 'error':
            addMessage('错误', data.message || '未知错误', 'error')
            setStatus('error', '分析失败')
            break

          default:
            // 其他未知事件类型
            addMessage('事件', `${eventType}: ${JSON.stringify(data)}`, 'info')
        }
      }

      function addMessage(sender, content, type = 'info') {
        const resultContent = document.getElementById('resultContent')
        const timestamp = new Date().toLocaleTimeString()

        const messageDiv = document.createElement('div')
        messageDiv.className = `message ${type}`
        messageDiv.innerHTML = `
                <div class="timestamp">[${timestamp}] ${sender}</div>
                <div>${content}</div>
            `

        resultContent.appendChild(messageDiv)
        resultContent.scrollTop = resultContent.scrollHeight
      }

      function setStatus(className, text) {
        const statusElement = document.getElementById('status')
        statusElement.className = `status ${className}`
        statusElement.textContent = text
      }

      function setButtons(analyzing) {
        document.querySelector('button[type="submit"]').disabled = analyzing
        document.getElementById('stopBtn').disabled = !analyzing
      }

      function resetButtons() {
        setButtons(false)
      }

      // 设置默认时间为当前时间
      document.getElementById('faultTime').value = new Date().toISOString().slice(0, 16)
    </script>
  </body>
</html>
