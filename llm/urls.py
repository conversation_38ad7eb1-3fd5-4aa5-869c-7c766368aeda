"""fault_sys URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path
from .views import feedback_proxy_view, rectify_proxy_view, fault_report_analysis_view

urlpatterns = [
    path('dify/workflows/run/extract_feedback_info/', feedback_proxy_view, name='feedback_proxy_view_empty_path'),
    path('dify/workflows/run/extract_feedback_info/<path:path>', feedback_proxy_view, name='feedback_proxy_view'),
    path('dify/workflows/run/extract_rectify_info/', rectify_proxy_view, name='rectify_proxy_view_empty_path'),
    path('dify/workflows/run/extract_rectify_info/<path:path>', rectify_proxy_view, name='rectify_proxy_view'),
    path('dify/workflows/run/fault_report_analysis/', fault_report_analysis_view, name='fault_report_analysis_empty_path'),
    path('dify/workflows/run/fault_report_analysis/<path:path>', fault_report_analysis_view, name='fault_report_analysis_view'),
]