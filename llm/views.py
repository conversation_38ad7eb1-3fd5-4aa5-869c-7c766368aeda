import json
from .funcs import my_authentication


import requests
from django.http import HttpResponse, JsonResponse
# Create your views here.
from common.utils.log import Logger
from common.utils.config import itmp_conf

set_log = Logger.get_logger(__name__, 3)

from common.utils.utils import return_code
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from django.http import StreamingHttpResponse


@csrf_exempt
def feedback_proxy_view(request, path=""):

    set_log.info("proxy_view")
    # 鉴权
    user, code = my_authentication.MyTokenAuthentication_by_hand(request)
    if code != 20000:
        return HttpResponse(return_code(code, interface="proxy_view"))
    # 目标 URL
    target_url = f'http://office.dev.com/v1/workflows/run'
    
    # 代理配置
    proxies = {
        'http': 'http://***********',
        #'https': 'http://***********'
    }
    
    # 转发请求头
    #headers = {key: value for (key, value) in request.headers.items() if key != 'Host'}
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer app-Ju3NyvzOzWcAiNz2rs8Awqtu',
    }
    set_log.info(f"[headers] {headers} [target_url] {target_url} [proxies] {proxies} [method] {request.method}")
    try:
        if request.method == 'GET':
            response = requests.get(target_url, headers=headers, proxies=proxies)
        elif request.method == 'POST':
            response = requests.post(target_url, headers=headers, data=request.body, proxies=proxies)
        elif request.method == 'PUT':
            response = requests.put(target_url, headers=headers, data=request.body, proxies=proxies)
        elif request.method == 'DELETE':
            response = requests.delete(target_url, headers=headers, proxies=proxies)
        else:
            return HttpResponse(status=405)
        set_log.info(f"[LLM] [code]{response.status_code} [data]{response.text} [header]{response.headers}")
        # 创建 Django 响应对象
        if response.status_code == 200:
            data = {
                "code": 20000,
                "data": response.json(),
                "msg": "请求成功",
                "status": "ok"
            }
            django_response = JsonResponse(data=data, status=response.status_code)
            set_log.info(f"[data] {data}")
        else:
            data = {
                "code": -1,
                "data": "",
                "msg": "请求失败",
                "status": "error"
            }
            django_response = JsonResponse(data=data, status=response.status_code)
            set_log.info(f"[data] {data}")
        #django_response = HttpResponse(response.content, status=response.status_code)
        # 手动设置 Content-Length 头
        
        # 复制响应头
        for key, value in response.headers.items():
            if key not in ["Connection", "Transfer-Encoding"]:
                django_response[key] = value
        django_response['Content-Length'] = len(django_response.content)
        return django_response
    
    except requests.RequestException as e:
        set_log.info(f"Proxy error: {str(e)}")
        return HttpResponse(f"Proxy error: {str(e)}", status=500)

@csrf_exempt
def rectify_proxy_view(request, path=""):

    set_log.info("proxy_view")
    # 鉴权
    user, code = my_authentication.MyTokenAuthentication_by_hand(request)
    if code != 20000:
        return HttpResponse(return_code(code, interface="proxy_view"))
    # 目标 URL
    target_url = f'http://office.dev.com/v1/workflows/run'
    
    # 代理配置
    proxies = {
        'http': 'http://***********',
        #'https': 'http://***********'
    }
    
    # 转发请求头
    #headers = {key: value for (key, value) in request.headers.items() if key != 'Host'}
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer app-OnNr2o1Rj9jMIl78SOmTlQKy',
    }
    set_log.info(f"[headers] {headers} [target_url] {target_url} [proxies] {proxies} [method] {request.method}")
    try:
        if request.method == 'GET':
            response = requests.get(target_url, headers=headers, proxies=proxies)
        elif request.method == 'POST':
            response = requests.post(target_url, headers=headers, data=request.body, proxies=proxies)
        elif request.method == 'PUT':
            response = requests.put(target_url, headers=headers, data=request.body, proxies=proxies)
        elif request.method == 'DELETE':
            response = requests.delete(target_url, headers=headers, proxies=proxies)
        else:
            return HttpResponse(status=405)
        set_log.info(f"[LLM] [code]{response.status_code} [data]{response.text} [header]{response.headers}")
        # 创建 Django 响应对象
        if response.status_code == 200:
            data = {
                "code": 20000,
                "data": response.json(),
                "msg": "请求成功",
                "status": "ok"
            }
            django_response = JsonResponse(data=data, status=response.status_code)
            set_log.info(f"[data] {data}")
        else:
            data = {
                "code": -1,
                "data": "",
                "msg": "请求失败",
                "status": "error"
            }
            django_response = JsonResponse(data=data, status=response.status_code)
            set_log.info(f"[data] {data}")
        #django_response = HttpResponse(response.content, status=response.status_code)
        # 手动设置 Content-Length 头
        
        # 复制响应头
        for key, value in response.headers.items():
            if key not in ["Connection", "Transfer-Encoding"]:
                django_response[key] = value
        django_response['Content-Length'] = len(django_response.content)
        return django_response
    
    except requests.RequestException as e:
        set_log.info(f"Proxy error: {str(e)}")
        return HttpResponse(f"Proxy error: {str(e)}", status=500)       

@csrf_exempt
def fault_report_analysis_view(request, path=""):

    set_log.info("proxy_view")
    # 鉴权
    user, code = my_authentication.MyTokenAuthentication_by_hand(request)
    if code != 20000:
        return HttpResponse(return_code(code, interface="proxy_view"))
    # 目标 URL
    target_url = f'http://office.dev.com/v1/chat-messages'
    
    # 代理配置
    proxies = {
        'http': 'http://***********',
        #'https': 'http://***********'
    }
    
    # 转发请求头
    #headers = {key: value for (key, value) in request.headers.items() if key != 'Host'}
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer app-TCabBJz2iX0xspXZd4Qc3fwA',
    }
    set_log.info(f"[headers] {headers} [target_url] {target_url} [proxies] {proxies} [method] {request.method}")
    def stream_generator():
        try:
            with requests.post(target_url, headers=headers, data=request.body,
                             proxies=proxies, stream=True) as response:
                set_log.info(f"[Streaming] Response status: {response.status_code}")
                # 直接转发所有 SSE 数据，不判断状态码
                for line in response.iter_lines(decode_unicode=True):
                    if line:
                        set_log.debug(f"[Streaming] Received line: {line}")
                        # 直接转发 Dify 的 SSE 格式数据
                        if line.startswith('data: ') or line.startswith('event: '):
                            yield f"{line}\n".encode('utf-8')
                        elif line.strip() == '':
                            # 转发空行（SSE 消息分隔符）
                            yield "\n".encode('utf-8')
                        else:
                            # 其他格式的数据，包装成 SSE 格式
                            try:
                                data = json.loads(line)
                                yield f"data: {json.dumps(data)}\n\n".encode('utf-8')
                            except json.JSONDecodeError:
                                # 如果不是 JSON，直接作为文本发送
                                yield f"data: {json.dumps({'text': line})}\n\n".encode('utf-8')
        except requests.RequestException as e:
            set_log.error(f"Streaming error: {str(e)}")
            # 发送错误事件
            yield f"event: error\n".encode('utf-8')
            yield f"data: {json.dumps({'message': f'Streaming error: {str(e)}'})}\n\n".encode('utf-8')
    response = StreamingHttpResponse(
        stream_generator(),
        content_type='text/event-stream'
    )
    response['Cache-Control'] = 'no-cache'
#    response['Connection'] = 'keep-alive'
    response['Content-Type'] = 'text/event-stream'
    response['X-Accel-Buffering'] = 'no'  # 禁用 nginx 缓冲
    set_log.info(f"[Response] Final Content-Type: {response['Content-Type']}") 
    return response        
        #django_response = HttpResponse(response.content, status=response.status_code)
        # 手动设置 Content-Length 头
        
        # 复制响应头
        # for key, value in response.headers.items():
        #     if key not in ["Connection", "Transfer-Encoding"]:
        #         django_response[key] = value
        # django_response['Content-Length'] = len(django_response.content)
    #     return django_response
    
    # except requests.RequestException as e:
    #     set_log.info(f"Proxy error: {str(e)}")
    #     return HttpResponse(f"Proxy error: {str(e)}", status=500)               