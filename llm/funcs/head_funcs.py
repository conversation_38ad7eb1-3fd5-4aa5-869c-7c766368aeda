import copy
import json
import time

import requests
from django.db.models import Q
from ..models import *
from ..serializers import *
from common.utils.log import Logger
from common.utils.exception import send_exception
set_log = Logger.get_logger(__name__, 0)

# sel
def my_selectHead(request):
    # cookie = get_cookie(request)
    # operator = UserInfo.objects.get(userid=cookie['UserID']).name
    # 获取请求参数
    # data = {"page": False, "limit": False}
    json_field = ['devOwner', 'clientOwner', 'OperationsOwner']
    frontend_data = request.data
    set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")


    try:
        page = frontend_data.get('page', 1)
        limit = frontend_data.get('limit', 10)
        # 分页参数错误
        set_log.debug(f"分页参数：page - {page}, limit - {limit}")
        search = dict()
        search_by_hand = Q()


        for i, j in frontend_data.get('query', {}).items():
            set_log.debug(f"[one search] {i} {j} {type(j)}")

            # 精确搜索
            # if
            # search[f'{i}__in'] = j
            if j != []:
                # 模糊搜索
                if i == 'search':
                    if j == '':
                        continue
                    # Q(id__icontains=j) |
                    search_by_hand = Q(firstLevelCategory__icontains=j) | Q(secondLevelCategory__icontains=j) | \
                                     Q(thirdLevelCategory__icontains=j) | Q(devOwner__icontains=j) | Q(clientOwner__icontains=j) | \
                                     Q(OperationsOwner__icontains=j)

                # json 格式
                elif i in json_field:
                    search_by_hand.connector = 'OR'
                    search_by_hand.children.append((f"{i}__icontains", j))
                    # search_by_hand.OR({temp}="11")
                # elif i == 'stage':
                #     search[f'{i}__in'] = j
                else:
                    search[f'{i}__in'] = j
        set_log.debug(f"查询参数：{search} {search_by_hand} {len(search_by_hand)}")

        func_start_time = time.time()

        # 无参数
        if len(search) == 0 and len(search_by_hand) == 0:
            # func_start_time = time.time()
            data = ResponsiblePersonsInfo.objects.all().order_by('-id')
            total = data.count()
            data = ResponsiblePersonsSerializers(data[:10], many=True).data

        elif len(search) > 0 and len(search_by_hand) == 0:
            data = ResponsiblePersonsInfo.objects.filter(**search).order_by('-id')
            total = data.count()
            data = ResponsiblePersonsSerializers(data[int(page)*limit-limit:int(page)*limit], many=True).data
        else:
            # search_by_hand.children.append(filter_Q)
            set_log.debug(f"[search_by_hand] {search_by_hand}")
            data = ResponsiblePersonsInfo.objects.filter(**search).filter(search_by_hand).order_by('-id')
            total = data.count()
            data = ResponsiblePersonsSerializers(data[int(page)*limit-limit:int(page)*limit], many=True).data
        # total = len(alerts)

        set_log.info(f"[sel data] SUCCESS")
        func_end_time = time.time()
        set_log.info(f"[查询 耗时] {func_end_time - func_start_time:.2f} seconds")
        # 时间格式转换
        # data = timeserializers(data)
        set_log.debug(f"分页后数据长度：{len(data)}")

        # 查询为空
        if data == []:
            set_log.error(f"查询结果为空：{data}")
            # return data, count, total
        return data, total
    except Exception as e:
        set_log.error(f"error : {e}")
        return -1, 0

# dashboard = get_dashboard(json.loads(fault[0]), json.loads(rectify), mttf_times_count(fault[-1]))
# set_log.info(f"[dashboard] {dashboard}")


def get_category_tree(biz):
    # cookie = get_cookie(request)
    # http://192.168.22.17:5000/
    # http://yw-inner-cmdb.ttyuyin.com:5000/
    # url = "http://dev-yw-cmdb.ttyuyin.com/api/jsonrpc/business_label_tree"
    url = "https://yw-cmdb.ttyuyin.com/api/jsonrpc/"
    headers = {"Authorization": "NOLHTYVeHEdM3fIA4MLjzll2s1FWvdbsFfhNKGuGKUSzZYIWckSIdxV3NQ8fyKUc"}
    data = {
        "id": 0,
        "jsonrpc": "2.0",
        "method": "ui_get_app_function_select",
        "params": {
            "type": "function",
            "function_biz": biz
    }
    }
    ret_data = requests.post(url=url, json=data, headers=headers)
    set_log.debug(f"ret_data {ret_data}")
    # set_log.debug(f"{ret_data.json()}")
    ret_data = ret_data.json()['result']['function']
    return ret_data


def get_category_treeTest(request):
    try:
        # cookie = get_cookie(request)
        # 获取请求参数
        frontend_data = request.data
        ret_data = []
        set_log.info(f"[ret_data] {type(ret_data)} {len(ret_data)}")
        return ret_data
    except Exception as e:
        send_exception(-1, f"\n[DEF] get_category_tree\n[ERROR] {e}")
        set_log.error(f"{e}")


def get_OpenID(request):
    frontend_data = request.data
    set_log.debug(f"frontend data:{frontend_data} {type(frontend_data)}")
    # name_list = frontend_data['name']
    # email_url = "http://fmp.ttyuyin.com/api/V2/scheduler/email/"
    # id_url = "http://yw-jarvis.ttyuyin.com/api_V1/query/lark_id/"
    # headers = {
    #     'Authorization': 'EB3xwAfrZwz.262VbIioi755+oYP#4t39RF_0+NR3BWzeGM0Cc7HvWDeT_50f5i+tzvu9ZU$qXaj8LoV0gV2JhHQ9WmRTK9LnKuu#Q4ZYbHm7krnyCmrV&fIv2ScElTz',
    #     'Content-Type': 'application/json'
    # }
    # # test = requests.post(url=email_url, data=name_list).json()
    # # set_log.info(f"[test] {test}")
    #
    # for i in name_list:
    #     try:
    #         set_log.debug(f"-"*50)
    #         set_log.info(f"[name] {i}")
    #         data = {'user_list': [i]}
    #         set_log.info(f"[user_list] {data}")
    #         email = requests.post(url=email_url, data=data).json()['data'][0]['user_email']
    #         set_log.info(f"[email] {email}")
    #         id_list = requests.post(url=id_url, data=json.dumps({"mails": [email], "id_type": "open_id"}), headers=headers)
    #         set_log.info(f"[res] {id_list.json()}")
    #         user_id = id_list.json()['lark_id_list'][0][email]
    #         set_log.info(f"[user_id] {user_id}")
    #         temp = PersonsInitInfo(email=email, user_id=user_id, user_name=i)
    #         temp.save()
    #         set_log.info(f"{i} {email} {user_id} 创建成功 ")
    #
    #     except Exception as e:
    #         set_log.error(f"{e}")
    #         set_log.info(f"{i} 创建失败 ")
    #         continue

    data = 'SUCESS'
    return data

def get_email_openid(name_list):
    email_url = "http://fmp.ttyuyin.com/api/V2/scheduler/email/"
    id_url = "http://yw-jarvis.ttyuyin.com/api_V1/query/lark_id/"
    headers = {
        'Authorization': 'EB3xwAfrZwz.262VbIioi755+oYP#4t39RF_0+NR3BWzeGM0Cc7HvWDeT_50f5i+tzvu9ZU$qXaj8LoV0gV2JhHQ9WmRTK9LnKuu#Q4ZYbHm7krnyCmrV&fIv2ScElTz',
        'Content-Type': 'application/json'
    }
    # 获取邮箱
    email_list = requests.post(url=email_url, data=name_list).json()['data']
    set_log.debug(f"[user_email] {email_list}")
    email_list = [k['user_email'] for k in email_list]
    # 获取open_id
    data = {"mails": email_list, "id_type": "open_id"}
    set_log.info(f"[post data] {data}")
    id_list = requests.post(id_url, data=json.dumps(data), headers=headers)
    set_log.info(f"[res] {id_list.json()}")
    id_list = id_list.json()['lark_id_list']
    set_log.info(f"[get_openid_list] {id_list}")
    return id_list

def get_dashboard(request):
    dashboard = {"test": "test"}
    return dashboard


#
# def get_cookie(request):
#     print("-" * 40)
#     set_log.info("Here is get frontend cookie")
#     cookie = request.META.get("HTTP_COOKIE")
#     temp = cookie.split('; ')
#     cookie = dict()
#     for i in temp:
#         i = i.split('=')
#         cookie[i[0]] = i[1]
#     cookie['Authorization'] = cookie['Authorization'].replace('%20', ' ')
#     set_log.debug(f"frontend cookie: {cookie}")
#     return cookie





